# docker/api/supervisord.conf
[unix_http_server]
file=/var/run/supervisor.sock

[supervisord]
logfile=/var/log/supervisord.log
pidfile=/var/run/supervisord.pid
nodaemon=true

[program:php-fpm]
command=php-fpm
autostart=true
autorestart=true
priority=5

[program:queue-worker]
command=php /app/artisan queue:work --sleep=3 --tries=3
autostart=true
autorestart=true
numprocs=1
process_name=%(program_name)s_%(process_num)02d