FROM php:8.2-fpm

# タイムゾーンの設定
ENV TZ Asia/Tokyo

# パッケージのインストール
RUN apt-get update && \
    apt-get install -y \
        git \
        unzip \
        libzip-dev \
        libicu-dev \
        libonig-dev \
        libwebp-dev \
        libpng-dev \
        libjpeg-dev \
        libmagickwand-dev \
        imagemagick \
        supervisor \
        ffmpeg \
        && \
    rm -rf /var/lib/apt/lists/* && \
    # GDのインストール、JPEG, PNG, WebPサポート付き
    docker-php-ext-configure gd --with-jpeg --with-webp --with-freetype && \
    docker-php-ext-install -j$(nproc) gd && \
    # Imagickのインストール
    pecl install imagick && \
    docker-php-ext-enable imagick && \
    # その他のPHP拡張機能
    docker-php-ext-install intl pdo_mysql zip bcmath

# Supervisorの設定ファイルをコピー
COPY ./docker/api/supervisord.conf /etc/supervisor/supervisord.conf

# Supervisorを起動
CMD ["/usr/bin/supervisord"]

COPY ./docker/api/php.ini /usr/local/etc/php/php.ini
COPY --from=composer:2.0 /usr/bin/composer /usr/bin/composer

WORKDIR /app

# # Copy source code
COPY src .
RUN composer install