version: "3.9"

services:
  # php
  api:
    build:
      context: .
      dockerfile: ./docker/api/Dockerfile
    volumes:
      - ./src/:/app
      - ./docker/api/php.ini:/usr/local/etc/php/php.ini
    env_file: .env
    environment:
      APP_ENV: local
      APP_DEBUG: true
      APP_KEY: base64:6NyVWMe4EmpcZQNpzpd+qJRBkWyTxqTRHu3SsBqK5eE=
      # LOG_CHANNEL: stderr
      LOG_LEVEL: debug
      REDIS_HOST: 127.0.0.1
      REDIS_PASSWORD: null
      REDIS_PORT: 6379
      DB_CONNECTION: mysql
      DB_HOST: db
      DB_PORT: 3306
      DB_DATABASE: database
      DB_USERNAME: root
      DB_PASSWORD: password
      MAIL_MAILER: smtp
      MAIL_HOST: mailpit
      MAIL_PORT: 1025
      MAIL_USERNAME:
      MAIL_PASSWORD:
      MAIL_ENCRYPTION:
      MAIL_FROM_ADDRESS: "<EMAIL>"
      MAIL_FROM_NAME: "Webiner Local"
      LOG_SQL_ENABLE: true
    networks:
      - favori
    depends_on:
      - web
      - db
      - redis
      - mailpit
      - minio
  web:
    build:
      context: .
      dockerfile: ./docker/web/Dockerfile
    ports:
      - 80:80
    volumes:
      - ./src/:/app
    networks:
      - favori

  # MySQL
  db:
    platform: linux/x86_64
    image: mysql:8.0.26
    build:
      context: .
      dockerfile: ./docker/db/Dockerfile
    command: mysqld  --innodb_use_native_aio=0 --character-set-server=utf8mb4 --collation-server=utf8mb4_0900_ai_ci
    ports:
      - 3306:3306
    environment:
      MYSQL_DATABASE: database
      MYSQL_USER: user
      MYSQL_PASSWORD: password
      MYSQL_ROOT_PASSWORD: password
      MYSQL_ROOT_HOST: "%"
      TZ: "Asia/Tokyo"
    volumes:
      - mysql-volume:/var/lib/mysql
    healthcheck:
      test:
        - CMD
        - mysqladmin
        - ping
        - "-proot"
      retries: 3
      timeout: 5s
    networks:
      - favori
  schemaspy:
    build:
      context: .
      dockerfile: ./docker/schemaspy/Dockerfile
    entrypoint: /bin/sh
    tty: true
    stdin_open: true
    platform: linux/x86_64
    volumes:
      - ./docs/schema:/output
    networks:
      - favori
    depends_on:
      db:
        condition: service_healthy
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    environment:
      - PMA_ARBITRARY=1
      - PMA_HOSTS=db
      - PMA_USER=user
      - PMA_PASSWORD=password
    platform: linux/x86_64
    ports:
      - 4040:80
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - ./docker/phpmyadmin/sessions:/sessions
    networks:
      - favori
  redis:
    image: redis:alpine
    ports:
      - 6379:6379
    volumes:
      - redis-volume:/data
    healthcheck:
      test:
        - CMD
        - redis-cli
        - ping
      retries: 3
      timeout: 5s
    networks:
      - favori
  mailpit:
    image: axllent/mailpit:latest
    ports:
      - 1025:1025
      - 8025:8025
    networks:
      - favori
  minio:
    image: quay.io/minio/minio
    container_name: minio
    ports:
      - 9000:9001
      - 9001:9002
    networks:
      - favori
    environment:
      - MINIO_ROOT_USER=user
      - MINIO_ROOT_PASSWORD=password
    command: server /export --address :9001 --console-address :9002
    volumes:
      - ./docker/minio/data:/export
networks:
  favori:
    driver: bridge
volumes:
  mysql-volume:
    driver: local
  redis-volume:
    driver: local
