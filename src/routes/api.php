<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\Admin\MoneyTransferController;
use App\Http\Controllers\Api\Admin\GuestPaymentController;
use App\Http\Controllers\Api\GuestController;
use App\Http\Controllers\CsvDownloadController;
use App\Http\Controllers\Api\InstagramImageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});

// /auth/google/url or /auth/line/url
Route::get('/auth/{sns}/url', 'App\Http\Controllers\Auth\SocialAuthController@redirect');

// /auth/google/callback or /auth/line/callback
Route::get('/auth/{sns}/callback', 'App\Http\Controllers\Auth\SocialAuthController@handleCallback');

// API ルートグループ
Route::middleware(['auth:sanctum'])->group(function () {
    // CSVエクスポート
    Route::post('/guest/export', [GuestController::class, 'export']);
    // 既存システム ゲストリスト : ①宛名リスト
    Route::post('/guest/export/address', [GuestController::class, 'exportAddress']);
    // 既存システム ゲストリスト : ②席次表ゲスト
    Route::post('/guest/export/seating', [GuestController::class, 'exportSeating']);

    // 管理者用のプレフィックス付きルート
    Route::prefix('admin')->group(function () {

        //送金管理CSVダウンロード
        Route::post('/money-transfer/export', [MoneyTransferController::class, 'export'])->name('money-transfer.export');

        //ゲスト決済データダウンロード
        Route::post('/guest-payment/export', [GuestPaymentController::class, 'export'])->name('guest-payment.export');
    });

    // Instagram画像生成API
    Route::post('/instagram/generate', [InstagramImageController::class, 'generate']);
});
