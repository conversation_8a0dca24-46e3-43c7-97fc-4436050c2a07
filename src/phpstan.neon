includes:
    - ./vendor/nunomaduro/larastan/extension.neon
    - ./vendor/timeweb/phpstan-enum/extension.neon

parameters:
  stubFiles:
    - vendor/nuwave/lighthouse/_ide_helper.php

  paths:
    - app
    - bootstrap
    # - config
    #- database
    #- resources/views
    #- routes

  # The level 9 is the highest level
  level: 5

  #ignoreErrors:
  #    - '#Unsafe usage of new static#'

  #excludePaths:
  #    - ./*/*/FileToBeExcluded.php

  checkMissingIterableValueType: false
  treatPhpDocTypesAsCertain: false