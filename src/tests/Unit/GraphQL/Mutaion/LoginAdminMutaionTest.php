<?php

namespace Tests\Unit\GraphQL\Mutaion;

use Tests\TestCase;
use App\Models\Admin;

class LoginAdminMutaionTest extends TestCase
{
    /**
     * ログインに成功することを確認する
     */
    public function test_login_admin()
    {
        $password = 'password';
        $admin = Admin::factory()->create([
            "password" => $password,
        ]);

        $response = $this->graphQL(/** @lang GraphQL */ '
          mutation Login($email: String!, $password: String!) {
              loginAdmin(email: $email, password: $password)
          }
        ', [
            "email" => $admin->email,
            "password" => $password,
        ]);

        $token = $response->json("data.loginAdmin");
        $this->assertNotNull($token);
        // TODO: tokenの検証
    }

    /**
     * ログインに失敗することを確認する
     */
    public function test_login_admin_failer()
    {
        $password = 'password';
        $admin = Admin::factory()->create([
            "password" => $password,
        ]);

        $response = $this->graphQL(/** @lang GraphQL */ '
          mutation Login($email: String!, $password: String!) {
              loginAdmin(email: $email, password: $password)
          }
        ', [
            "email" => $admin->email,
            "password" => "aaaa",
        ]);

       $response->assertJson([
           "errors" => [
               [
                   "message" => "メールアドレスもしくは、パスワードが違います。",
               ],
           ],
       ]);
    }
}
