<?php

namespace Tests\Unit\GraphQL\Mutaion;

use Tests\TestCase;
use App\Models\Member;

class LoginMemberMutaionTest extends TestCase
{
    /**
     * ログインに成功することを確認する
     */
    public function test_login_Member()
    {
        $password = 'password';
        $Member = Member::factory()->create([
            "password" => $password,
        ]);

        $response = $this->graphQL(/** @lang GraphQL */ '
          mutation Login($email: String!, $password: String!) {
              loginMember(email: $email, password: $password)
          }
        ', [
            "email" => $Member->email,
            "password" => $password,
        ]);

        $token = $response->json("data.loginMember");
        $this->assertNotNull($token);
        // TODO: tokenの検証
    }

    /**
     * ログインに失敗することを確認する
     */
    public function test_login_Member_failer()
    {
        $password = 'password';
        $Member = Member::factory()->create([
            "password" => $password,
        ]);

        $response = $this->graphQL(/** @lang GraphQL */ '
          mutation Login($email: String!, $password: String!) {
              loginMember(email: $email, password: $password)
          }
        ', [
            "email" => $Member->email,
            "password" => "aaaa",
        ]);

       $response->assertJson([
           "errors" => [
               [
                   "message" => "メールアドレスもしくは、パスワードが違います。",
               ],
           ],
       ]);
    }
}
