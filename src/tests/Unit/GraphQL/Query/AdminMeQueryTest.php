<?php

namespace Tests\Unit\GraphQL\Query;

use Tests\TestCase;
use App\Models\Admin;
use App\Models\Member;

class AdminMeQueryTest extends TestCase
{
    /**
     * A basic test example.
     *
     * @return void
     */
    public function test_query_one_success()
    {
        $admin= Admin::factory()->create();
        $token = $admin->createAuthToken();

        $response = $this->graphQL(/** @lang GraphQL */ '
          query {
            adminMe {
              id
              name
              email
            }
          }
        ',
        [],
        [],
        [
          "Authorization" => "Bearer $token",
        ]);
        $response->assertJson([
          "data" => [
            "adminMe" => [
              "id" => $admin->id,
              "name" => $admin->name,
              "email" => $admin->email,
            ],
          ],
        ]);
    }

    /**
     * A basic test example.
     *
     * @return void
     */
    public function test_query_one_other_ability()
    {
        $member= Member::factory()->create();
        $token = $member->createAuthToken();

        $response = $this->graphQL(/** @lang GraphQL */ '
          query {
            adminMe {
              id
              name
              email
            }
          }
        ',
        [],
        [],
        [
          "Authorization" => "Bearer $token",
        ]);
        $this->assertSame($response->json("errors.0.message"), "Unauthenticated.");
    }

    /**
     * A basic test example.
     *
     * @return void
     */
    public function test_query_one_auth_no_token()
    {

        $admin= Admin::factory()->create();

        $response = $this->graphQL(/** @lang GraphQL */ '
            query {
              adminMe{
                id
                name
                email
              }
            }
        ', [
            "id" => $admin->id,
        ],
        [],
        []);
        $this->assertSame($response->json("errors.0.message"), "Unauthenticated.");
    }
}