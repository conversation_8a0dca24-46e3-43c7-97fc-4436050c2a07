<?php

namespace Tests\Unit\Models;

use Tests\TestCase;
use App\Models\Admin;

class AdminTest extends TestCase
{
    /**
     * Admin#valid_password
     *
     * @return void
     */
    public function test_valid_password()
    {
      $password = "password";
      $admin= Admin::factory()->create([
        "password" => $password,
      ]);
      $this->assertTrue($admin->validPassword($password), "パスワードが正常に認証されこと");
      $this->assertFalse($admin->validPassword('aaa'), "間違ったパスワードが認証されないこと");
    }
}
