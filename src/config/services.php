<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_CALLBACK_URL'),
    ],
    'line' => [
        'client_id' => env('LINE_CLIENT_ID'),
        'client_secret' => env('LINE_CLIENT_SECRET'),
        'redirect' => env('LINE_CALLBACK_URL'),
    ],
    'rakuten_payment' => [
        'api_endpoint' => env('RAKUTEN_PAYMENT_API_ENDPOINT'),
        'service_id' => env('RAKUTEN_PAYMENT_SERVICE_ID'),
        'secret_key' => env('RAKUTEN_PAYMENT_SECRET_KEY'),
    ],
    'gmp_pg' => [
        'api_endpoint' => env('GMO_PG_API_ENDPOINT', 'https://pt01.mul-pay.jp/'),
        'shop_id' => env('GMO_PG_SHOP_ID', 'tshop00024104'),
        'shop_pass' => env('GMO_PG_SHOP_PASS', '35yzuzh6')
    ],

    'cloud_front' => [
        'key_pair_id' => env('AWS_CLOUDFRONT_KEY_PAIR_ID', ''),
        'private_key_path' => env('AWS_CLOUDFRONT_PRIVATE_KEY_PATH', ''),
        'resource_url' => env('AWS_CLOUDFRONT_RESOURCE_URL', ''),
        'download_url' => env('AWS_CLOUDFRONT_DOWNLOAD_URL', ''),
        'expiry' => env('CLOUDFRONT_COOKIE_EXPIRY', ''),
        'domain' => env('AWS_CLOUDFRONT_COOKIE_DOMAIN', ''),
        'secure' => env('AWS_CLOUDFRONT_COOKIE_SECURE', ''),
    ],
    'media_convert' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => 'us-east-1',
        'name_decorator' => env('AWS_MEDIACONVERT_DECORATOR', ''),
        'aws_mediaconvert_decorator_role' => env('AWS_MEDIACONVERT_DECORATOR_ROLE', ''),

    ],
    'api_key' => env('API_KEY'),
];
