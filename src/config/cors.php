<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    'paths' => ['api/*', 'sanctum/csrf-cookie', 'graphql'],

    // 'allowed_methods' => ['*'],
    'allowed_methods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],


    'allowed_origins' => [
        'http://localhost:3000',
        'http://localhost:3001',
        'https://dev8695.favori.wedding',
        'https://dev8695-alb.favori.wedding',
        'https://stg8695.favori.wedding',
        'https://stg8695-api.favori.wedding',
        'https://favori.wedding',
        'https://api.favori.wedding',

        // favori-cloudとの連携のあるquery
        // 呼び出しquery guests ゲスト一覧
        'https://www.favori-cloud.com', // favori-cloud 本番
        'https://base-dev.bizright.co.jp', // favori-cloud ステージング
    ],
    // 'allowed_origins' => ['*'],

    'allowed_origins_patterns' => [],

    'allowed_headers' => ['*'],

    'exposed_headers' => ['Content-Disposition'],
  
    'max_age' => 86400,

    'supports_credentials' => true,
    // 'supports_credentials' => false,
];
