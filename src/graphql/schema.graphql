"A datetime string with format `Y-m-d H:i:s`, e.g. `2018-01-01 13:00:00`."
scalar DateTime
    @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\DateTime")

"A date string with format `Y-m-d`, e.g. `2011-05-23`."
scalar Date
    @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\Date")

# scalar Time @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\Time")

scalar Json
    @scalar(class: "App\\GraphQL\\Scalars\\Json")

scalar Upload
    @scalar(class: "Nuwave\\Lighthouse\\Schema\\Types\\Scalars\\Upload")

type Query

type Mutation

#import types/*.graphql
#import types/not_model/*.graphql
#import inputs/*.graphql
#import mutations/*.graphql
#import queries/*.graphql

#admin
#import admin/inputs/*/*.graphql
#import admin/queries/*.graphql
#import admin/mutations/*.graphql


#app
#import app/inputs/*/*.graphql
#import app/mutations/*.graphql
#import app/queries/*.graphql

