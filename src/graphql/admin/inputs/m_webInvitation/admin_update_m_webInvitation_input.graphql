# WEB招待状マスタ
input AdminUpdateMWebInvitationInput {
    id: ID

    "規格別商品マスタ"
    m_specification_product_id: ID

    "WEB招待状テンプレートマスタ"
    m_web_invitation_template_id: ID

    "Web招待状メインビジュアルブロックマスタ"
    m_web_invitation_visual_block_id: ID

    "選択ファーストビュー"
    first_view_id: String

    "メインビジュアル画像利用不可FLG"
    is_main_visual_image_disabled: Boolean

    "プロフィール画像利用不可FLG"
    is_profile_image_disabled: Boolean

    "メインビジュアル画像差し替え可能FLG"
    is_main_visual_image_replaceable: Boolean

    "CSSコード"
    css_code: String

    "エディタ設定値JSON"
    editor_settings_json: Json

    "画像アスペクト比設定値JSON"
    image_aspect_settings_json: Json

    "WEB招待状デザイン画像"
    web_invitation_design_images: [AdminUpdateWebInvitationDesignImageInput!]
}
