#管理者登録引数
input AdminCreateAdminInput @validator(class: "AdminAdminInputValidator") {
    "管理者名"
    name: String!
        @rules(
            apply: ["max:255"]
            attribute: "管理者名"
        )

    "メールアドレス"
    email: String!
        @rules(
            apply: ["max:255", "email:filter"]
            attribute: "メールアドレス"

        )

    "パスワード"
    password: String!
        @rules(
            apply: ["max:255"]
            attribute: "パスワード"
        )
}
