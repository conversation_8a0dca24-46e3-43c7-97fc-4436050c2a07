#管理者更新引数
input AdminUpdateAdminInput @validator(class: "AdminAdminInputValidator") {

    id: ID!

    "管理者名"
    name: String
        @rules(
            apply: ["required", "max:255"]
            attribute: "管理者名"
        )

    "メールアドレス"
    email: String!
        @rules(
            apply: ["required", "max:255", "email:filter"]
            attribute: "メールアドレス"

        )

    "パスワード"
    password: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "パスワード"
        )
}
