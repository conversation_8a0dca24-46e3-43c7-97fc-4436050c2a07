# 送金更新
input AdminUpdateMoneyTransferInput {
    id: ID!

    "会員"
    member_id: ID

    "Web招待状"
    web_invitation_id: ID

    "管理者"
    admin_id: ID

    "送金ステータス"
    status: MoneyTransferStatusEnum

    "エラーフラグ"
    is_error: Boolean

    "送金期限"
    deadline_date: Date

    "事前支払金額"
    prepayment_amount: Float

    "システム使用料"
    system_fee: Float

    "会員システム使用料"
    member_system_fee: Float

    "ゲストシステム使用料"
    guest_system_fee: Float

    "手数料"
    commission_fee: Float

    "送金金額"
    transfer_amount: Float

    "送金日"
    transfer_date: Date

    "送金完了時間"
    completion_datetime: DateTime

    "銀行コード"
    bank_code: String

    "銀行名"
    bank_name: String

    "支店コード"
    branch_code: String

    "支店名"
    branch_name: String

    "口座種別"
    account_type: AccountTypeEnum

    "口座名義"
    account_name: String

    "口座番号"
    account_number: String
}
