# マスターブロックのスキーマ定義
input AdminUpdateMasterBlockInput {

    "id"
    id: ID!

    "基本バリエーションテンプレートID"
    basic_variation_template_id: ID

    "テンプレート名称"
    name: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "テンプレート名称"
        )

    "商品種別"
    product_type: ProductTypeEnum
        @rules(
            apply: ["nullable"]
            attribute: "商品種別"
        )

    "Jsonデータ"
    json_data: Json
        @rules(
            apply: ["nullable", "max:10000"]
            attribute: "Jsonデータ"
        )
}
