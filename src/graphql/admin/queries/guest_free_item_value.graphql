#ゲストフリー項目値のスキーマ定義
extend type Query {
    "ゲストフリー項目値一覧取得"
    adminGuestFreeItemValues(
        orderBy: [AdminQueryGuestFreeItemValuesOrderByOrderByClause!]
            @orderBy(
                columns: ["guest_id", "name", "content"]
            )
    ): [GuestFreeItemValue!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲストフリー項目値検索詳細取得"
    adminGuestFreeItemValue(id: ID! @eq): GuestFreeItemValue @find @canAccess
}

input AdminQueryGuestFreeItemValuesOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestFreeItemValuesOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestFreeItemValuesOrderByColumn{
    GUEST_ID @enum(value: "guest_id")
    NAME @enum(value: "name")
    CONTENT @enum(value: "content")
}
