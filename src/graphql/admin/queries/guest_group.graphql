#ゲストグループのスキーマ定義
extend type Query {
    "ゲストグループ一覧取得"
    adminGuestGroups(
        orderBy: [AdminQueryGuestGroupsOrderByOrderByClause!]
            @orderBy(
                columns: ["guest_list_id", "name", "created_at"]
        )
    ): [GuestGroup!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲストグループ検索詳細取得"
    adminGuestGroup(id: ID! @eq): GuestGroup @find @canAccess
}

input AdminQueryGuestGroupsOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestGroupsOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestGroupsOrderByColumn{
    GUEST_LIST_ID @enum(value: "guest_list_id")
    NAME @enum(value: "name")
    CREATED_AT @enum(value: "created_at")
}
