#ゲストタグのスキーマ定義
extend type Query {

    "ゲストタグ一覧取得"
    adminGuestTags(
        orderBy: [AdminQueryGuestTagsOrderByOrderByClause!]
            @orderBy(
                columns: ["guest_list_id", "tag", "created_at"]
        )
    ): [GuestTag!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲストタグ取得詳細取得"
    adminGuestTag(id: ID! @eq): GuestTag @find @canAccess
}

input AdminQueryGuestTagsOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestTagsOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestTagsOrderByColumn{
    GUEST_LIST_ID  @enum(value: "guest_list_id")
    TAG @enum(value: "tag")
    CREATED_AT @enum(value: "created_at")
}
