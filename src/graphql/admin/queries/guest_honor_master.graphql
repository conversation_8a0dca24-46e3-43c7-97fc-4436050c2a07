#ゲスト敬称リスト初期値マスタのスキーマ定義
extend type Query {
    "ゲスト敬称初期値マスタ一覧取得"
    adminGuestHonorMasters(
        orderBy: [AdminQueryGuestHonorMastersOrderByOrderByClause!]
            @orderBy(
                columns: ["name", "created_at"]
            )
    ): [GuestHonorMaster!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲスト敬称初期値マスタ検索詳細取得"
    adminGuestHonorMaster(id: ID! @eq): GuestHonorMaster @find @canAccess
}

input AdminQueryGuestHonorMastersOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestHonorMastersOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestHonorMastersOrderByColumn{
    NAME @enum(value: "name")
    CREATED_AT @enum(value: "created_at")
}
