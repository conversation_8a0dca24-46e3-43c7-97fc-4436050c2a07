#送金
extend type Query {

    "送金情報一覧取得"
    adminGuestPayments(
        member_number: String
        member_name: String
        from_date: Date
        to_date: Date
        dead_line_from_date: Date
        dead_line_to_date: Date
        account: RegistrationEnum
        status: [MoneyTransferGuestPaymentStatusEnum!]
    ): [GuestPayments!]!
    @paginate(defaultCount: 50, resolver: "App\\GraphQL\\Admin\\Queries\\AdminGuestPaymentsQuery")
    @canAccess

    "送金情報詳細取得"
    adminGuestPayment(id: ID!): GuestPayments
    @field(resolver: "App\\GraphQL\\Admin\\Queries\\AdminGuestPaymentQuery")
    @canAccess

    "送金情報合計金額"
    adminTotalGuestPaymentAmount(
        member_number: String
        member_name: String
        from_date: Date
        to_date: Date
        dead_line_from_date: Date
        dead_line_to_date: Date
        account: RegistrationEnum
        status: [MoneyTransferGuestPaymentStatusEnum!]
    ): Int!
    @field(resolver: "App\\GraphQL\\Admin\\Queries\\AdminTotalGuestPaymentAmountQuery")
    @canAccess
}
