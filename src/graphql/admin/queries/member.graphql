#会員
extend type Query {

    "会員情報一覧取得"
    adminMembers(
        name: String @scope(name: "fullName")
        email: String @like(template: "%{}%")
        number: String @scope(name: "Number")
        is_active: Int @scope(name: "isActive")
        is_regist: <PERSON><PERSON><PERSON> @scope(name: "isRegist")
    ): [Member!]!
        @paginate(defaultCount: 50)
        @canAccess

    "会員情報詳細取得"
    adminMember(id: ID! @eq): Member @find @canAccess
}
