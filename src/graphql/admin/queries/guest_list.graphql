#ゲストリストのスキーマ定義
extend type Query {
    "ゲストリスト一覧取得"
    adminGuestLists(
        orderBy: [AdminQueryGuestListsOrderByOrderByClause!]
            @orderBy(
                columns: ["member_id", "name", "created_at"]
            )
    ): [GuestList!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲストリスト検索詳細取得"
    adminGuestList(id: ID! @eq): GuestList @find @canAccess
}

input AdminQueryGuestListsOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestListsOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestListsOrderByColumn{
    MEMBER_ID @enum(value: "member_id")
    NAME @enum(value: "name")
    CREATED_AT @enum(value: "created_at")
}
