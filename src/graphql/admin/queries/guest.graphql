#ゲストのスキーマ定義
extend type Query {

    "ゲスト一覧取得"
    adminGuests(
        orderBy: [AdminQueryGuestsOrderByOrderByClause!]
            @orderBy(
                columns: [
                    "guest_list_id",
                    "last_name",
                    "first_name",
                    "last_name_kana",
                    "first_name_kana",
                    "last_name_romaji",
                    "first_name_romaji"
                    "created_at"
                ]
            )
    ): [Guest!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲスト詳細情報取得"
    adminGuest(id: ID! @eq): Guest @find @canAccess
}


input AdminQueryGuestsOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestsOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestsOrderByColumn{
    GUEST_LIST_ID @enum(value: "guest_list_id")
    LAST_NAME @enum(value: "last_name")
    FIRST_NAME @enum(value: "first_name")
    LAST_NAME_KANA @enum(value: "last_name_kana")
    FIRST_NAME_KANA @enum(value: "first_name_kana")
    LAST_NAME_ROMAJI @enum(value: "last_name_romaji")
    FIRST_NAME_ROMAJI @enum(value: "first_name_romaji")
    CREATED_AT @enum(value: "created_at")
}
