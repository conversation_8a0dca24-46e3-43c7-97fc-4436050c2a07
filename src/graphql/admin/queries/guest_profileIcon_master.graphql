#ゲストプロフィールプリセットアイコンマスタのスキーマ定義
extend type Query {
    "ゲストプロフィールアイコンマスタ一覧取得"
    adminGuestProfileIconMasters(
        orderBy: [AdminQueryGuestProfileIconMastersOrderByOrderByClause!]
            @orderBy(
                columns: ["photo_url", "created_at"]
            )
    ): [GuestProfileIconMaster!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲストプロフィールアイコンマスタ検索詳細取得"
    adminGuestProfileIconMaster(id: ID! @eq): GuestProfileIconMaster @find @canAccess
}


input AdminQueryGuestProfileIconMastersOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestProfileIconMastersOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestProfileIconMastersOrderByColumn{
    PHOTO_URL @enum(value: "photo_url")
    CREATED_AT @enum(value: "created_at")
}
