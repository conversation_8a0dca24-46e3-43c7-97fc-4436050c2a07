#ゲスト肩書リスト初期値マスタのスキーマ定義
extend type Query {
    "ゲスト肩書リスト初期値マスタ一覧取得"
    adminGuestTitleMasters(
        orderBy: [AdminQueryGuestTitleMastersOrderByOrderByClause!]
            @orderBy(
                columns: ["name", "created_at"]
            )
    ): [GuestTitleMaster!]!
    @paginate(defaultCount: 50)
    @canAccess

    "ゲスト肩書リスト初期値マスタ検索詳細取得"
    adminGuestTitleMaster(id: ID! @eq): GuestTitleMaster @find @canAccess
}

input AdminQueryGuestTitleMastersOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryGuestTitleMastersOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryGuestTitleMastersOrderByColumn{
    NAME @enum(value: "name")
    CREATED_AT @enum(value: "created_at")
}
