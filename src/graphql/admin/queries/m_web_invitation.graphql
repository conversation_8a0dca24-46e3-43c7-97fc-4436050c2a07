#Web招待状(商品マスタ)
extend type Query {

    "Web招待状共通テンプレートマスター一覧取得"
    adminMWebInvitationTemplates: [MWebInvitationTemplate!]! @canAccess @all

    "WEB招待状共通テンプレートマスタ詳細取得"
    adminMWebInvitationTemplate(id: ID! @eq): MWebInvitationTemplate @find @canAccess

    "WEB招待状メインビジュアルブロックマスタ一覧取得"
    adminMWebInvitationVisualBlocks: [MWebInvitationVisualBlock!]! @canAccess @all

    "WEB招待状メインビジュアルブロックマスタ詳細 取得"
    adminMWebInvitationVisualBlock(id: ID! @eq): MWebInvitationVisualBlock @find @canAccess

    "商品マスタ（Web招待状）一覧取得"
    adminProductWebInvitations(
        id: ID
        name: String
        sales_period_start: DateTime
        sales_period_end: DateTime
    ): [WebInvitationProduct!]!
        @canAccess
        @paginate(defaultCount: 50, resolver: "App\\GraphQL\\Admin\\Queries\\AdminProductWebInvitationsQuery")

    "商品マスタ（Web招待状）詳細取得"
    adminProductWebInvitation(
        id: ID! @eq
    ): Product
        @canAccess
        @find(scopes: ["deleteScope"])
}
