#送金
extend type Query {

    "送金情報一覧取得"
    adminMoneyTransfers(
        member_number: String @scope(name: "withMemberId")
        member_name: String @scope(name: "withMemberName")
        status: MoneyTransferStatusEnum @scope(name: "status")
        error_status: MoneyTransferErrorStatusEnum @scope(name: "errorStatus")
        transfer_from: Date @where(key: "transfer_date", operator: ">=")
        transfer_to: Date @where(key: "transfer_date", operator: "<=")
        from: Date @where(key: "deadline_date", operator: ">=")
        to: Date @where(key: "deadline_date", operator: "<=")
        account: RegistrationEnum @scope(name: "account")
        orderBy: [AdminQueryMoneyTransferOrderByOrderByClause!]
            @orderBy(
                columns: ["deadline_date", "transfer_amount"]
        )
    ): [MoneyTransfer!]!
    @paginate(defaultCount: 200)
    @canAccess

    "送金情報詳細取得"
    adminMoneyTransfer(id: ID! @eq): MoneyTransfer @find @canAccess

    "送金情報合計金額"
    adminTotalMoneyTransferAmount(
        member_number: String,
        member_name: String,
        status: MoneyTransferStatusEnum,
        error_status: MoneyTransferErrorStatusEnum,
        transfer_from: Date,
        transfer_to: Date,
        from: Date,
        to: Date,
        account: RegistrationEnum
    ): Int
        @field(resolver: "App\\GraphQL\\Admin\\Queries\\AdminTotalMoneyTransferAmountQuery")
        @canAccess
}

input AdminQueryMoneyTransferOrderByOrderByClause {
    "The column that is used for ordering."
    column: AdminQueryMoneyTransferOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  AdminQueryMoneyTransferOrderByColumn{
    DEADLINE_DATE  @enum(value: "deadline_date")
    TRANSFER_AMOUNT @enum(value: "transfer_amount")
}
