# 基本バリエーションテンプレートのスキーマ定義
extend type Mutation {

    "基本バリエーションテンプレート 登録"
    adminCreateBasicVariationTemplate(
        input: AdminCreateBasicVariationTemplateInput! @spread
    ): BasicVariationTemplate
    @canAccess
    @create

    "基本バリエーションテンプレート 更新"
    adminUpdateBasicVariationTemplate(
        input: AdminUpdateBasicVariationTemplateInput! @spread
    ): BasicVariationTemplate!
    @canAccess
    @update

    "基本バリエーションテンプレート 削除"
    adminDeleteBasicVariationTemplate(
        id: ID! @eq
    ): BasicVariationTemplate
    @canAccess
    @delete
}
