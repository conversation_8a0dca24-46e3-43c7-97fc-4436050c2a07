#会員
extend type Mutation {
    "会員情報登録"
    adminCreateMember(
        member: AdminCreateMemberInput!
        wedding_info: AdminCreateWeddingInfoInput
    ): Member!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminCreateMemberMutation")

    "会員情報更新"
    adminUpdateMember(
        member: AdminUpdateMemberInput!
        wedding_info: AdminUpdateWeddingInfoInput
    ): Member!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminUpdateMemberMutation")

    "会員情報削除"
    adminDeleteMember(
        id: ID! @eq
    ): Member!
        @canAccess
        @delete


    "会員なりすましログイン"
    adminLoginMember(
        id: ID! @eq
    ): String
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminLoginMemberMutation")
}