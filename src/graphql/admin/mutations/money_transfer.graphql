# 送金
extend type Mutation {

    "送金更新"
    adminUpdateMoneyTransfer(
        money_transfer: AdminUpdateMoneyTransferInput! @spread
    ): MoneyTransfer!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminUpdateMoneyTransferMutation")

    "送金情報 入出金明細CSVインポート"
    adminCsvMoneyTransferImport(
        file: Upload!
    ): MoneyTransferCsvUpload!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminCSVMoneyTransferImportMutation")

    "複数データの送金日を更新"
    adminUpdateMultiplePaymentDate(
        ids: [ID!]
        date: Date
    ): Boolean!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminUpdateMultipleDateMutation")
}
