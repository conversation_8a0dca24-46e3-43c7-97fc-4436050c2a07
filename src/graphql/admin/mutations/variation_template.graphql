# バリエーションテンプレートのスキーマ定義
extend type Mutation {
    "バリエーションテンプレート情報新規作成"
    adminCreateVariationTemplate(
        input: AdminCreateVariationTemplateInput!
        master_block_ids: [ID!]
    ): VariationTemplate!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminCreateVariationTemplateMutation")

    "バリエーションテンプレート情報変更"
    adminUpdateVariationTemplate(
        input: AdminUpdateVariationTemplateInput!
        master_block_ids: [ID!]
    ): VariationTemplate!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminUpdateVariationTemplateMutation")


    "バリエーションテンプレート情報 削除"
    adminDeleteVariationTemplate(
        id: ID! @eq
    ): VariationTemplate
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminDeleteVariationTemplateMutation")
}
