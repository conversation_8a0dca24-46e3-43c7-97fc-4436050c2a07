#Web招待状(商品マスタ)
extend type Mutation {

    "商品マスタ（Web招待状）登録"
    adminCreateProductWebInvitation(
        product: AdminCreateProductinput!
        m_specification_products: [AdminCreateMSpecificationProductInput!]
        product_tags: [AdminCreateProductTagInput!]
    ): Product!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminCreateProductWebInvitationMutation")

    "商品マスタ（Web招待状）登録"
    adminUpdateProductWebInvitation(
        product: AdminUpdateProductinput!
        m_specification_products: [AdminUpdateMSpecificationProductInput!]
        product_tags: [AdminCreateProductTagInput!]
    ): Product!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminUpdateProductWebInvitationMutation")

    "商品マスタ（Web招待状）削除"
    adminDeleteProductWebInvitation(
        id: ID!
    ): Product!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminDeleteProductWebInvitationMutation")

    "商品マスタ（Web招待状）コピー"
    adminCopyProductWebInvitation(
        id: ID!
    ): Product!
        @canAccess
        @field(resolver: "App\\GraphQL\\Admin\\Mutations\\AdminCopyProductWebInvitationMutation")
}
