# イベントリスト
type EventList {
    id: ID!

    "ゲストリスト"
    guest_list: GuestList! @belongsTo

    "イベント名"
    event_name: String

    "開催日"
    event_date: Date

    "時間(Json)"
    event_time: Json

    "会場名"
    venue_name: String!

    "会場名(よみがな)"
    venue_name_kana: String

    "会場郵便番号"
    venue_postal_code: String

    "会場住所"
    venue_address: String

    "会場TEL"
    venue_tel: String

    "会場URL"
    venue_url: String

    "会費・ご祝儀(JSON)"
    fees: Json

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime

    "更新日時"
    updated_at: DateTime

    "ゲストイベント別回答"
    guest_event_answers: [GuestEventAnswer!]! @hasMany
}
