#商品スキーマ定義
type Product {
    id: ID!
    name: String #商品名
    shipping_cost: Int # 送料
    estimated_delivery_days: Int # 発送目安日数
    is_sample_request_allowed: Boolean # サンプル請求可能FLG
    is_use_component: Boolean # 構成部材利用FLG (falseの場合は部材)
    is_sales_period_specified: Boolean # 販売期間指定ありFLG
    sales_period_start: DateTime # 販売期間(FROM日時)
    sales_period_end: DateTime # 販売期間(TO日時)
    is_reservation_period_specified: Boolean # 販売予約期間指定ありFLG
    reservation_period_start: DateTime # 販売予約期間(FROM日時)
    reservation_period_end: DateTime # 販売予約期間(TO日時)
    product_inventory: Int # 商品在庫数
    admin_notes: String # 管理用備考
    product_description: String # 商品説明
    is_editor: Boolean # エディタありFLG
    is_specification: Boolean # 規格指定ありFLG
    is_variation_specification: Boolean # バリエーション指定ありFLG
    product_type_code: String # 商品種別コード
    option_product_price_difference: Int # オプション商品差分価格
    is_unpublished: Boolean # 非公開FLG
    meta_title: String # meta_title
    meta_description: String # meta_description
    meta_canonical: String # meta_canonical
    meta_keywords: String # meta_keywords
    display_order: String # 表示順
    deleted_at: DateTime #削除日時
    created_at: DateTime! #登録日時
    updated_at: DateTime! #更新日時
    tags: [Tag!]! @belongsToMany(relation: "tags") #商品タグ
    m_specification_products: [MSpecificationProduct!] @hasMany #規格別商品
}
