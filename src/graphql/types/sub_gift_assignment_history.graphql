#引き出物割り当てサブギフト履歴スキーマ定義
type SubGiftAssignmentHistory {
    id: ID!

    "引き出物割り当てメインギフト履歴"
    gift_assignment_history: GiftAssignmentHistory! @belongsTo

    "引き出物オプション商品"
    gift_option_product: GiftOptionProduct! @belongsTo

    "サブギフト番号"
    sub_gift_number: Int!

    "引き出物オプション商品名"
    gift_option_product_name: String!

    "引き出物オプション商品説明"
    gift_option_product_description: String

    "引き出物オプション商品画像"
    gift_option_product_image: String!

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
