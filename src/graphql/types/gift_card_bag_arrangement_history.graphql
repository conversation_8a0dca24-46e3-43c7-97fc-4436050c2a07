#引き出物カードバッグ手配履歴スキーマ定義
type GiftCardBagArrangementHistory {
    id: ID!

    "引き出物手配履歴"
    gift_arrangement_history: GiftArrangementHistory! @belongsTo

    "ステータス"
    status: Int!

    "出荷ステータス"
    shipping_status: Int!

    "入稿予定日"
    submission_schedule: Date!

    "出荷期日"
    shipping_deadline: Date!

    "出荷日"
    shipping_date: Date!

    "出荷元"
    shipping_origin: String!

    "お届け希望日"
    delivery_date: Date!

    "お届け希望時間"
    delivery_time: String

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "引き出物カードバッグ手配履歴明細"
    gift_card_bag_arrangement_history_details: [GiftCardBagArrangementHistoryDetail!]!
        @hasMany
}
