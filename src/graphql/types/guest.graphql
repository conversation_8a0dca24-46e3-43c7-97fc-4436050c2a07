#ゲストのスキーマ定義
type Guest {
    id: ID!

    "会員"
    member: Member! @belongsTo

    "ゲストリスト"
    guest_list: GuestList! @belongsTo

    "ゲストグループ"
    guest_group: GuestGroup @belongsTo

    "連名筆頭者"
    parent_guest: Guest @belongsTo

    "配下の連名ゲスト"
    children_guests: [Guest!]! @hasMany

    "ゲストフリー項目値"
    guest_free_item_values: [GuestFreeItemValue!]! @hasMany

    "ゲストタグ"
    guest_tags: [GuestTag!]! @belongsToMany(relation: "guest_tags")

    "ユーザー作成WEB招待状"
    web_invitation: WebInvitation @belongsTo

    "WEB招待状マスタ"
    m_web_invitation: MWebInvitation @belongsTo

    "ゲストイベント別回答"
    guest_event_answers: [GuestEventAnswer!]! @hasMany

    "ゲストアンケート別回答"
    guest_survey_answers: [GuestSurveyAnswer!]! @hasMany

    "ゲストタイプ"
    guest_type: GuestTypeEnum

    "姓"
    last_name: String!

    "名"
    first_name: String!

    "せい"
    last_name_kana: String

    "めい"
    first_name_kana: String

    "姓(ローマ字)"
    last_name_romaji: String

    "名(ローマ字)"
    first_name_romaji: String

    "性別"
    gender: GenderEnum

    "アレルギー品目"
    allergies: Json

    "その他アレルギー"
    allergy: String

    "生年月日"
    birthdate: Date

    "プロフィール画像"
    image_url: String

    "郵便番号"
    postal_code: String

    "都道府県"
    prefecture: String

    "市区町村"
    city: String

    "丁目・番地"
    address: String

    "建物名・部屋番号"
    building: String

    "電話番号"
    phone: String

    "メールアドレス"
    email: String

    "メッセージ"
    message: String

    "お祝い画像・動画の種類"
    media_type: MediaTypeEnum

    "お祝い画像・動画UUID"
    media_uuid: String

    "招待状お届け方法"
    invitation_delivery: Int

    "ゲスト肩書"
    guest_title: String

    "ゲスト敬称"
    guest_honor: String

    "間柄"
    relationship: String

    "関係性"
    relationship_name: String

    "Web招待状返信日時"
    web_invite_reply_datetime: DateTime

    "会員確認済FLG"
    member_confirm_type: MemberConfirmTypeEnum

    "会費・ご祝儀支払い方法"
    payment_method: PaymentMethodEnum

    "お気持ち金額"
    gift_amount: Int

    "システム利用料負担FLG"
    is_system_fee: Boolean

    "システム利用料"
    system_fee: Int

    "システム利用料率"
    system_fee_rate: Float

    "会費・ご祝儀・お気持ち金額合計金額"
    total_amount: Int

    "決算金額"
    settlement_amount: Int

    "カード決算ID"
    card_settlement_id: String

    "削除日時"
    deleted_at: DateTime

    "作成日時"
    created_at: DateTime!

    "最終更新日時"
    updated_at: DateTime!

    "ゲスト出席"
    guest_event_attendance: [GuestEventAttendance!]
}
