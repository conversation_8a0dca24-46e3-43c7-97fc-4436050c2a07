# WEB招待状共通テンプレートマスタ
type MWebInvitationTemplate {
    id: ID!

    "テンプレート名"
    name: String!

    "テンプレート説明"
    description: String!

    "テンプレートイメージ画像"
    image: String

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "WEB招待状共通テンプレートブロック"
    web_invitation_template_blocks: [WebInvitationTemplateBlock!] @hasMany

    "WEB招待状マスタ"
    m_web_invitations: [MWebInvitation!] @hasMany
}
