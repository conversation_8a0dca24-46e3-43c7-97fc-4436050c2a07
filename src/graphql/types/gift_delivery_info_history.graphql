type GiftDeliveryInfoHistory {
    id: ID!
    "引き出物送付先情報履歴"
    gift_arrangement_history: GiftArrangementHistory! @belongsTo
    "ゲスト"
    guest: Guest! @belongsTo
    "ゲストタグ"
    guest_tag: GuestTag! @belongsTo
    "宛名"
    recipient_name: String!
    "郵便番号"
    postal_code: String!
    "都道府県"
    prefecture: String!
    "市区町村"
    city: String!
    "丁目・番地"
    address: String!
    "建物名・部屋番号"
    building: String
    "電話番号"
    phone: String!
    "配送伝票番号"
    shipping_number: String
    "配送状況"
    shipping_status: Int
    "削除日時"
    deleted_at: DateTime
    "作成日時"
    created_at: DateTime!
    "更新日時"
    updated_at: DateTime!
    "引き出物割り当てメインギフト履歴"
    gift_assignment_history: GiftAssignmentHistory! @hasOne
}
