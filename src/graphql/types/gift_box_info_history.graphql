#引き出物Box情報履歴スキーマ定義
type GiftBoxInfoHistory {
    id: ID!

    "引き出物手配履歴"
    gift_arrangement_history: GiftArrangementHistory! @belongsTo

    "引き出物オプション商品"
    gift_option_product: GiftOptionProduct! @belongsTo

    "引き出物オプション商品名"
    gift_option_product_name: String!

    "引き出物オプション説明"
    gift_option_product_description: String

    "引き出物商品画像"
    gift_option_product_image: String!

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
