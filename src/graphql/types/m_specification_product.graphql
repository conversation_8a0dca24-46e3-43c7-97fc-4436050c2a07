#規格別商品マスタ
type MSpecificationProduct {
    id: ID!

    "商品マスタ(販売期間検索あり)"
    product: Product @belongsTo

    "商品マスタ(販売期間検索なし)"
    no_period_product: Product! @belongsTo

    "規格明細1"
    product_specification_detail_info_id1: ID

    "規格明細2"
    product_specification_detail_info_id2: ID

    "規格明細3"
    product_specification_detail_info_id3: ID

    "バリエーションテンプレート"
    variation_template_id: ID

    "エディタテンプレート"
    editor_template_id: ID

    "品番"
    item_number: String

    "通常価格"
    regular_price: Int

    "販売価格"
    sale_price: Int

    "販売価格利用期間(FROM日時)"
    sale_price_start: DateTime

    "販売価格利用期間(TO日時)"
    sale_price_end: DateTime

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "Web招待状マスタ"
    m_web_invitations: MWebInvitation @hasOne

    "規格別商品画像"
    product_images: [ProductImage!]! @hasMany
}
