#ファイルアップロードのレスポンス型作成
type ImageResponse {

    "uuid"
    uuid: String

    "サイズ指定"
    size: String

    "ファイル種別"
    type: ImageTypeEnum

    "画像パス(有効期限つき)"
    presigned_url: String

    "画像取得結果"
    status: Boolean

    "動画(有効期限つき)"
    presigned_url_main: String
}

# ファイルアップロードのレスポンス型作成
type ImageUploadResponse {

    "uuid"
    uuid: String

    "ファイル種別"
    type: ImageTypeEnum

    "ファイル拡張子"
    file_extension: String

    "オリジナル(元サイズ)画像"
    presigned_url: String

    "Sサイズ画像"
    presigned_url_s: String

    "Mサイズ画像"
    presigned_url_m: String

    "Lサイズ画像"
    presigned_url_l: String

    "動画"
    presigned_url_main: String
}
