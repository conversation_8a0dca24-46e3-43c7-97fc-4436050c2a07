#ゲストのスキーマ定義
type GuestEvent {
    id: ID!

    "会員ID"
    member_id: String

    "ゲストリストID"
    guest_list_id: String

    "ゲストグループID"
    guest_group_id: String

    "筆頭者ID"
    parent_guest_id: String

    "招待状ID"
    web_invitation_id: String

    "招待状マスタID"
    m_web_invitation_id: String

    "ゲストグループ名"
    guest_group_name: String

    "ゲスト出席"
    guest_event_attendances: [GuestEventAttendance!]!

    "ゲストタグ"
    guest_event_tags: [GuestEventTag!]

    "ゲストタイプ"
    guest_type: GuestTypeEnum

    "姓"
    last_name: String

    "名"
    first_name: String

    "せい"
    last_name_kana: String

    "めい"
    first_name_kana: String

    "姓(ローマ字)"
    last_name_romaji: String

    "名(ローマ字)"
    first_name_romaji: String

    "性別"
    gender: GenderEnum

    "アレルギー品目"
    allergies: Json

    "その他アレルギー"
    allergy: String

    "生年月日"
    birthdate: Date

    "プロフィール画像"
    image_url: String

    "郵便番号"
    postal_code: String

    "都道府県"
    prefecture: String

    "市区町村"
    city: String

    "丁目・番地"
    address: String

    "建物名・部屋番号"
    building: String

    "電話番号"
    phone: String

    "メールアドレス"
    email: String

    "メッセージ"
    message: String

    "お祝い画像・動画の種類"
    media_type: MediaTypeEnum

    "お祝い画像・動画UUID"
    media_uuid: String

    "招待状お届け方法"
    invitation_delivery: Int

    "ゲスト肩書"
    guest_title: String

    "ゲスト敬称"
    guest_honor: String

    "間柄"
    relationship: String

    "Web招待状返信日時"
    web_invite_reply_datetime: DateTime

    "会員確認済FLG"
    member_confirm_type: MemberConfirmTypeEnum

    "会費・ご祝儀支払い方法"
    payment_method: PaymentMethodEnum

    "お気持ち金額"
    gift_amount: Int

    "システム利用料負担FLG"
    is_system_fee: Boolean

    "システム利用料"
    system_fee: Int

    "システム利用料率"
    system_fee_rate: Float

    "会費・ご祝儀・お気持ち金額合計金額"
    total_amount: Int

    "決算金額"
    settlement_amount: Int

    "カード決算ID"
    card_settlement_id: String

    "削除日時"
    deleted_at: DateTime

    "作成日時"
    created_at: DateTime

    "最終更新日時"
    updated_at: DateTime
}
