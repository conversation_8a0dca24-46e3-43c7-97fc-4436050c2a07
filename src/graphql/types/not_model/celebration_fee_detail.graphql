# 会費ご祝儀詳細
type CelebrationFee {

    "Web招待状ID"
    id: ID

    "Web招待状名"
    name: String

    "パーティー開催日"
    event_date: String

    "振込予定日"
    scheduled_transfer_date: Date

    "回答数"
    guest_event_answers_count: Int

    "お気持ち金額(合計)"
    gift_total_amount: Int

    "システム利用料負担金額"
    system_total_fee: Int

    "決済金額"
    payment_amount: Int

    "システム利用料"
    system_amount: Int

    "振込事務手数料"
    transfer_amount: Int

    "受取金額"
    total_amount: Int

    "会費ご祝儀イベント 配列"
    events: [CelebrationFeeEvent!]!
}

# 会費ご祝儀イベント
type CelebrationFeeEvent {

    "イベント名"
    event_name: String

    "事前支払いの金額合計"
    prepaid_amount_total: Int

    "事前支払いの人数合計"
    prepaid_count_total: Int

    "当日持参の人数合計"
    pay_at_venue_count_total: Int

    "支払い済みの人数合計"
    paid_count_total: Int

    "お気持ち金額(合計)"
    gift_total_amount: Int

    "システム利用料負担金額"
    system_total_fee: Int

    "イベント別合計金額"
    amount_total: Int

    "会費ご祝儀ゲスト 配列"
    guests: [CelebrationFeeGuest!]!
}

# 会費ご祝儀ゲスト
type CelebrationFeeGuest {

    "ID"
    id: ID

    "姓"
    last_name: String

    "名"
    first_name: String

    "敬称"
    guest_honor: String

    "支払いステータス"
    payment_method: PaymentMethodEnum

    "料金"
    amount: Int

    "出席"
    attendance: String

    "出席フラグ"
    is_attendance: Boolean

    "日付"
    date: Date

    "お気持ちフラグ"
    is_gift_amount: Boolean

    "お気持ち金額"
    gift_amount: Int

    "システム利用料負担フラグ"
    is_system_fee: Boolean

    "システム利用料金"
    system_fee: Int

    "連名者"
    child_guests: [CelebrationFeeGuest!]
}
