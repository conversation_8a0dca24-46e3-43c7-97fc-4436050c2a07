type GuestPayments {

    "Web招待状ID"
    id: ID!

    "会員ID"
    member_id: ID

    "会員番号"
    member_number: String

    "新会員番号"
    alternate_member_number: String

    "会員名"
    member_name: String

    "メールアドレス"
    email: String

    "電話番号"
    phone: String

    "web招待状名"
    name: String

    "送金日"
    date: Date

    "送金完了日時"
    completion_datetime: DateTime

    "送金期限"
    deadline_date: Date

    "会費・ご祝儀"
    total_amount: Int

    "システム利用料"
    system_fee: Int

    "ゲストシステム利用料"
    guest_system_fee: Int

    "会員システム利用料"
    member_system_fee: Int

    "手数料"
    commission_fee: Int

    "送金予定額"
    member_settlement_amount: Int

    "銀行コード"
    bank_code: String

    "銀行名"
    bank_name: String

    "支店コード"
    branch_code: String

    "支店名"
    branch_name: String

    "口座種別"
    account_type: AccountTypeEnum

    "口座名義"
    account_name: String

    "口座番号"
    account_number: String

    "送金ステータス"
    status: MoneyTransferGuestPaymentStatusEnum

    "事前支払い締め日"
    prepayment_due_date: Date
}
