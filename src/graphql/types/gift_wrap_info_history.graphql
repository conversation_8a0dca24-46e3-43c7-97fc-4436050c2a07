#引き出物のし情報履歴スキーマ定義
type GiftWrapInfoHistory {
    id: ID!

    "引き出物手配履歴"
    gift_arrangement_history: GiftArrangementHistory! @belongsTo

    "のし行数"
    noshi_rows: Int!

    "のし記載1"
    noshi_content_1: String

    "のし記載2"
    noshi_content_2: String

    "旧字・外字指定詳細"
    kanji_alphabet_details: String

    "のしPDFURL"
    noshi_pdf_url: String

    "入稿用のしPDFURL"
    submission_noshi_pdf_url: String

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
