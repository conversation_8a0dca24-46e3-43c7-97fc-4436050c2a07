#ユーザー作成WEB招待状
type WebInvitation {
    id: ID!

    "会員"
    member: Member! @belongsTo

    "Web招待状マスタ"
    m_web_invitation: MWebInvitation! @belongsTo

    "ゲストリスト"
    guest_list: GuestList! @belongsTo

    "ゲスト"
    guests: [Guest!]! @hasMany

    "招待状名"
    name: String

    "公開URL"
    public_url: String

    "パスワード"
    password: String

    "パスワード利用フラグ"
    is_password: <PERSON><PERSON>an

    "公開済みフラグ"
    is_public: Boolean

    "開催日"
    scheduled_date: Date

    "回答締切日"
    reply_deadline_date: Date

    "送金予定日"
    scheduled_transfer_date: Date

    "事前支払い締め日"
    prepayment_due_date: Date

    "エディタ設定値"
    editor_settings: Json

    "ブロック設定値"
    block_settings: Json

    "削除日時"
    deleted_at: DateTime

    "作成日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "パーティ情報配列(アクセサ)"
    event_list: Json

    "事前支払いゲストの存在チェック"
    has_pre_paid_guest: <PERSON><PERSON><PERSON>
}
