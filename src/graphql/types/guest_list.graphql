#ゲストリストのスキーマ定義
type GuestList {
    ""
    id: ID!

    "会員"
    member: Member! @belongsTo

    "ゲストリスト名称"
    name: String!

    "デフォルトゲストリストフラグ"
    is_default: Boolean

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "ゲスト"
    guests: [Guest!]! @hasMany

    "ゲストグループ"
    guest_groups: [GuestGroup!]! @hasMany

    "ゲストタグ"
    guest_tags: [GuestTag!]! @hasMany

    "ゲストタグ"
    web_invitations: [WebInvitation!]! @hasMany

    "パーティ情報配列(アクセサ)"
    event_list: Json

    "最新のゲスト更新日(アクセサ)"
    latest_guest_updated_at: DateTime
}
