#手配前引き出物割り当てメインギフトスキーマ定義
type GiftAssignment {
    id: ID!

    "手配前引き出物割り当てメインギフト履歴"
    gift_shipping_info: GiftShippingInfo! @belongsTo

    "購入済FLG"
    is_purchase: Boolean!

    "決済履歴明細(購入済みFLG=TRUE)"
    payment_history_detail: PaymentHistoryDetail @belongsTo

    "商品マスタ(購入済み=FALSEの時に使用)"
    product: Product @belongsTo

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "引き出物割り当てサブギフト"
    sub_gift_assignments: [SubGiftAssignment!]! @hasMany
}
