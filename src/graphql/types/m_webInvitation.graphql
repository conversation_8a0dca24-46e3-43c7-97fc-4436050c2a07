# WEB招待状マスタ
type MWebInvitation {
    id: ID!

    "規格別商品マスタ"
    m_specification_product: MSpecificationProduct! @belongsTo

    "WEB招待状テンプレートマスタ"
    m_web_invitation_template: MWebInvitationTemplate! @belongsTo

    "Web招待状メインビジュアルブロックマスタ"
    m_web_invitation_visual_block: MWebInvitationVisualBlock! @belongsTo

    "選択ファーストビュー"
    first_view_id: Int

    "メインビジュアル画像利用不可FLG"
    is_main_visual_image_disabled: Boolean!

    "プロフィール画像利用不可FLG"
    is_profile_image_disabled: Boolean!

    "メインビジュアル画像差し替え可能FLG"
    is_main_visual_image_replaceable: Boolean!

    "CSSコード(置換)"
    css_code: String

    "エディタ設定値JSON"
    editor_settings_json: Json

    "画像アスペクト比設定値"
    image_aspect_settings_json: Json

    "画像アスペクト比設定値(Jsonエンコード)"
    encode_image_aspect_settings_json: String

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "WEB招待状デザイン画像"
    web_invitation_design_images: [WebInvitationDesignImage!]! @hasMany

    "CSSコード(置換　管理画面)"
    css_code_replace: String
}
