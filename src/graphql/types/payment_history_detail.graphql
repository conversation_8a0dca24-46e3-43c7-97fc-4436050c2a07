#決済履歴明細スキーマ定義
type PaymentHistoryDetail {
    id: ID!

    "決済履歴"
    payment_history: PaymentHistory! @belongsTo

    "商品"
    product: Product! @belongsTo

    "商品名"
    product_name: String!

    "単価"
    unit_price: Int!

    "数量"
    quantity: Int!

    "キャンセルFLG"
    is_cancel: Boolean!

    "キャンセル日時"
    cancel_date: DateTime

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
