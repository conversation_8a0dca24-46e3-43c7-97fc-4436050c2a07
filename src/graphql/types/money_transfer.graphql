#送金
type MoneyTransfer {
    id: ID!

    "会員"
    member: Member! @belongsTo

    "Web招待状"
    web_invitation: WebInvitation! @belongsTo

    "管理人"
    admin: Admin @belongsTo

    "送金ステータス"
    status: MoneyTransferStatusEnum

    "送金エラー"
    is_error: <PERSON><PERSON>an

    "送金期限"
    deadline_date: Date

    "事前支払金額"
    prepayment_amount: Float

    "システム使用料"
    system_fee: Float

    "会員システム使用料"
    member_system_fee: Float

    "ゲストシステム使用料"
    guest_system_fee: Float

    "手数料"
    commission_fee: Float

    "送金金額"
    transfer_amount: Float

    "送金日"
    transfer_date: Date

    "送金完了日時"
    completion_datetime: DateTime

    "銀行コード"
    bank_code: String

    "銀行名"
    bank_name: String

    "支店コード"
    branch_code: String

    "支店名"
    branch_name: String

    "口座種別"
    account_type: AccountTypeEnum

    "口座名義"
    account_name: String

    "口座番号"
    account_number: String

    created_at: DateTime
    updated_at: DateTime

    "システム使用料"
    accessor_system_fee: Float

    "ゲストシステム使用料"
    accessor_guest_system_fee: Float

    "会員システム使用料"
    accessor_member_system_fee: Float
}
