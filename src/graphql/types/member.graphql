#会員
type Member {
    id: ID!

    "姓"
    last_name: String!

    "名"
    first_name: String!

    "せい"
    last_name_kana: String

    "めい"
    first_name_kana: String

    "姓(romaji)"
    last_name_romaji: String

    "名(romaji)"
    first_name_romaji: String

    "メールアドレス"
    email: String!

    email_verified_at: DateTime
    "パスワード"
    password: String

    "SNSログインID"
    sns_login_id: Json

    "SNSログイン時にパスワードでのログインも利用するか"
    is_use_password: Boolean

    "生年月日"
    birthdate: Date

    "会員番号"
    number: String

    "新会員番号"
    alternate_member_number: String

    "本会員FLG"
    is_regist: <PERSON><PERSON><PERSON>

    "仮会員UUID"
    tmp_uuid: String

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "ゲストリスト"
    guest_lists: [GuestList!] @hasMany

    "ゲスト"
    guests: [Guest!] @hasMany

    "ゲストフリー項目値"
    guest_free_item_values: [GuestFreeItemValue!] @hasMany

    "ゲストタグ"
    guest_tags: [GuestTag!] @hasMany

    "会員登録時アンケート情報"
    member_regist_questionnaires: [MemberRegistQuestionnaire!] @hasMany

    "結婚式情報"
    wedding_info: WeddingInfo @hasOne

    gift_wrappings: [GiftWrapping!]! @hasMany
    gift_shipping_infos: [GiftShippingInfo!]! @hasMany
    gift_arrangement_histories: [GiftArrangementHistory!]! @hasMany

    "会員銀行口座"
    member_bank_account: MemberBankAccount @hasOne

    "送金"
    money_transfers: [MoneyTransfer!] @hasMany

    "家族プロフィール"
    family_profiles: [FamilyProfile!] @hasMany

    "口座登録アラートフラグ"
    isAccountAlertRegist: Boolean
}
