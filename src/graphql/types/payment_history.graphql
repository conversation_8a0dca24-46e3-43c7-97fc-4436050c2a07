#決済履歴スキーマ定義
type PaymentHistory {
    id: ID!

    "注文履歴"
    order_history_id: Int

    "会員"
    member: Member! @belongsTo

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "決済履歴明細"
    payment_history_details: [PaymentHistoryDetail!]! @hasMany

    "引き出物手配り歴"
    gift_arrangement_history: [GiftArrangementHistory!]!
        @belongsToMany(relation: "gift_arrangement_histories")
}
