#引き出物手配履歴スキーマ定義
type GiftArrangementHistory {
    id: ID!

    "会員"
    member: Member! @belongsTo

    "手配日時"
    shipping_company_id: ID!

    "担当者"
    staff: Staff @belongsTo

    "手配日時"
    arrangement_datetime: DateTime!

    "お届け希望日"
    desired_delivery_date: Date!

    "ステータス"
    status: Int!

    "出荷ステータス"
    shipping_status: Int!

    "発注期日"
    order_deadline: Date

    "出荷日"
    shipping_date: Date

    "手配会社への伝達事項"
    note: String

    "作業メモ"
    memo: String

    "申込済みフラグ"
    is_apply_complete: Boolean!

    "送り主氏名"
    sender_name: String

    "送り主電話番号"
    sender_phone_number: String

    "伝票件名"
    slip_title: String

    "送り主郵便番号"
    sender_postal_code: String

    "送り主都道府県"
    sender_prefecture: String

    "送り主市区町村"
    sender_city: String

    "送り主丁目・番地"
    sender_address1: String

    "送り主建物名・部屋番号"
    sender_address2: String

    "削除日時"
    deleted_at: DateTime

    "作成日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "引き出物カードバッグ手配履歴"
    gift_card_bag_arrangement_history: GiftCardBagArrangementHistory @hasOne

    "引き出物のし情報履歴"
    gift_wrap_info_history: GiftWrapInfoHistory @hasOne

    "引き出物Box情報履歴"
    gift_box_info_history: GiftBoxInfoHistory @hasOne

    "引き出物送付先情報履歴"
    gift_delivery_info_histories: [GiftDeliveryInfoHistory!]! @hasMany

    "決済履歴"
    payment_histories: [PaymentHistory!]!
        @belongsToMany(relation: "payment_histories")
}
