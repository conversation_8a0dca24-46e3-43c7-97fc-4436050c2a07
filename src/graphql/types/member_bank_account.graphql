#会員銀行口座
type MemberBankAccount {
    id: ID!

    "会員"
    member: Member! @belongsTo

    "銀行コード"
    bank_code: String

    "銀行名"
    bank_name: String

    "支店コード"
    branch_code: String

    "支店名"
    branch_name: String

    "口座種別"
    account_type: AccountTypeEnum

    "口座名義"
    account_name: String

    "口座番号"
    account_number: String

    "電話番号"
    phone: String

    "削除日時"
    deleted_at: DateTime

    "作成日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
