# 家族プロフィール
type FamilyProfile {

    "id"
    id: ID

    "会員"
    member: Member! @belongsTo

    "姓"
    last_name: String

    "名"
    first_name: String

    "並び順"
    order: Int

    "姓"
    last_name_kana: String

    "名"
    first_name_kana: String

    "姓"
    last_name_romaji: String

    "名"
    first_name_romaji: String

    "生年月日"
    birth_date: Date

    "新郎新婦種別"
    type: FamilyProfileTypeEnum

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
