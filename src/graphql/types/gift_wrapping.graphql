#引き出物ラッピングのスキーマ定義を作成
type GiftWrapping {
    id: ID!

    "会員"
    member: Member! @belongsTo

    "購入済みFLG"
    is_purchase: Boolean!

    "決済履歴明細(購入済みFLG=true)"
    payment_history_detail: PaymentHistoryDetail @belongsTo

    "商品(購入済みFLG=false)"
    product: Product @belongsTo

    "引き出物オプション商品"
    gift_option_product: GiftOptionProduct @belongsTo

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!
}
