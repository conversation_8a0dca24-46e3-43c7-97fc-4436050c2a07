# 基本バリエーションテンプレートのスキーマ定義
type BasicVariationTemplate {

    "ID"
    id: ID!

    "テンプレート名称"
    name: String

    "サイズ(幅)"
    width: Int

    "サイズ(高さ)"
    height: Int

    "サイズ(単位)"
    unit: String

    "色延ばし有無"
    color_extension: Boolean

    "折り線種類"
    fold_line_type: Int

    "ウォーターマーク有無"
    watermark: Boolean

    "Jsonデータ"
    json_data: Json

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime

    "更新日時"
    updated_at: DateTime
}
