# バリエーションテンプレートのスキーマ定義
type VariationTemplate {

    "ID"
    id: ID!

    "基本バリエーションテンプレート"
    basic_variation_template: BasicVariationTemplate @belongsTo

    "マスターブロック"
    master_blocks: [MasterBlock!]! @belongsToMany(relation: "master_blocks")

    "テンプレート名称"
    name: String

    "商品種別"
    product_type: ProductTypeEnum

    "Jsonデータ"
    json_data: Json

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime

    "更新日時"
    updated_at: DateTime
}
