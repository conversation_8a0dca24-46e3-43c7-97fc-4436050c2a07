# Instagramカウントダウン画像設定
type InstagramCountdownSetting {
    "UUID"
    uuid: ID!

    "会員ID"
    member_id: String!

    "Instagram画像種別"
    instagram_type: InstagramTypeEnum!

    "画像データ（JSON）"
    image_data: JSON

    "名前表示フラグ"
    show_names: <PERSON><PERSON><PERSON>!

    "挙式日表示フラグ"
    show_event_date: Boolean!

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "会員情報"
    member: Member @belongsTo
}

# 入力タイプ
input InstagramCountdownSettingInput {
    "Instagram画像種別"
    instagram_type: InstagramTypeEnum!

    "画像データ（JSON）"
    image_data: JSON

    "名前表示フラグ"
    show_names: Boolean = true

    "挙式日表示フラグ"
    show_event_date: Boolean = true
}

# 更新用入力タイプ
input InstagramCountdownSettingUpdateInput {
    "UUID"
    uuid: ID!

    "画像データ（JSON）"
    image_data: JSON

    "名前表示フラグ"
    show_names: Boolean

    "挙式日表示フラグ"
    show_event_date: <PERSON>ole<PERSON>
}
