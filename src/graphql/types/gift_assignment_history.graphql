#引き出物割り当てメインギフト履歴スキーマ定義
type GiftAssignmentHistory {
    id: ID!

    "引き出物送付先情報履歴"
    gift_delivery_info_history: GiftDeliveryInfoHistory! @belongsTo

    "決済履歴明細"
    payment_history_detail: PaymentHistoryDetail! @belongsTo

    "削除日時"
    deleted_at: DateTime

    "登録日時"
    created_at: DateTime!

    "更新日時"
    updated_at: DateTime!

    "引き出物割り当てサブギフト履歴"
    sub_gift_assignment_histories: [SubGiftAssignmentHistory!]! @hasMany
}
