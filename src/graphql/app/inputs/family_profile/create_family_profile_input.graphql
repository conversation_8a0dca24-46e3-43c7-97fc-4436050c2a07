# 家族プロフィール
input CreateFamilyProfileInput {

    "姓"
    last_name: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "姓"
        )

    "名"
    first_name: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "名"
        )

    "並び順"
    order: Int
        @rules(
            apply: ["nullable", "numeric"]
            attribute: "並び順"
        )

    "せい"
    last_name_kana: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "せい"
        )

    "名（カナ）"
    first_name_kana: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "名（カナ）"
        )

    "姓（romaji）"
    last_name_romaji: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "姓(romaji)"
        )

    "名（romaji）"
    first_name_romaji: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "名(romaji)"
        )

    "生年月日"
    birth_date: Date
        @rules(
            apply: ["nullable"]
            attribute: "生年月日"
        )

    "新郎新婦種別"
    type: FamilyProfileTypeEnum
        @rules(
            apply: ["nullable"]
            attribute: "新郎新婦種別"
        )
}
