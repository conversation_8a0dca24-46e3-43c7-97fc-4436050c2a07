#ユーザー作成WEB招待状
input CreateWebInvitationInput {

    m_web_invitation_id: ID
        @rules(
            apply: ["required"]
            attribute: "WEB招待状マスタ"
        )

    guest_list_id: ID
        @rules(
            apply: ["required"]
            attribute: "ゲストリスト"
        )

    name: String
        @rules(
            apply: ["max:255"]
            attribute: "招待状名"
        )
    public_url: String
        @rules(
            apply: ["max:255", "unique:web_invitations,public_url"]
            attribute: "公開URL"
        )

    password: String
        @rules(
            apply: ["max:255"]
            attribute: "パスワード"
        )

    is_password: Boolean
        @rules(
            apply: ["boolean"]
            attribute: "パスワード利用フラグ"
        )

    is_public: Boolean
        @rules(
            apply: ["boolean"]
            attribute: "公開用フラグ"
        )

    scheduled_date: Date
        @rules(
            apply: ["nullable", "date"]
            attribute: "開催日"
        )

    reply_deadline_date: Date
        @rules(
            apply: ["nullable", "date"]
            attribute: "回答日"
        )

    scheduled_transfer_date: Date
        @rules(
            apply: ["nullable", "date"]
            attribute: "事前支払い締め日"
        )

    prepayment_due_date: Date
        @rules(
            apply: ["nullable", "date"]
            attribute: "送金予定日"
        )

    editor_settings: Json
        @rules(
            apply: ["nullable", "max:10000"]
            attribute: "エディタ設定値"
        )
    block_settings: Json
            @rules(
            apply: ["nullable", "max:10000"]
            attribute: "ブロック設定値"
        )
}
