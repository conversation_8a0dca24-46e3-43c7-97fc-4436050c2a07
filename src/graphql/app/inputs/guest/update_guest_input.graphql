#ゲストのスキーマ定義
input UpdateGuestInput {

    id: ID

    "ゲストリスト"
    guest_list_id: ID

    "ゲストグループ"
    guest_group_id: ID
        @rules(
            apply: ["nullable"]
            attribute: "ゲストグループ"
        )

    "連名筆頭者"
    parent_guest_id: ID
        @rules(
            apply: ["nullable"]
            attribute: "連名筆頭者"
        )

    "ゲストタイプ"
    guest_type: GuestTypeEnum
        @rules(
            apply: ["nullable"]
            attribute: "ゲストタイプ"
        )

    "姓"
    last_name: String
        @rules(
            apply: ["filled", "max:255"]
            attribute: "姓"
        )

    "名"
    first_name: String
        @rules(
            apply: ["filled", "max:255"]
            attribute: "名"
        )

    "せい"
    last_name_kana: String
        @rules(
            apply: ["max:255"]
            attribute: "せい"
        )

    "めい"
    first_name_kana: String
        @rules(
            apply: ["max:255"]
            attribute: "めい"
        )

    "姓(ローマ字)"
    last_name_romaji: String
        @rules(
            apply: ["max:255"]
            attribute: "姓(ローマ字)"
        )

    "名(ローマ字)"
    first_name_romaji: String
        @rules(
            apply: ["max:255"]
            attribute: "名(ローマ字)"
        )

    "性別"
    gender: GenderEnum

    "アレルギー品目"
    allergies: Json
        @rules(
            apply: ["nullable"]
            attribute: "アレルギー品目"
        )

    "その他アレルギー"
    allergy: String
        @rules(
            apply: ["max:255"]
            attribute: "その他アレルギー"
        )

    "生年月日"
    birthdate: Date
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "生年月日"
        )

    "プロフィール画像"
    image_url: String
        @rules(
            apply: ["max:255"]
            attribute: "プロフィール画像"
        )

    "郵便番号"
    postal_code: String
        @rules(
            apply: ["max:255"]
            attribute: "郵便番号"
        )

    "都道府県"
    prefecture: String
        @rules(
            apply: ["max:255"]
            attribute: "都道府県"
        )

    "市区町村"
    city: String
        @rules(
            apply: ["max:255"]
            attribute: "市区町村"
        )

    "丁目・番地"
    address: String
        @rules(
            apply: ["max:255"]
            attribute: "丁目・番地"
        )

    "建物名・部屋番号"
    building: String
        @rules(
            apply: ["max:255"]
            attribute: "建物名・部屋番号"
        )

    "電話番号"
    phone: String
        @rules(
            apply: ["max:255"]
            attribute: "電話番号"
        )

    "メールアドレス"
    email: String
        @rules(
            apply: ["max:255"]
            attribute: "メールアドレス"
        )

    "メッセージ"
    message: String
        @rules(
            apply: ["max:500"]
            attribute: "メッセージ"
        )
    "招待状お届け方法"
    invitation_delivery: Int
        @rules(
            apply: ["nullable"]
            attribute: "招待状お届け方法"
        )

    "ゲスト肩書"
    guest_title: String
        @rules(
            apply: ["max:255"]
            attribute: "ゲスト肩書"
        )

    "ゲスト敬称"
    guest_honor: String
        @rules(
            apply: ["max:255"]
            attribute: "ゲスト敬称"
        )

    "間柄"
    relationship: String
        @rules(
            apply: ["max:255"]
            attribute: "間柄"
        )

    "関係性"
    relationship_name: String
        @rules(
            apply: ["max:255"]
            attribute: "関係性"
        )

    "Web招待状返信日時"
    web_invite_reply_datetime: DateTime
        @rules(
            apply: ["nullable"]
            attribute: "Web招待状返信日時"
        )

    "会員確認済FLG"
    member_confirm_type: MemberConfirmTypeEnum
        @rules(
            apply: ["boolean"]
            attribute: "会員確認済FLG"
        )

    "会費・ご祝儀支払い方法"
    payment_method: PaymentMethodEnum
        @rules(
            apply: ["nullable"]
            attribute: "会費・ご祝儀支払い方法"
        )

    "お気持ち金額"
    gift_amount: Int
        @rules(
            apply: ["nullable", "numeric"]
            attribute: "お気持ち金額"
        )

    "システム利用料負担FLG"
    is_system_fee: Boolean
        @rules(
            apply: ["boolean"]
            attribute: "システム利用料負担FLG"
        )

    "システム利用料"
    system_fee: Int
        @rules(
            apply: ["nullable", "numeric"]
            attribute: "システム利用料"
        )

    "システム利用料率"
    system_fee_rate: Float
        @rules(
            apply: ["nullable", "numeric"]
            attribute: "システム利用料率"
        )

    "会費・ご祝儀・お気持ち金額合計金額"
    total_amount: Int
        @rules(
            apply: ["nullable", "numeric"]
            attribute: "会費・ご祝儀・お気持ち金額合計金額"
        )

    "決算金額"
    settlement_amount: Int
        @rules(
            apply: ["nullable", "numeric"]
            attribute: "決算金額"
        )

    "カード決算ID"
    card_settlement_id: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "カード決算ID"
        )

    guest_tag_guests: [UpdateGuestTagGuestInput!]
}
