#会員
input UpdateMemberInput @validator(class: "MemberInputValidator") {

    id:ID

    "姓"
    last_name: String
        @rules(
            apply: ["max:255"]
            attribute: "姓"
        )
    "名"
    first_name: String
        @rules(
            apply: ["max:255"]
            attribute: "姓"
        )

    "せい"
    last_name_kana: String
        @rules(
            apply: ["max:255"]
            attribute: "せい"
        )

    "めい"
    first_name_kana: String
        @rules(
            apply: ["max:255"]
            attribute: "めい"
        )

    "姓(romaji)"
    last_name_romaji: String
        @rules(
            apply: ["max:255"]
            attribute: "姓(romaji)"
        )

    "名(romaji)"
    first_name_romaji: String
        @rules(
            apply: ["max:255"]
            attribute: "名(romaji)"
        )

    "メールアドレス"
    email: String
        @rules(
            apply: ["max:255", "email:filter"]
            attribute: "メールアドレス"

        )

    "パスワード"
    password: String
        @rules(
            apply: ["max:255",]
            attribute: "パスワード"
        )

    "SNSログインID"
    sns_login_id: Json
        @rules(
            apply: ["nullable", "max:255",]
            attribute: "SNSログインID"
        )

    "生年月日"
    birthdate: Date
        @rules(
            apply: ["nullable","date"]
            attribute: "生年月日"

        )
}
