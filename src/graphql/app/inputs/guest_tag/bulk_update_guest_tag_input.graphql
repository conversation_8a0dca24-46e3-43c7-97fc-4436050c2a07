#ゲストタグのスキーマ定義
input BulkUpdateGuestTagInput {

    "ゲストリスト"
    guest_list_id: String
        @rules(
            apply: ["required", "max:255"]
            attribute: "ゲストリスト"
        )

    "ゲスト(複数)"
    guest_ids: [String]
        @rules(
            apply: ["required"]
            attribute: "ゲスト"
        )

    "タグ"
    tags: [GuestTagInput!]
}

input GuestTagInput {

    tag_id: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "タグID"
        )

    tag: String
        @rules(
            apply: ["max:255"]
            attribute: "タグ名"
        )

    selected: Boolean
        @rules(
            apply: ["required", "boolean"]
            attribute: "選択状態フラグ"
        )

    dirty: Boolean
        @rules(
            apply: ["required", "boolean"]
            attribute: "dirtyフラグ"
        )
}
