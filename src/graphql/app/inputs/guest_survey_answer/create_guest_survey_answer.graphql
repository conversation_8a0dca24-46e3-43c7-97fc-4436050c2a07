#ゲストイベント別回答
input CreateGuestSurveyAnswerInput {

    "ゲスト"
    guest_id: ID
        @rules(
            apply: ["nullable"]
            attribute: "ゲスト"
        )

    "UI種類"
    ui_type: UiTypeEnum
        @rules(
            apply: ["nullable"]
            attribute: "UI種類"
        )

    "質問内容"
    question: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "質問内容"
        )

    "回答内容"
    answer_content: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "回答内容"
        )
}
