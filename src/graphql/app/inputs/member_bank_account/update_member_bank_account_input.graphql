#会員銀行口座
input UpdateMemberBankAccountInput {

    id: ID!

    "銀行コード"
    bank_code: String
        @rules(
            apply: ["nullable", "max:4"]
            attribute: "銀行コード"
        )

    "銀行名"
    bank_name: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "銀行名"
        )

    "支店コード"
    branch_code: String
        @rules(
            apply: ["nullable", "max:3"]
            attribute: "支店コード"
        )

    "支店名"
    branch_name: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "支店名"
        )

    "口座種別"
    account_type: AccountTypeEnum
        @rules(
            apply: ["nullable"]
            attribute: "口座種別"
        )

    "口座名義"
    account_name: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "口座名義"
        )

    "口座番号"
    account_number: String
        @rules(
            apply: ["nullable", "max:7"]
            attribute: "口座番号"
        )

    "電話番号"
    phone: String
        @rules(
            apply: ["nullable", "max:255"]
            attribute: "電話番号"
        )
}
