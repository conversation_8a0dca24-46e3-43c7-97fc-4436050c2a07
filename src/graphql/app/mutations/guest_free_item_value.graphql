#ゲストフリー項目値のスキーマ定義
extend type Mutation {
    "ゲストフリー項目値作成"
    createGuestFreeItemValue(
        input: CreateGuestFreeItemValueInput! @spread
    ): GuestFreeItemValue! @create

    "ゲストフリー項目値更新"
    updateGuestFreeItemValue(
        input: UpdateGuestFreeItemValueInput! @spread
    ): GuestFreeItemValue! @update

    "ゲストフリー項目値削除"
    deleteGuestFreeItemValue(id: ID! @eq): GuestFreeItemValue @delete
}
