#ゲストタグのスキーマ定義
extend type Mutation {
    "ゲストタグ作成"
    createGuestTag(
        input: CreateGuestTagInput! @spread
    ): GuestTag!
        @canAccess
        @create
        @addMember

    "ゲストタグ更新"
    updateGuestTag(
        input: UpdateGuestTagInput! @spread
    ): GuestTag!
        @canAccess
        @update
        @checkMutationMember

    "ゲストタグ削除"
    deleteGuestTag(
        id: ID! @eq
    ): GuestTag
        @delete
        @checkMutationMember

    "タグ一括編集用 保存API"
    bulkUpdateGuestTag(
        input: BulkUpdateGuestTagInput! @spread
    ):  Boolean!
        @canAccess
        @field(resolver: "BulkUpdateGuestTagMutation")
}
