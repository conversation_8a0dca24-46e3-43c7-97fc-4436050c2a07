#会員
extend type Mutation {
    "会員情報仮登録"
    tmpCreateMember(
        input: CreateMemberInput!
        member_regist_questionnaire: [CreateMemberRegistQuestionnaireInput!]
        wedding_info: CreateWeddingInfoInput
    ): Boolean!
        @field(resolver: "TmpCreateMemberMutation")

    "会員情報本登録"
    createMember(
        uuid: String!
    ): String!
        @field(resolver: "CreateMemberMutation")

    "会員更新"
    updateMember(
        input: UpdateMemberInput @spread
        wedding_info: UpdateWeddingInfoInput
    ): Member
        @canAccess
        @field(resolver: "UpdateMemberMutation")

    "会員削除(退会)"
    deleteMember: Member
        @canAccess
        @field(resolver: "DeleteMemberMutation")


    "パスワード変更"
    updateMemberPassword(
        password:String!
        @rules(
            apply: ["max:255"]
            attribute: "現在のパスワード"
        )
        new_password:String!
        @rules(
            apply: ["max:255"]
            attribute: "新しいパスワード"
        )

    ): Boolean!
        @canAccess
        @field(resolver: "UpdateMemberPasswordMutation")



    "パスワードリセットURL送信"
    sendResetPasswordUrl(
        email: String!
        @rules(
            apply: ["max:255", "email:filter"]
            attribute: "メールアドレス"
        )
    ): Boolean!
        @field(resolver: "SendResetPasswordUrlMutation")


    "パスワード再設定URLのチェック"
    checkResetPasswordUrl(
        token: String!
    ): Boolean!
        @field(resolver: "CheckResetPasswordUrlMutation")


    "パスワード再設定"
    resetPassword(
        token: String!
        @rules(
            apply: ["max:255"]
            attribute: "トークン"
        )
        password: String!
        @rules(
            apply: ["max:255"]
            attribute: "パスワード"
        )
    ): Boolean!
        @field(resolver: "ResetPasswordMutation")



    "メールアドレス再設定URL送信"
    sendChangeEmailUrl(
        email: String!
        @rules(
            apply: ["max:255", "email:filter"]
            attribute: "メールアドレス"
        )
        password: String!
        @rules(
            apply: ["max:255"]
            attribute: "パスワード"
        )
    ): Boolean!
        @field(resolver: "SendChangeEmailUrlMutation")


    "メールアドレス再設定URLのチェック"
    checkChangeEmailUrl(
        token: String!
    ): Boolean!
        @field(resolver: "CheckChangeEmailUrlMutation")
}
