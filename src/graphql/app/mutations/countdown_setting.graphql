extend type Mutation {
    "カウントダウン画像設定を作成・更新"
    saveCountdownImage(
        input: CountdownImageInput!
    ): CountdownImage!
        @canAccess
        @field(resolver: "SaveCountdownImageMutation")

    "カウントダウン画像設定を更新"
    updateCountdownImage(
        input: CountdownImageUpdateInput!
    ): CountdownImage!
        @canAccess
        @field(resolver: "UpdateCountdownImageMutation")

    "カウントダウン画像設定を削除"
    deleteCountdownImage(
        uuid: ID!
    ): Boolean!
        @canAccess
        @field(resolver: "DeleteCountdownImageMutation")
}
