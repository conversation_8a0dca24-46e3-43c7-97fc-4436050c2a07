#ファイル管理のスキーマ定義
extend type Mutation {
    "ファイルアップロード(base64)"
    uploadImage(
        file_type: FileType!
        file_data: String!
    ): ImageResponse!
        @field(resolver: "UploadImageMutation")

    "ゲストファイルアップロード(base64)"
    guestUploadImage(
        dir_name: String!
        file_data: String!
    ): ImageResponse
        @field(resolver: "GuestUploadImageMutation")

    "ファイルアップロード(バイナリー)"
    binaryUploadImage(
        file_type: FileType!
        file_data: Upload!
    ): ImageResponse!
        @field(resolver: "BinaryUploadImageMutation")

    "ゲストファイルアップロード(バイナリー)"
    guestBinaryUploadImage(
        dir_name: String!
        file_data: Upload!
    ): ImageResponse!
        @field(resolver: "GuestBinaryUploadImageMutation")

    "ファイルアップロード(バイナリー) version2"
    binaryUploadImage2(
        file_type: FileType!
        file_data: Upload!
            @rules(
                apply: ["required", "file", "mimes:jpg,jpeg,png,mp4,mov,avi,webm,flv", "max:51200"]
                attribute: "画像・動画"
            )
    ): ImageUploadResponse!
        @field(resolver: "BinaryUploadImageMutation2")

    "ゲストファイルアップロード(バイナリー) version2"
    guestBinaryUploadImage2(
        dir_name: String!
        file_data: Upload!
            @rules(
                apply: ["required", "file", "mimes:jpg,jpeg,png,mp4,mov,avi,webm,flv", "max:51200"]
                attribute: "画像"
            )
    ): ImageUploadResponse!
        @field(resolver: "GuestBinaryUploadImageMutation2")

    "画像アップロード拡張子更新"
    updateImageExtension(
        name: String
    ): Boolean!
        @field(resolver: "UpdateImageExtensionMutation")

    "画像非表示更新"
    updateImageHidden(
        uuid: String!
    ): Boolean!
        @field(resolver: "UpdateImageHiddenMutation")
}
