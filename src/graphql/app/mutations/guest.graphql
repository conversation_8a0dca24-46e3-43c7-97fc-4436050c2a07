#ゲストのスキーマ定義
extend type Mutation {
    "ゲスト情報新規作成"
    createGuest(
        input: CreateGuestInput!
        guests: [CreateGuestInput!]
        free_item_values: [CreateGuestFreeItemValueInput!]
        guest_event_answers: [CreateGuestEventAnswerInput!]
        guest_survey_answers: [CreateGuestSurveyAnswerInput!]
    ): Guest!
        @canAccess
        @field(resolver: "CreateGuestMutation")

    "ゲスト情報一括登録"
    bulkCreateGuest(
        input: [BulkCreateGuestInput!]!
    ): Boolean!
        @canAccess
        # @validator(class: "BulkCreateGuestValidator")
        @field(resolver: "BulkCreateGuestMutation")

    "ゲスト情報変更"
    updateGuest(
        input: UpdateGuestInput!
        guests: [UpdateGuestInput!]
        free_item_values: [CreateGuestFreeItemValueInput!]
        guest_event_answers: [CreateGuestEventAnswerInput!]
        guest_survey_answers: [CreateGuestSurveyAnswerInput!]
    ): Guest!
        @canAccess
        @field(resolver: "UpdateGuestMutation")

    "ゲスト情報削除"
    deleteGuest(
        id: [ID!]!
        is_delete_all: Boolean!
    ): Boolean!
        @canAccess
        @field(resolver: "DeleteGuestMutation")

    "ゲスト情報コピー"
    copyGuest(
        guest_list_id: ID!
        guest_ids: [ID!]!
    ): [Guest!]
        @canAccess
        @field(resolver: "CopyGuestMutation")

    "ゲスト自動登録(情報収集ツール)"
    createWebToolGuest(
        input: CreateGuestInput!
        guests: [CreateGuestInput!]!
    ): Guest! @field(resolver: "CreateWebToolGuestMutation")

    "ゲスト自動登録(WEB招待状回答)"
    createWebInvitationGuest(
        is_save: Boolean!
        input: CreateGuestNoValidateInput!
        guests: [CreateGuestNoValidateInput!]!
        free_item_values: [CreateGuestFreeItemValueNoValidateInput!]
        guest_event_answers: [CreateGuestEventAnswerNoValidateInput!]
        guest_survey_answers: [CreateGuestSurveyAnswerInput!]
        payment: PaymentInput
    ): Guest @field(resolver: "WebInvitationGuestMutation")

    "WEB招待状回答時の決済"
    createWebInvitationGuestPayment(
        input: CreateGuestNoValidateInput!
        guests: [CreateGuestNoValidateInput!]!
        payment: PaymentInput
    ): PaymentResult
        @field(resolver: "WebInvitationGuestPaymentMutation")

    "ゲスト情報複数一括更新"
    bulkUpdateGuest(
        input: [BulkUpdateGuestInput!]
    ): Boolean
        @canAccess
        @field(resolver: "BulkUpdateGuestMutation")
}
