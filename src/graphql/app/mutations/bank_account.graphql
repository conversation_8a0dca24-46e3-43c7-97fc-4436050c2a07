# 会員銀行口座
extend type Mutation {

    "会員銀行口座 登録"
    createMemberBankAccount(
        input: CreateMemberBankAccountInput! @spread
    ): MemberBankAccount!
        @canAccess
        @create
        @addMember

    "会員銀行口座 更新"
    updateMemberBankAccount(
        input: UpdateMemberBankAccountInput!
    ): MemberBankAccount!
        @canAccess
        @field(resolver: "UpdateMemberBankAccountMutation")
}
