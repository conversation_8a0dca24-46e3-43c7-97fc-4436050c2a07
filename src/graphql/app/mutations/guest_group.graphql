#ゲストグループのスキーマ定義
extend type Mutation {
    "ゲストグループ登録"
    createGuestGroup(
        input: CreateGuestGroupInput! @spread
    ): GuestGroup!
        @canAccess
        @create
        @addMember

    "ゲストグループ変更"
    updateGuestGroup(
        input: UpdateGuestGroupInput! @spread
    ): GuestGroup!
        @canAccess
        @update
        @checkMutationMember

    "ゲストグループ削除"
    deleteGuestGroup(
        id: ID! @eq
    ): GuestGroup
        @canAccess
        @delete
        @checkMutationMember
}
