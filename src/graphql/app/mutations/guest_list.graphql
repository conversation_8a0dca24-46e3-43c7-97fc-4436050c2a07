#ゲストリストのスキーマ定義
extend type Mutation {
    "ゲストリスト作成"
    createGuestList(input: CreateGuestListInput! @spread):
        GuestList!
        @canAccess
        @create
        @addMember

    "ゲストリスト更新"
    updateGuestList(input: UpdateGuestListInput! @spread):
        GuestList!
        @canAccess
        @update
        @checkMutationMember

    "ゲストリスト削除"
    deleteGuestList(id: ID! @eq): <PERSON><PERSON>an!
        @canAccess
        @field(resolver: "DeleteGuestListMutation")

    "ゲストリストコピー"
    copyGuestList(id: ID!, name: String!): GuestList!
        @canAccess
        @field(resolver: "CopyGuestListMutation")
}
