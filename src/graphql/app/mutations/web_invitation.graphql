#ユーザー作成Web招待状
extend type Mutation {
    "ユーザー作成Web招待状データ 登録"
    CreateWebInvitation(
        input: CreateWebInvitationInput! @spread
    ): WebInvitation
        @canAccess
        @field(resolver: "CreateWebInvitationMutation")

    "ユーザー作成Web招待状データ 変更"
    UpdateWebInvitation(
        is_synced: Boolean
        input: UpdateWebInvitationInput!
    ): WebInvitation
        @canAccess
        @field(resolver: "UpdateWebInvitationMutation")

    "ユーザー作成Web招待状データ 削除"
    deleteWebInvitation(
        id: ID! @eq
    ): WebInvitation
        @canAccess
        @delete
        @checkMutationMember

    "コピー ユーザー作成Web招待状データ"
    copyWebInvitation(
        id: String!
    ): WebInvitation
        @canAccess
        @field(resolver: "CopyWebInvitationMutation")
}
