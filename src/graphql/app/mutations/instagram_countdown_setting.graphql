extend type Mutation {
    "Instagramカウントダウン画像設定を作成・更新"
    saveInstagramCountdownSetting(
        input: InstagramCountdownSettingInput!
    ): InstagramCountdownSetting!
        @canAccess
        @field(resolver: "SaveInstagramCountdownSettingMutation")

    "Instagramカウントダウン画像設定を更新"
    updateInstagramCountdownSetting(
        input: InstagramCountdownSettingUpdateInput!
    ): InstagramCountdownSetting!
        @canAccess
        @field(resolver: "UpdateInstagramCountdownSettingMutation")

    "Instagramカウントダウン画像設定を削除"
    deleteInstagramCountdownSetting(
        uuid: ID!
    ): Bo<PERSON>an!
        @canAccess
        @field(resolver: "DeleteInstagramCountdownSettingMutation")
}
