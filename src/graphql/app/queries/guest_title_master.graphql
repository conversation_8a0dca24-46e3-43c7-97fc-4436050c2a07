#ゲスト肩書リスト初期値マスタのスキーマ定義
extend type Query {
    "ゲスト肩書リスト初期値マスタ一覧取得"
    guestTitleMasters(
        orderBy: [QueryGuestTitleMastersOrderByOrderByClause!]
            @orderBy(
                columns: ["name", "created_at"]
            )
    ): [GuestTitleMaster!]! @all @canAccess

    "ゲスト肩書リスト初期値マスタID検索"
    guestTitleMaster(id: ID! @eq): GuestTitleMaster @find
}

input QueryGuestTitleMastersOrderByOrderByClause {
    "The column that is used for ordering."
    column: QueryGuestTitleMastersOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  QueryGuestTitleMastersOrderByColumn{
    NAME @enum(value: "name")
    CREATED_AT @enum(value: "created_at")
}
