#ゲストリストのスキーマ定義
extend type Query {
    "ゲストリスト一覧取得"
    guestLists(
        orderBy: [QueryGuestListsOrderByOrderByClause!]
            @orderBy(
                columns: ["name", "is_default", "created_at"]
            )
    ): [GuestList!]! @all @whereAuth(relation: "member")

    "ゲストリストID検索"
    guestList(id: ID! @eq): GuestList @find @checkQueryMember
}

input QueryGuestListsOrderByOrderByClause {
    "The column that is used for ordering."
    column: QueryGuestListsOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  QueryGuestListsOrderByColumn{
    NAME @enum(value: "name")
    IS_DEFAULT @enum(value: "is_default")
    CREATED_AT @enum(value: "created_at")
}
