#ゲストタグのスキーマ定義
extend type Query {

    "ゲストタグ取得"
    guestTags(
        guest_list_id: ID! @eq
    ):
        [GuestTag!]!
        @all
        @whereAuth(relation: "member")

    "ゲストタグID検索"
    guestTag(
        id: ID! @eq
    ): GuestTag
        @find

    "タグ一括編集用 タグ一覧取得"
    updateGuestTags(
        guest_list_id: ID!
        guest_ids: [ID]!
    ):
        GuestTagResponse
        @canAccess
        @field(resolver: "UpdateGuestTagsQuery")
}
