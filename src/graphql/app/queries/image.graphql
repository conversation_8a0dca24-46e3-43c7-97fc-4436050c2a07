#画像取得
extend type Query {

    "共通ファイル取得"
    images(uuids: [String!]!):
        [ImageResponse!]!
        @field(resolver: "ImageQuery")

    "会員アップロード素材一覧取得"
    memberMaterialListImages:
        [ImageResponse!]!
        @canAccess
        @field(resolver: "MemberMaterialListImageQuery")

    "ファイルタイプに基づくファイル一覧を取得(デフォルトはおすすめ素材)"
    materialListImages(
        file_type: FileType
    ):
        [ImageResponse!]!
        @field(resolver: "MaterialListImageQuery")

    "共通ファイル取得 version2"
    images2(uuids: [String!]!):
        [ImageResponse!]!
        @field(resolver: "ImageQuery2")
}
