#ゲスト敬称リスト初期値マスタのスキーマ定義
extend type Query {
    "ゲスト敬称初期値マスタ一覧取得"
    guestHonorMasters(
        orderBy: [QueryGuestHonorMastersOrderByOrderByClause!]
            @orderBy(
                columns: ["name", "created_at"]
            )
    ): [GuestHonorMaster!]! @all @canAccess

    "ゲスト敬称リスト初期値マスタID検索"
    guestHonorMaster(id: ID! @eq): GuestHonorMaster @find
}

input QueryGuestHonorMastersOrderByOrderByClause {
    "The column that is used for ordering."
    column: QueryGuestHonorMastersOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  QueryGuestHonorMastersOrderByColumn{
    NAME @enum(value: "name")
    CREATED_AT @enum(value: "created_at")
}
