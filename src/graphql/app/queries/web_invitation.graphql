#ユーザー作成Web招待状
extend type Query {
    "ユーザー作成Web招待状データ 一覧取得"
    webInvitations(
        orderBy: [QueryWebInvitationsOrderByOrderByClause!]
            @orderBy(
                columns: ["created_at", "updated_at"]
            )
    ):
        [WebInvitation!]!
    @all @whereAuth(relation: "member")

    "ユーザー作成Web招待状データ詳細"
    webInvitation(
        id: ID! @eq
    ): WebInvitation
    @cloudFrontCookie
    @find
    @canAccess
    @checkQueryMember

    "ゲストユーザー作成web招待データ詳細"
    urlWebInvitation(
        public_url: String! @eq
    ): WebInvitation
    @field(resolver: "UrlWebInvitationQuery")
}

#ユーザー作成Web招待状データ ソート
input QueryWebInvitationsOrderByOrderByClause {
    column: QueryWebInvitationsOrderByColumn!
    order: SortOrder!
}

enum  QueryWebInvitationsOrderByColumn{
    CREATED_AT @enum(value: "created_at")
    UPDATED_AT @enum(value: "updated_at")
}
