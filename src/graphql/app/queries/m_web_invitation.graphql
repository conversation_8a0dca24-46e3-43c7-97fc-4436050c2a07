#Web招待状(商品マスタ)
extend type Query {
    "商品マスタ（Web招待状）詳細取得 旧"
    mSpecificationProduct(id: ID! @eq): MSpecificationProduct! @find

    "商品マスタ（Web招待状）一覧取得 旧"
    mSpecificationProducts(
        product_tag_ids: [ID!] @scope(name: "withProductTag")
        orderBy: [QueryMSpecificationProductsOrderByOrderByClause!]
            @orderBy(
                columns: ["created_at", "updated_at"]
            )
    ): [MSpecificationProduct!]! @paginate(defaultCount: 50)

    "商品マスタ（Web招待状）詳細取得 新"
    productWebInvitation(
        id: ID! @eq
    ): Product
        @find

    "商品マスタ（Web招待状）一覧取得 新"
    productWebInvitations(
        product_tag_ids: [ID!] @scope(name: "withTag")
        orderBy: [QueryMSpecificationProductsOrderByOrderByClause!]
            @orderBy(
                columns: ["created_at", "updated_at", "display_order"]
            )
    ): [Product!]! @paginate(defaultCount: 50)

    "WEB招待状共通テンプレートマスタ詳細取得"
    mWebInvitationTemplate(
        id: ID! @eq
    ): MWebInvitationTemplate!
        @find
}



#商品マスタ(Web招待状)ソート
input QueryMSpecificationProductsOrderByOrderByClause {
    "The column that is used for ordering."
    column: QueryMSpecificationProductsOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  QueryMSpecificationProductsOrderByColumn{
    CREATED_AT @enum(value: "created_at")
    UPDATED_AT @enum(value: "updated_at")
    DISPLAY_ORDER @enum(value: "display_order")
}
