#ゲストのスキーマ定義
extend type Query {

    "ゲスト一覧取得"
    guests(
        guest_list_id: String!
        guest_event_attendances: [GuestEventAttendanceQueryInput!]
        guest_name: String
        web_invitation_id: String
        guest_type: GuestTypeEnum
        guest_group_ids: [String!]
        guest_tag_ids: [String!]
        orderBy: _
            @orderBy(
                columns: ["created_at", "name"]
                relations: [{ relation: "guest_group", columns: ["name"] }]
            )
        is_update_member_confirm_type: Boolean!
    ): [GuestEvent!]!
        @canAccess
        @field(resolver: "GuestListEventAnswerQuery")

    "ゲスト詳細情報取得"
    guest(
        id: ID!
        is_update_member_confirm_type: <PERSON>olean!
    ): Guest
        @canAccess
        @cloudFrontCookie
        @field(resolver: "GuestQuery")

    "ゲスト新規登録者数取得"
    countNewGuest(member_confirm_type: MemberConfirmTypeEnum! @eq(value: New)): Int! @count(model: "Guest") @whereAuth(relation: "member") @canAccess
}
