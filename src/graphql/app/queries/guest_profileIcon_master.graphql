#ゲストプロフィールプリセットアイコンマスタのスキーマ定義
extend type Query {
    "ゲストプロフィールアイコンマスタ一覧取得"
    guestProfileIconMasters(
        orderBy: [QueryGuestProfileIconMastersOrderByOrderByClause!]
            @orderBy(
                columns: ["photo_url", "created_at"]
            )
    ): [GuestProfileIconMaster!]! @all @canAccess

    "ゲストプロフィールプリセットアイコンマスタID検索"
    guestProfileIconMaster(id: ID! @eq): GuestProfileIconMaster @find
}


input QueryGuestProfileIconMastersOrderByOrderByClause {
    "The column that is used for ordering."
    column: QueryGuestProfileIconMastersOrderByColumn!

    "The direction that is used for ordering."
    order: SortOrder!
}

enum  QueryGuestProfileIconMastersOrderByColumn{
    PHOTO_URL @enum(value: "photo_url")
    CREATED_AT @enum(value: "created_at")
}
