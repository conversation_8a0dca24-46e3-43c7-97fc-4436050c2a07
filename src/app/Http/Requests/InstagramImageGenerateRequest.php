<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Enums\InstagramTypeEnum;
use Illuminate\Validation\Rule;

class InstagramImageGenerateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'バリデーションエラーです。',
            'errors' => $validator->errors()
        ], 422));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'instagram_type' => ['required', Rule::in(InstagramTypeEnum::getValues())],
            'image_data' => 'nullable|array',
            'image_data.src' => 'string|url',
            'image_data.uuid' => 'string',
            'image_data.name' => 'nullable|string',
            'image_data.x' => 'numeric|min:0',
            'image_data.y' => 'numeric|min:0',
            'image_data.width' => 'numeric|min:1',
            'image_data.height' => 'numeric|min:1',
            'image_data.rotate' => 'numeric',
            'image_data.cropBoxLeft' => 'numeric|min:0',
            'image_data.cropBoxTop' => 'numeric|min:0',
            'image_data.cropBoxWidth' => 'numeric|min:1',
            'image_data.cropBoxHeight' => 'numeric|min:1',
            'image_data.originalWidth' => 'numeric|min:1',
            'image_data.originalHeight' => 'numeric|min:1',
            'image_data.scaleFactor' => 'numeric|min:0',
            'use_groom_bride_name' => 'required|boolean',
            'use_event_date' => 'required|boolean',
        ];
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'instagram_type.required' => 'Instagram種別は必須です。',
            'instagram_type.string' => 'Instagram種別は文字列で指定してください。',
            'image_data.array' => '画像データは配列形式で指定してください。',
            'image_data.src.url' => '画像URLは有効なURL形式で指定してください。',
            'use_groom_bride_name.boolean' => '新郎新婦名フラグはboolean値で指定してください。',
            'use_event_date.boolean' => '開催日フラグはboolean値で指定してください。',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'instagram_type' => 'Instagram種別',
            'image_data' => '画像データ',
            'image_data.src' => '画像URL',
            'image_data.uuid' => '画像UUID',
            'image_data.name' => '画像名',
            'image_data.x' => 'X座標',
            'image_data.y' => 'Y座標',
            'image_data.width' => '横幅',
            'image_data.height' => '高さ',
            'image_data.rotate' => '回転角度',
            'image_data.cropBoxLeft' => 'クロップボックスX座標',
            'image_data.cropBoxTop' => 'クロップボックスY座標',
            'image_data.cropBoxWidth' => 'クロップボックス横幅',
            'image_data.cropBoxHeight' => 'クロップボックス高さ',
            'image_data.originalWidth' => '元画像横幅',
            'image_data.originalHeight' => '元画像高さ',
            'image_data.scaleFactor' => '拡大率',
            'use_groom_bride_name' => '新郎新婦名取得フラグ',
            'use_event_date' => '開催日取得フラグ'
        ];
    }
}
