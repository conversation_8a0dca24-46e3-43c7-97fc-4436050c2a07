<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\DateRangeRule;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class GuestPaymentExportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'message' => 'バリデーションエラーです。',
            'errors' => $validator->errors()
        ], 422));
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'from_date' => ['nullable', 'date', new DateRangeRule('from_date', 'to_date', 'fromゲスト決済日はToゲスト決済日より前である必要があります。')],
            'to_date' => ['nullable', 'date'],
            'dead_line_from_date' => ['nullable', 'date', new DateRangeRule('dead_line_from_date', 'dead_line_to_date', 'From会員への送金日はTo会員への送金日より前である必要があります。')],
            'dead_line_to_date' => ['nullable', 'date'],
        ];
    }

    public function messages(): array
    {
        return [
            'from_date.date' => 'From日付は有効な日付形式である必要があります。',
            'to_date.date' => 'To日付は有効な日付形式である必要があります。',
            'dead_line_from_date.date' => '締切From日付は有効な日付形式である必要があります。',
            'dead_line_to_date.date' => '締切To日付は有効な日付形式である必要があります。',
        ];
    }
}
