<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\TrimStrings as Middleware;

class TrimStrings extends Middleware
{
    /**
     * The names of the attributes that should not be trimmed.
     *
     * @var array<int, string>
     */
    protected $except = [
        "current_password",
        "password",
        "password_confirmation",
    ];

    // TrimStringsクラスのtransformクラスをオーバーライド
    protected function transform($key, $value)
    {
        if (in_array($key, $this->except, true) || ! is_string($value) || str_contains($key, "variables.input.editor_settings")) {
            return $value;
        }

        return preg_replace('~^[\s\x{FEFF}\x{200B}]+|[\s\x{FEFF}\x{200B}]+$~u', '', $value) ?? trim($value);
    }
}
