<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class SetCustomHeaders
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        if (!$response->isRedirect()) {
            $response->headers->set(
                "Cache-Control",
                "no-cache, no-store, private"
            );
            $response->headers->set("Referrer-Policy", "no-referrer");
            $response->headers->set(
                "Strict-Transport-Security",
                "max-age=31536000; includeSubDomains"
            );
        }

        return $response;
    }
}
