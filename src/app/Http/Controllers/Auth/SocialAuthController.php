<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Laravel\Socialite\Facades\Socialite;
use App\Models\Member;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class SocialAuthController extends Controller
{
    // Google と LINE のみ
    private $_sns_list = ["google", "line"];

    /**
     * 認証ページへリダイレクトする。
     */
    public function redirect(Request $request, $sns = "")
    {
        // 許可SNSのみ
        if (!in_array($sns, $this->_sns_list)) {
            return redirect(env("SNS_LOGIN_URL"));
        }

        $redirect_uri = config("services." . $sns . ".redirect");
        if ($request->input("mode")) {
            $redirect_uri .= "?mode=" . $request->input("mode");
        }

        $url = Socialite::driver($sns)
            ->stateless()
            ->redirectUrl($redirect_uri)
            ->redirect()
            ->getTargetUrl();

        if ($sns === "line") {
            // クロスサイトリクエストフォージェリ (opens new window)防止用の固有な英数字の文字列。
            // ログインセッションごとにウェブアプリでランダムに生成してください。
            $url .= "&state=" . env("LINE_APP_STATE");
            // ボットプロンプトの設定（友達追加）
            // $url .= "&bot_prompt=". env('LINE_BOT_PROMPT');
        }
        return response()->json([
            "url" => $url,
        ]);
    }

    /**
     * コールバックを処理する。
     */
    public function handleCallback(Request $request, $sns = "")
    {
        // 許可SNSのみ
        if (!in_array($sns, $this->_sns_list)) {
            return redirect(env("SNS_LOGIN_URL"));
        }

        $redirect_uri = config("services." . $sns . ".redirect");
        $mode = $request->input("mode");
        if ($mode) {
            $redirect_uri .= "?mode=" . $mode;
        }

        try {
            $snsUser = Socialite::driver($sns)
                ->stateless()
                ->redirectUrl($redirect_uri)
                ->user();

            Log::info("SNSユーザー情報", [
                'id' => $snsUser->getId(),
                'email' => $snsUser->getEmail(),
            ]);

            // メールアドレスがなければエラーを返却
            if (!$snsUser->getEmail()) {
                return redirect(
                    env("SNS_LOGIN_URL") .
                        "?sns=" .
                        $sns .
                        "&error_code=NO_EMAIL"
                );
            }

            // SNSログインの場合は、SNS名とIDで認証する
            $member = Member::findBySns($sns, $snsUser->getId());
            if ($member) {
                if ($member->is_regist == false) {
                    // 仮会員
                    return redirect(
                        env("SNS_REGISTER_URL") .
                        "/" .
                        $sns .
                        "?id=" .
                        $snsUser->getId() .
                        "&email=" .
                        $snsUser->getEmail() .
                        "&mode=" .
                        $mode
                    );
                }
                // データがあればログイン
                $token = $member->createAuthToken();
                return redirect(
                    env("SNS_LOGIN_URL") .
                        "/" .
                        $token .
                        "?sns=" .
                        $sns .
                        "&mode=" .
                        $mode
                );
            } else {
                $member = Member::findByEmail($snsUser->getEmail());
                if ($member && $member->is_regist == true) {
                    if ($member->sns_login_id) {
                        // すでに別のSNSでログインしてた場合
                        return redirect(
                            env("SNS_LOGIN_URL") .
                                "?sns=" .
                                $sns .
                                "&error_code=IS_REGISTERED_" .
                                strtoupper(
                                    array_key_first($member->sns_login_id)
                                ) .
                                "&mode=" .
                                $mode
                        );
                    } else {
                        // メールアドレスでログインしてた場合
                        return redirect(
                            env("SNS_LOGIN_URL") .
                                "?sns=" .
                                $sns .
                                "&error_code=IS_REGISTERED_EMAIL" .
                                "&mode=" .
                                $mode
                        );
                    }
                }

                return redirect(
                    env("SNS_REGISTER_URL") .
                        "/" .
                        $sns .
                        "?id=" .
                        $snsUser->getId() .
                        "&email=" .
                        $snsUser->getEmail() .
                        "&mode=" .
                        $mode
                );
            }
        } catch (\Exception $e) {
            return redirect(env("SNS_LOGIN_URL"));
        }
    }
}
