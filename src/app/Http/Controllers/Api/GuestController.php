<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Guest;
use App\Enums\GuestTypeEnum;
use App\Enums\GenderEnum;
use App\Enums\PaymentMethodEnum;
use App\Services\CsvService;
use Carbon\Carbon;
use App\Consts\CsvConst;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\StreamedResponse;

use App\Exports\GuestsExport;
use App\Exports\GuestsFavoriCloudAddressExport;
use App\Exports\GuestsFavoriCloudSeatExport;
use App\Exports\MultipleSheetsExport;
use Maatwebsite\Excel\Facades\Excel;

class GuestController extends Controller
{
    protected $csvService;

    public function __construct(CsvService $csvService)
    {
        $this->csvService = $csvService;
    }

    /**
     * CSVエクスポート
     *
     * @param Request $request
     */
    public function export(Request $request)
    {
        $data = [];
        $data["member_id"] = Auth::id();
        $data["guest_list_id"] = $request->input("guestListId");

        $sheets = [];
        $sheets[] = new GuestsExport($data);
        // $sheets[] = new GuestsFavoriCloudAddressExport($data);
        // $sheets[] = new GuestsFavoriCloudSeatExport($data);

        return Excel::download(
            new MultipleSheetsExport($sheets),
            "export.xlsx"
        );
    }
}
