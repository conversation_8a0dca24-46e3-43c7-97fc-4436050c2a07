<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\MoneyTransfer;
use App\Services\CsvService;
use App\Enums\MoneyTransferStatusEnum;
use Carbon\Carbon;
use App\Consts\CsvConst;
use Exception;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class MoneyTransferController extends Controller
{
    protected $csvService;

    public function __construct(CsvService $csvService)
    {
        $this->csvService = $csvService;
    }

    /**
     * 送金情報 振込依頼CSVエクスポート
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function export(Request $request)
    {
        return DB::transaction(function () use ($request) {

            if (empty($request->id) && empty($request->ids)) {
                return response()->json([
                    "message" => "ダウンロード対象が選択されていません",
                    "status" => false
                ], 400);
            }

            $moneyTransfers = MoneyTransfer::with([
                "member" => function ($query) {
                    $query->withTrashed();
                },
                "member.member_bank_account"
                ])
                ->where(function ($query) use ($request) {
                    $id = $request->id;
                    $ids = $request->ids;

                    if (!empty($id)) {
                        $query->where("id", $id);
                    }

                    if (!empty($ids)) {
                        $query->whereIn("id", $ids);
                    }
                })
                ->get();

            $moneyTransferArray = [];
            foreach ($moneyTransfers as $moneyTransfer) {

                // ステータスが NOT_TRANSFERRED 以外なら例外
                if ($moneyTransfer->status !== MoneyTransferStatusEnum::NOT_TRANSFERRED) {
                    return response()->json([
                        "message" => "未送金ステータス以外が選択されています",
                        "status" => false
                    ], 400);
                }

                // member.member_bank_account がない場合、例外
                if (!$moneyTransfer->member->member_bank_account) {
                    return response()->json([
                        "message" => "口座未登録のデータが選択されています",
                        "status" => false
                    ], 400);
                }

                // 送金管理 更新
                $bankAccount = $moneyTransfer->member->member_bank_account;
                $moneyTransfer->update([
                    "bank_code" => $bankAccount->bank_code ?? null,
                    "bank_name" => $bankAccount->bank_name ?? null,
                    "branch_code" => $bankAccount->branch_code ?? null,
                    "branch_name" => $bankAccount->branch_name ?? null,
                    "account_type" => $bankAccount->account_type ?? null,
                    "account_name" => $bankAccount->account_name ?? null,
                    "account_number" => $bankAccount->account_number ?? null,
                    "status" => MoneyTransferStatusEnum::RESERVATION,
                ]);

                $moneyTransferArray[] = [
                    "bank_code" => $bankAccount->bank_code ?? null,
                    "branch_code" => $bankAccount->branch_code ?? null,
                    "account_type" => $bankAccount->account_type ?? null,
                    "account_number" => $bankAccount->account_number ?? null,
                    "account_name" => $bankAccount->account_name ?? null,
                    "transfer_amount" => (int) $moneyTransfer->transfer_amount,
                    "transfer_date" => $moneyTransfer->transfer_date,
                ];
            }

            $csvDate = '';
            if (!empty($moneyTransferArray)) {
                // 送金日 ごとにデータをグループ化
                $groupedTransfers = [];

                foreach ($moneyTransferArray as $item) {
                    $transfer_date = Carbon::parse($item['transfer_date'])->format("Y-m-d");
                    unset($item['transfer_date']);

                    $groupedTransfers[$transfer_date][] = $item;

                    if(empty($csvDate)) {
                        $csvDate = Carbon::parse($transfer_date)->format("Ymd");
                    }
                }

                // 送金日 ごとに CSV を出力
                $formattedTransfers = [];
                foreach ($groupedTransfers as $completionDate => $transferData) {
                    $date = Carbon::parse($completionDate)->format("Ymd");
                    $formattedTransfers[$date] = array_map(
                        function ($item, $idx) use ($completionDate) {
                            return array_merge(
                                [
                                    "service_type" => CsvConst::RAKUTEN_SERVICE_TYPE,
                                    "date" => Carbon::parse($completionDate)->format("md"),
                                ],
                                (array) $item,
                                [
                                    "count" => str_pad((string) ($idx + 1), 3, "0", STR_PAD_LEFT),
                                ]
                            );
                        },
                        $transferData,
                        array_keys($transferData)
                    );
                }

                if($request->has('id')){
                    $data = !empty($csvDate) ? array_map(fn($nestedArray) => reset($nestedArray), $formattedTransfers) : [];
                    $csvDate = $csvDate ?? Carbon::today()->format("Ymd");

                    return $this->csvService->export(
                        [],
                        $data,
                        $csvDate. ".csv"
                    );
                }else {

                    if(count($formattedTransfers) == 1){
                        return $this->csvService->export(
                            [],
                            $formattedTransfers,
                            $csvDate. ".csv"
                        );
                    }

                    return $this->csvService->exportZip(
                        [],
                        $formattedTransfers
                    );
                }


            }
        });
    }
}
