<?php

namespace App\Http\Controllers\Api\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\CsvService;
use Carbon\Carbon;
use App\Consts\SystemConst;
use App\Consts\CsvConst;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Illuminate\Support\Facades\DB;
use App\Enums\PaymentMethodEnum;
use App\Http\Requests\GuestPaymentExportRequest;

class GuestPaymentController extends Controller
{
    protected $csvService;

    public function __construct(CsvService $csvService)
    {
        $this->csvService = $csvService;
    }

    /**
     * 送金情報 振込依頼CSVエクスポート
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function export(GuestPaymentExportRequest $request)
    {
        $from_date = $request->from_date;
        $to_date = $request->to_date;
        $dead_line_from_date = $request->dead_line_from_date;
        $dead_line_to_date = $request->dead_line_to_date;

        $maxRows = SystemConst::MAX_CSV_ROWS;
        $systemFeeRate = SystemConst::SYSTEM_FEE_RATE;

        $where = [];

        $where[] = ['g.payment_method', PaymentMethodEnum::ADVANCE_PAYMENT];
        $where[] = ['g.settlement_amount', '>', 0];

        //決済日時
        if($from_date) {
            $where[] = [DB::raw('DATE(g.web_invite_reply_datetime)'), '>=', $from_date];
        }
        if($to_date) {
            $where[] = [DB::raw('DATE(g.web_invite_reply_datetime)'), '<=', $to_date];
        }

        // 送金期限
        if($dead_line_from_date) {
            $where[] = [DB::raw('DATE(w.scheduled_transfer_date)'), '>=', $dead_line_from_date];
        }
        if($dead_line_to_date) {
            $where[] = [DB::raw('DATE(w.scheduled_transfer_date)'), '<=', $dead_line_to_date];
        }

        $guests = DB::table('guests as g')
            ->select([
                'm.member_number as member_number',
                DB::raw("CONCAT(m.last_name, ' ', m.first_name) as member_name"),
                DB::raw("CONCAT(g.last_name, ' ', g.first_name) as guest_name"),
                'g.web_invite_reply_datetime as web_invite_reply_datetime',
                'g.total_amount as total_amount',
                'g.system_fee as guest_system_fee',
                'g.settlement_amount as settlement_amount',
                DB::raw("IF(g.is_system_fee = 1, 0, FLOOR(g.total_amount * {$systemFeeRate})) as member_system_fee"),
                DB::raw("g.settlement_amount - IF(g.is_system_fee = 1, 0, FLOOR(g.total_amount * {$systemFeeRate})) as member_settlement_amount"),
                'w.scheduled_transfer_date as scheduled_transfer_date',
            ])
            ->join('members as m', 'g.member_id', '=', 'm.id')
            ->join('web_invitations as w', 'g.web_invitation_id', '=', 'w.id')
            ->where($where)
            ->orderBy('g.web_invite_reply_datetime')
            ->orderBy('w.scheduled_transfer_date')
            ->get();

        $totalCount = count($guests);
        if ($totalCount > $maxRows) {
            return response()->json([
                'status' => 'error',
                'message' => "ダウンロード可能な行数（$maxRows 件）を超えています。現在のデータ数: $totalCount 件"
            ], 400);
        }

        $data = $guests->map(function ($item) {
            return (array) $item;
        })->toArray();

        return $this->csvService->export(
            CsvConst::GUEST_PAYMENT_CSV_EXPORT,
            $data,
            "sample_" . Carbon::now()->format("YmdHis") . ".csv"
        );
    }
}
