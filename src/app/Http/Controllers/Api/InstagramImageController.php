<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\InstagramImage;
use App\Http\Requests\InstagramImageGenerateRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Enums\InstagramTypeEnum;

class InstagramImageController extends Controller
{
    private $instagramImageService;

    public function __construct(InstagramImage $instagramImageService)
    {
        $this->instagramImageService = $instagramImageService;
    }

    /**
     * Instagram画像を生成してバイナリデータで返す（ストリーム版）
     *
     * @param InstagramImageGenerateRequest $request
     * @return Response
     */
    public function generate(InstagramImageGenerateRequest $request)
    {
        try {
            $data = $request->only('instagram_type', 'image_data', 'use_groom_bride_name', 'use_event_date');

            // Instagram画像を生成（UploadedFileから直接）
            $binaryData = $this->instagramImageService->generate(
                $data['instagram_type'],
                $data['image_data'],
                $data['use_groom_bride_name'],
                $data['use_event_date']
            );

            $fileName = $this->instagramImageService->generateUidFileName();

            // バイナリデータをレスポンスとして返す
            return response($binaryData)
                ->header('Content-Type', 'image/jpeg')
                ->header('Content-Disposition', 'inline; filename="' . $fileName . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Instagram画像の生成に失敗しました',
                'message' => $e->getMessage()
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }
}
