<?php
namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use App\Models\Member;

class MemberEmailRule implements Rule
{
    protected $sns;

    public function passes($attribute, $value)
    {
        // SNSで会員登録されてる場合
        $member = Member::findByEmail($value);
        if (!$member) {
            return true;
        }

        if (
            $member->is_regist == true &&
            $member->sns_login_id &&
            ! $member->is_use_password
        ) {
            $this->sns = array_key_first($member->sns_login_id);
            return false;
        }

        return true;
    }

    public function message()
    {
        switch ($this->sns) {
            case "line":
                return "LINEで会員登録されたアカウントです。LINEでログインしてください。";
            case "google":
                return "Googleで会員登録されたアカウントです。Googleでログインしてください。";
            default:
                return "指定されたSNSアカウントでログインしてください。";
        }
    }
}
