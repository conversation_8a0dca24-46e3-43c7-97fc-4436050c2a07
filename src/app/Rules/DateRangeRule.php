<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class DateRangeRule implements Rule
{
    protected $fromField;
    protected $toField;
    protected $errorMessage;

    public function __construct(string $fromField, string $toField, string $errorMessage)
    {
        $this->fromField = $fromField;
        $this->toField = $toField;
        $this->errorMessage = $errorMessage;
    }

    public function passes($attribute, $value)
    {
        $fromDate = request()->input($this->fromField);
        $toDate = request()->input($this->toField);

        if ($fromDate && $toDate && $fromDate > $toDate) {
            return false;
        }

        return true;
    }

    public function message()
    {
        return $this->errorMessage;
    }
}
