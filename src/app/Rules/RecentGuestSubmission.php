<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class RecentGuestSubmission implements Rule
{
    protected $email;

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        // 10秒以内の制限時間を設定
        $recentThreshold = Carbon::now()->subSeconds(10);

        // 同一メールアドレスで制限時間内のデータがあるかをチェック
        $exists = DB::table('guests')
            ->where('email', $value)
            ->where('created_at', '>=', $recentThreshold)
            ->exists();

        return !$exists; // 存在しない場合にのみパス
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return '同じメールアドレスでの回答は連続で回答できません。';
    }
}
