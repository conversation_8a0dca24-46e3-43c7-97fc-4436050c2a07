<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Imagick;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;


//ファイルサイズのリサイズと圧縮
class ResizeAndCompressImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * タイムアウトになる前にジョブを実行できる秒数
     *
     * @var int
     */
    public $timeout = 500;
    public $tries = 3; // 試行回数

    protected $storage;
    protected $tempFile;
    protected $dirPath;
    protected $fileName;
    protected $fileDataArray;

    /**
     * Create a new job instance.
     * @param array  $fileDataArray [[size => サイズ(s・lなど), length => 長辺の長さ, quality => 圧縮度]]
     * @param string $tempFile 一時ファイルのパス
     * @param string $dirPath リサイズと圧縮したいディレクトリパス
     * @param string $fileName ファイル名
     *
     * @return void
     */
    public function __construct($fileDataArray, $tempFile, $dirPath, $fileName)
    {
        $this->fileDataArray = $fileDataArray;
        $this->tempFile = $tempFile;
        $this->dirPath = $dirPath;
        $this->fileName = $fileName;
    }
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // ファイルの拡張子を取得
        $extension = pathinfo($this->tempFile, PATHINFO_EXTENSION);

        // S3からファイルストリームを取得
        foreach ($this->fileDataArray as $row) {
            $fileStream = Storage::disk("s3")
                ->getDriver()
                ->readStream($this->tempFile);

            // Imagickオブジェクトをストリームから作成
            $imagick = new Imagick();
            $imagick->readImageFile($fileStream);

            // 画像の現在の寸法を取得
            $width = $imagick->getImageWidth();
            $height = $imagick->getImageHeight();

            // 長辺を計算
            $longestSide = max($width, $height);

            // 現在の色空間を取得
            // $currentColorspace = $imagick->getColorspace();
            //サイズ指定分ループ
            // $imagick->setImageColorspace($currentColorspace);

            //長辺が$length以上ならリサイズ
            if ($longestSide > $row["length"]) {
                // カラースペースを RGB に設定
                // $imagick->setImageColorspace(\Imagick::COLORSPACE_RGB);

                // 長辺が定数の値になるようにリサイズするための比率を計算
                $scale = $row["length"] / $longestSide;

                // 新しい幅と高さを計算
                $newWidth = (int) round($width * $scale);
                $newHeight = (int) round($height * $scale);
                // 画像をリサイズ
                $imagick->resizeImage(
                    $newWidth,
                    $newHeight,
                    Imagick::FILTER_LANCZOS,
                    0.9891028367558475
                );
                // $imagick->setImageColorspace(Imagick::COLORSPACE_SRGB);
            }

            // 圧縮の品質を設定
            $imagick->setImageCompressionQuality($row["quality"]);

            //ファイル名を変更して保存
            $newFileName =
                $this->dirPath .
                "/" .
                $this->fileName .
                "_" .
                $row["size"] .
                "." .
                $extension;
            $resizedFile = $imagick->getImageBlob();

            Storage::disk("s3")->put(
                $newFileName,
                $resizedFile,
            );

            // リソース解放
            $imagick->clear();
            $imagick->destroy();

        }
        // ストリームを解放
        fclose($fileStream);

        // S3の一時ファイルを削除
        Storage::disk("s3")->delete($this->tempFile);
    }
}
