<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

//Web招待状の回答受付(ゲスト向け)
class WebInvitationGuestCompleteGuestMail extends Mailable
{
    use Queueable, SerializesModels;

    public $mailList;

    /**
     * $mailList = [
     *     "attendance" => 出席情報,
     *     "last_name"  => 筆頭者姓,
     *     "first_name" => 筆頭者名,
     *     "groom_full_name" => 新郎姓,
     *     "bride_full_name" => 新婦性,
     *     "scheduled_date" => 開催日
     *     "googleUrl" => GoogleカレンダーURL,
     *     "publicUrl" => 公開URL,
     *     "paymentMethod" => お支払い種別,
     *     "guests" => [連名者],
     *     "payment_list" => [
     *       "name" => "ゲスト名"
     *       "settlement_amount" => "決済金額"
     *     ]
     * ];
     * @return void
     */
    public function __construct(array $mailList)
    {
        $this->mailList = $mailList;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: "【WEB招待状】出欠登録を受け付けました！",
            bcc: [config("mail.bcc.address")]
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(html: "emails.web_invitation_guest_complete_guest");
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
