<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

//お問い合わせの受付メール（管理者宛）
class ContactAdminMail extends Mailable
{
    use Queueable, SerializesModels;

    public $first_name;
    public $last_name;
    public $first_name_kana;
    public $last_name_kana;
    public $email;
    public $phone;
    public $content;
    public $alternate_member_number;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        $first_name,
        $last_name,
        $first_name_kana,
        $last_name_kana,
        $email,
        $phone,
        $content,
        $alternate_member_number
    ) {
        $this->first_name = $first_name;
        $this->last_name = $last_name;
        $this->first_name_kana = $first_name_kana;
        $this->last_name_kana = $last_name_kana;
        $this->email = $email;
        $this->phone = $phone;
        $this->content = $content;
        $this->alternate_member_number = $alternate_member_number;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: "★WEB招待状　お客様からお問い合わせをいただきました"
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(html: "emails.contact_admin");
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
