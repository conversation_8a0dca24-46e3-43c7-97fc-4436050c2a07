<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

//事前支払い 送金完了のご案内メール
class AdvancePaymentCompleteMail extends Mailable
{
    use Queueable, SerializesModels;

    public $last_name;
    public $first_name;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($last_name, $first_name)
    {
        $this->last_name = $last_name;
        $this->first_name = $first_name;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: "【favori WEB招待状】ご祝儀の送金手続き完了のご報告",
            bcc: [config("mail.bcc.address")]
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(view: "emails.advance_payment_complete");
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
