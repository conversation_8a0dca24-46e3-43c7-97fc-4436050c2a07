<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

//お問い合わせの受付メール（ユーザー向け）
class ContactMemberMail extends Mailable
{
    use Queueable, SerializesModels;

    public $first_name;
    public $last_name;
    public $first_name_kana;
    public $last_name_kana;
    public $email;
    public $phone;
    public $content;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        $first_name,
        $last_name,
        $first_name_kana,
        $last_name_kana,
        $email,
        $phone,
        $content
    ) {
        $this->first_name = $first_name;
        $this->last_name = $last_name;
        $this->first_name_kana = $first_name_kana;
        $this->last_name_kana = $last_name_kana;
        $this->email = $email;
        $this->phone = $phone;
        $this->content = $content;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: "【favori WEB招待状】お問い合わせ受け付けのお知らせ",
            bcc: [config("mail.bcc.address")]
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(html: "emails.contact_member");
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
