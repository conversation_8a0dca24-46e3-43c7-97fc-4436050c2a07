<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class ExceptionReport extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    private string $plainBody;
    private string $appName;

    public function __construct(
        string $plainBody,
        string $appName
    )
    {
        $this->plainBody = $plainBody;
        $this->appName = $appName;
    }

    public function build(): self
    {
        return $this
                    ->mailer('smtp')
                    ->from(config('mail.from_developer_address'))
                    ->subject('[Favori 例外通知] '.$this->appName)
                    ->view('emails.exception_plain')
                    ->with(['plainBody' => $this->plainBody]);
    }
}
