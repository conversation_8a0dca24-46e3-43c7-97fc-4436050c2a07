<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

//お問い合わせの受付メール（ユーザー向け）
class ChangeEmailMail extends Mailable
{
    use Queueable, SerializesModels;

    public $email;
    public $member;
    public $url;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        $email,
        $member,
        $url
    ) {
        $this->email = $email;
        $this->member = $member;
        $this->url = $url;
    }

    /**
     * Get the message envelope.
     *
     * @return \Illuminate\Mail\Mailables\Envelope
     */
    public function envelope()
    {
        return new Envelope(
            subject: "【favori WEB招待状】メールアドレス変更URL発行のご案内",
            bcc: [config("mail.bcc.address")]
        );
    }

    /**
     * Get the message content definition.
     *
     * @return \Illuminate\Mail\Mailables\Content
     */
    public function content()
    {
        return new Content(html: "emails.change_email");
    }

    /**
     * Get the attachments for the message.
     *
     * @return array
     */
    public function attachments()
    {
        return [];
    }
}
