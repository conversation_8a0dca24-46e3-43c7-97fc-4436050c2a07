<?php

namespace App\Consts;

class CsvConst
{
    //楽天ヘッダー
    public const RAKUTEN_HEADER_EXPORT = [
        "サービス区分",
        "実行日",
        "受取人銀行番号",
        "受取人支店番号",
        "受取人預金種目",
        "受取人口座番号",
        "受取人口座名",
        "金額",
        "顧客番号",
    ];

    //楽天ヘッダーサービス区分の値
    public const RAKUTEN_SERVICE_TYPE = 3;

    //管理画面送金CSVインポートヘッダー(出金明細CSV)
    public const CSV_TRANSFER_IMPORT_NAME = "TRANSFER_IMPORT";
    public const CSV_TRANSFER_IMPORT = [
        "取引日",
        "入出金(円)",
        "残高(円)",
        "入出金先内容",
    ];

    //管理画面送金CSVインポートヘッダー(総合振込 依頼ダウンロードCSV)
    public const CSV_GENERAL_TRANSFER_IMPORT_NAME = "GENERAL_TRANSFER_IMPORT";
    public const CSV_GENERAL_TRANSFER_IMPORT = [
        "サービス区分",
        "実行日",
        "受取人銀行番号",
        "受取人支店番号",
        "受取人預金種目",
        "受取人口座番号",
        "受取人口座名",
        "金額",
        "顧客番号",
        "登録者ユーザID",
        "登録者氏名"
    ];

    // ゲスト決済ダウンロードヘッダー
    public const GUEST_PAYMENT_CSV_EXPORT = [
        "会員番号",
        "会員氏名",
        "ゲスト名",
        "決済日時（WEB招待状回答日時）",
        "会費・ご祝儀金額",
        "システム利用料(ゲスト負担分)",
        "決済金額",
        "システム利用料(会員負担分)",
        "会員受取額(振込手数料除く)",
        "送金期限(送金予定日)",
    ];
}
