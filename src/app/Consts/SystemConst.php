<?php

namespace App\Consts;

class SystemConst
{
    const ADVANCE_PAYMENT_CLOSING_DAY = 7; // 事前支払い締め日（日数）
    const TRANSFER_DUE_BEFORE_BUSINESS_DAYS = 1; // 送金日の日数（送金期限の前営業日）
    const SYSTEM_FEE_RATE = 0.031; // システム手数料率 (3%)
    const TRANSFER_FEE = 660; // 振込手数料
    const GUEST_EVENT_ATTNDANCE_ALL = "すべて"; //出席すべて
    const DEFAULT_GUEST_LIST_NAME = "メインリスト"; //会員登録時のデフォルトゲストリスト名
    const MAIL_SEND_DUE_DATE = 3; //銀行口座やパーティ情報など送信メール間近の日数
    const MAIL_SEND_LIMIT_PENDING_GUEST_REMINDER_DATE = 7; //回答締切日のリマインドメール（保留中のゲスト）送信メール間近の日数

    //会員情報
    const MEMBER_TMP_UUID_EXPIRES_AT = 24;//有効期限(24時間後)

    //パーティ情報
    const WEDDING_RECEPTION_SAME_VENUE = "ceremony_reception_same_venue"; //挙式・披露宴(会場が同じ)'
    const WEDDING_RECEPTION_SEPARATE_VENUE = "ceremony_reception_separate_venue"; //挙式・披露宴(会場が別)
    const WEDDING_ONLY = "ceremony"; //挙式のみ
    const RECEPTION_ONLY = "reception"; //披露宴のみ
    const AFTER_PARTY_15 = "1.5_party"; //1.5次会
    const SECOND_PARTY_2 = "2_party"; //2次会
    const OTHER_PARTIES = "other_party"; //パーティ、その他の披露宴 eventNameから取得

    const PARTY_INFO_LIST = [
        self::WEDDING_RECEPTION_SAME_VENUE => "挙式・披露宴",
        self::WEDDING_RECEPTION_SEPARATE_VENUE => ["挙式", "披露宴"],
        self::WEDDING_ONLY => "挙式",
        self::RECEPTION_ONLY => "披露宴",
        self::AFTER_PARTY_15 => "1.5次会",
        self::SECOND_PARTY_2 => "2次会",
    ];

    // Boolean 型の定数
    const TRUE_CONSTANT = true; // 真
    const FALSE_CONSTANT = false; // 偽

    //圧縮率
    const QUALITY = 70;

    //サイズ別画像サイズ
    const LENGTH_S = 480;
    const LENGTH_M = 1440;
    const LENGTH_L = 2880;

    //画像サイズ
    const IMAGE_SIZES = [
        "small" => [
            "size" => "s",
            "length" => self::LENGTH_S,
            "quality" => self::QUALITY,
        ],
        "medium" => [
            "size" => "m",
            "length" => self::LENGTH_M,
            "quality" => self::QUALITY,
        ],
        "large" => [
            "size" => "l",
            "length" => self::LENGTH_L,
            "quality" => self::QUALITY,
        ],
    ];

    //JSONファイルのパス
    const EXAMPLE_CONTENT_SEASON_JSON = "data/example_content_season.json"; //時候の句

    // 動画関連の拡張子リスト
    const VIDEO_EXTENSIONS = ["mp4", "avi", "mkv", "mov", "flv", "wmv", "webm"];

    // CSSコード予約語
    const CSS_CODE_KEYWORD = "{{WebinvitationId}}";

    // csvの最大行数
    const MAX_CSV_ROWS = 1000;

    //決済ログ種別
    const PAYMENT_LOG_3D_INPUT_PARAMETER = 1; //入力パラメータ(3Dセキュア)
    const PAYMENT_LOG_3D_ENTRY = 2; //取引登録(3Dセキュア)
    const PAYMENT_LOG_3D_EXEC = 3; // 取引開始(3Dセキュア)

    const PAYMENT_LOG_WEB_INVITAION_INPUT_PARAMETER = 4; //入力パラメータ(web招待状)
    const PAYMENT_LOG_WEB_INVITAION_GUEST_PRE_REGISTRATION = 5; //ゲスト登録前
    const PAYMENT_LOG_WEB_INVITAION_PAYMENT_COMPLETED = 6; // 決済済み
    const PAYMENT_LOG_WEB_INVITAION_SECURE_3D = 7; // 3Dセキュア
    const PAYMENT_LOG_WEB_INVITAION_API_RESULT = 8; // API結果

    const PAYMENT_LOG_3D_ERROR = 98; // 例外エラー(3Dセキュア)
    const PAYMENT_LOG_WEB_INVITAION_ERROR = 99; // 例外エラー(web招待状)
}
