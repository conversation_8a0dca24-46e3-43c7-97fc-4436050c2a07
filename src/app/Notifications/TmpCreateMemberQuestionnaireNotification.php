<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

//会員仮登録メール
class TmpCreateMemberQuestionnaireNotification extends Notification
{
    use Queueable;

    private string $tempUuid;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($tempUuid)
    {
        $this->tempUuid = $tempUuid;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ["mail"];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $url = $this->createUrl();
        // return (new MailMessage())
        //     ->line("仮登録完了")
        //     ->action("Notification Action", $url);

        return (new MailMessage())
            ->subject("【favori WEB招待状】【重要】メールアドレス認証のお願い")
            ->view("emails.tmp_create_member_questionnaire", ["url" => $url])
            ->bcc([config("mail.bcc.address")]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
                //
            ];
    }

    // フロントエンド側で用意したリセットURLの生成ロジックを記述
    protected function createUrl()
    {
        $frontendResetUrl = env("MAIL_FRONT_URL_TMP_MEMBER");
        return $frontendResetUrl . "/" . $this->tempUuid;
    }
}
