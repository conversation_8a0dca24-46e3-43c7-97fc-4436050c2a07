<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

//会員本登録完了メール
class CreateMemberNotification extends Notification
{
    use Queueable;

    private $email;
    private $alternate_member_number;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct($email, $alternate_member_number)
    {
        $this->email = $email;
        $this->alternate_member_number = $alternate_member_number;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ["mail"];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        return (new MailMessage())
            ->subject(
                "【favori WEB招待状】favoriへようこそ！（本登録完了のお知らせ）"
            )
            ->view("emails.create_member", [
                "email" => $this->email,
                "alternate_member_number" => $this->alternate_member_number,
            ])
            ->bcc([config("mail.bcc.address")]);
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
                //
            ];
    }
}
