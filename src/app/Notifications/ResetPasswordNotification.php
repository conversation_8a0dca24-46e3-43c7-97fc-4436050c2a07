<?php

namespace App\Notifications;

use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;

//パスワード再設定メール
class ResetPasswordNotification extends ResetPassword
{
    public function toMail($notifiable)
    {
        $this->setEmailToken($notifiable->getEmailForPasswordReset());
        $token = $this->getResetToken($notifiable->getEmailForPasswordReset());
        $url = $this->createUrl($notifiable, $token);

        return (new MailMessage())
            ->subject("【favori WEB招待状】パスワード再設定")
            ->view("emails.reset_password", ["url" => $url])
            ->bcc([config("mail.bcc.address")]);
    }

    // フロントエンド側で用意したリセットURLの生成ロジックを記述
    protected function createUrl($notifiable, $token)
    {
        $frontendResetUrl = env("MAIL_FRONT_URL_RESET");
        return $frontendResetUrl . "/" . $this->token;
    }

    // データベースからトークンを取得
    protected function getResetToken($email)
    {
        return DB::table("password_resets")
            ->where("email", $email)
            ->first()->token;
    }

    //URLに渡すトークンを保存
    protected function setEmailToken($email)
    {
        DB::table("password_resets")
            ->where("email", $email)
            ->update(["email_token" => $this->token]);
    }
}
