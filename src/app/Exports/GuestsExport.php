<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Carbon\Carbon;
use App\Models\Guest;
use App\Enums\GuestTypeEnum;
use App\Enums\GenderEnum;
use App\Enums\PaymentMethodEnum;

class GuestsExport implements FromCollection, WithHeadings, WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function title(): string
    {
        return "ゲストリスト";
    }

    public function collection()
    {
        // Eager Loadingで関連データを一括取得
        $guestListModels = Guest::with([
            'guest_group',
            'guest_tags',
            'guest_free_item_values',
            'guest_survey_answers',
            'guest_event_answers',
            'web_invitation',
            'parent_guest',
            'children_guests'
        ])->where($this->data)->get();

        //筆頭者ゲスト情報を取得
        $guestLists = [];

        //連名者ゲスト情報を取得
        $childGuestList = [];
        foreach ($guestListModels as $guest) {
            if (empty($guest["parent_guest_id"])) {
                $guestLists[] = $guest;
            } else {
                array_unshift($childGuestList, $guest);
            }
        }

        //連名者を筆頭者の直後に追加
        foreach ($childGuestList as $childGuest) {
            $index = array_search(
                $childGuest["parent_guest_id"],
                array_column($guestLists, "id")
            );
            if ($index !== false) {
                array_splice($guestLists, $index + 1, 0, [$childGuest]);
            }
        }

        $items = [];
        foreach ($guestLists as $guest) {
            $item = [];
            for ($i = 0; $i <= 68; $i++) {
                $item[$i] = "";
            }
            if ($guest->guest_type == GuestTypeEnum::GROOM) {
                $item[0] = "新郎ゲスト";
            } elseif ($guest->guest_type == GuestTypeEnum::BRIDE) {
                $item[0] = "新婦ゲスト";
            }
            $item[1] = $guest->last_name;
            $item[2] = $guest->first_name;
            $item[3] = $guest->last_name_kana;
            $item[4] = $guest->first_name_kana;
            $item[5] = $guest->last_name_romaji;
            $item[6] = $guest->first_name_romaji;
            $item[7] = $guest->guest_honor;
            if ($guest->gender == GenderEnum::ETC) {
                $item[8] = "その他";
            } elseif ($guest->gender == GenderEnum::MALE) {
                $item[8] = "男性";
            } elseif ($guest->gender == GenderEnum::FEMALE) {
                $item[8] = "女性";
            }
            $item[9] = $guest->relationship_name;
            $item[10] = $guest->relationship;
            $item[11] = $guest->guest_title;
            if ($guest->guest_group) {
                $item[12] = $guest->guest_group->name;
            }
            $tags = [];
            foreach ($guest->guest_tags as $guest_tag) {
                $tags[] = $guest_tag->tag;
            }
            $item[13] = implode(",", $tags);
            $allergyArray = !empty($guest->allergies) ? $guest->allergies : [];
            $allergyString = implode(
                ",",
                array_filter([
                    implode(",", $allergyArray),
                    str_replace(
                        ["\r\n", "\r", "\n"],
                        " ",
                        $guest->allergy ?? ""
                    ),
                ])
            );
            $item[14] = $allergyString;
            $item[15] = "";
            if ($guest->birthdate) {
                $item[15] = Carbon::parse($guest->birthdate)->format("Y/m/d");
            }
            $item[16] = $guest->postal_code;
            $item[17] = $guest->prefecture;
            $item[18] = $guest->city;
            $item[19] = $guest->address;
            $item[20] = $guest->building;
            $item[21] = $guest->phone;
            $item[22] = $guest->email;
            $item[23] =
                $guest->parent_guest_id || count($guest->children_guests)
                    ? "あり"
                    : "なし";
            if ($guest->parent_guest) {
                $item[24] =
                    $guest->parent_guest->last_name .
                    " " .
                    $guest->parent_guest->first_name;
            }
            foreach (
                $guest->guest_free_item_values
                as $i => $guest_free_item_value
            ) {
                $item[25 + $i * 2] = $guest_free_item_value->name;
                $item[26 + $i * 2] = $guest_free_item_value->content;
                if ($i >= 4) {
                    break;
                }
            }
            if ($guest->invitation_delivery == 1) {
                $item[35] = "Web招待状";
            }
            if ($guest->web_invitation) {
                $item[36] = $guest->web_invitation->name;
            }
            if ($guest->web_invite_reply_datetime) {
                $item[37] = Carbon::parse(
                    $guest->web_invite_reply_datetime
                )->format("Y/m/d H:i:s");
            }
            $item[38] = $guest->message;
            foreach (
                $guest->guest_survey_answers
                as $i => $guest_survey_answer
            ) {
                $item[39 + $i * 2] = $guest_survey_answer->question;
                $item[40 + $i * 2] = $guest_survey_answer->answer_content;
                if ($i >= 4) {
                    break;
                }
            }

            if ($guest->web_invitation) {
                if (isset($guest->web_invitation->editor_settings["blocks"])) {
                    foreach (
                        $guest->web_invitation->editor_settings["blocks"]
                        as $block
                    ) {
                        if ($block["id"] == "information") {
                            if (isset($block["contents"]["date"])) {
                                if ($block["contents"]["date"]) {
                                    $item[49] = Carbon::parse(
                                        $block["contents"]["date"]
                                    )->format("Y/m/d");
                                }
                            }
                            break;
                        }
                    }
                }
            }
            if ($guest->payment_method == PaymentMethodEnum::ADVANCE_PAYMENT) {
                $item[50] = "事前支払い";
            } elseif (
                $guest->payment_method == PaymentMethodEnum::BRING_ON_THE_DAY
            ) {
                $item[50] = "持参する";
            } elseif ($guest->payment_method == PaymentMethodEnum::PAID) {
                $item[50] = "支払い済み";
            }
            foreach ($guest->guest_event_answers as $i => $guest_event_answer) {
                $item[51 + $i * 3] = $guest_event_answer->name;
                $item[52 + $i * 3] = $guest_event_answer->attendance;
                $item[53 + $i * 3] = $guest_event_answer->payment_amount;
                if ($i >= 4) {
                    break;
                }
            }
            $item[66] = $guest->gift_amount;
            $item[67] = $guest->system_fee;
            $item[68] = $guest->settlement_amount;
            $items[] = $item;
        }
        return new Collection($items);
    }
    public function headings(): array
    {
        return [
            "ゲストタイプ",
            "姓",
            "名",
            "姓（かな）",
            "名（かな）",
            "姓（ローマ字）",
            "名（ローマ字）",
            "敬称",
            "性別",
            "関係性",
            "間柄",
            "肩書",
            "グループ",
            "タグ",
            "アレルギー等",
            "生年月日",
            "郵便番号",
            "都道府県",
            "市区町村",
            "丁目・番地",
            "建物名・部屋番号など",
            "電話番号",
            "メールアドレス",
            "連名ゲスト有無",
            "筆頭者氏名",
            "フリー項目名1",
            "フリー項目内容1",
            "フリー項目名2",
            "フリー項目内容2",
            "フリー項目名3",
            "フリー項目内容3",
            "フリー項目名4",
            "フリー項目内容4",
            "フリー項目名5",
            "フリー項目内容5",
            "招待状お届け方法",
            "回答したWeb招待状",
            "Web招待状回答日時",
            "お祝いメッセージ",
            "アンケート質問1",
            "アンケート回答1",
            "アンケート質問2",
            "アンケート回答2",
            "アンケート質問3",
            "アンケート回答3",
            "アンケート質問4",
            "アンケート回答4",
            "アンケート質問5",
            "アンケート回答5",
            "パーティー開催日",
            "支払い方法",
            "パーティー1名称",
            "パーティー1出欠",
            "パーティー1事前支払額",
            "パーティー2名称",
            "パーティー2出欠",
            "パーティー2事前支払額",
            "パーティー3名称",
            "パーティー3出欠",
            "パーティー3事前支払額",
            "パーティー4名称",
            "パーティー4出欠",
            "パーティー4事前支払額",
            "パーティー5名称",
            "パーティー5出欠",
            "パーティー5事前支払額",
            "事前支払いお気持ち",
            "事前支払い手数料負担額",
            "事前支払い合計",
        ];
    }
}
