<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Carbon\Carbon;
use App\Models\Guest;

class GuestsFavoriCloudSeatExport implements
    FromCollection,
    WithHeadings,
    WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function title(): string
    {
        return "favori CLOUD席次表ゲスト";
    }

    public function collection()
    {
        $items = [];
        $guests = Guest::where($this->data)->get();
        foreach ($guests as $guest) {
            $item = [];
            $item[0] = $guest->guest_title;
            $item[1] = "";
            $item[2] = "";
            $item[3] = $guest->last_name;
            $item[4] = $guest->first_name;
            $item[5] = $guest->guest_honor;
            $items[] = $item;
        }
        return new Collection($items);
    }
    public function headings(): array
    {
        return ["肩書1行目", "肩書2行目", "肩書3行目", "氏", "名", "敬称"];
    }
}
