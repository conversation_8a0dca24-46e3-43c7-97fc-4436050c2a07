<?php

namespace App\Exports;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Carbon\Carbon;
use App\Models\Guest;

class GuestsFavoriCloudAddressExport implements
    FromCollection,
    WithHeadings,
    WithTitle
{
    protected $data;

    public function __construct(array $data)
    {
        $this->data = $data;
    }

    public function title(): string
    {
        return "favori CLOUD宛名リスト";
    }

    public function collection()
    {
        $data["parent_guest_id"] = null;
        $items = [];
        $guests = Guest::where($this->data)->get();
        foreach ($guests as $guest) {
            $item = [];
            $item[0] = $guest->last_name;
            $item[1] = $guest->first_name;
            $item[2] = $guest->guest_honor;
            $item[3] = $guest->postal_code;
            $item[4] = $guest->prefecture . $guest->city . $guest->address;
            $item[5] = $guest->building;
            // 6-17
            $item[6] = "";
            $item[7] = "";
            $item[8] = "";
            $item[9] = "";
            $item[10] = "";
            $item[11] = "";
            $item[12] = "";
            $item[13] = "";
            $item[14] = "";
            $item[15] = "";
            $item[16] = "";
            $item[17] = "";
            foreach ($guest->children_guests as $i => $children_guest) {
                $item[6 + $i * 3] = $children_guest->last_name;
                $item[7 + $i * 3] = $children_guest->first_name;
                $item[8 + $i * 3] = $children_guest->guest_honor;
                if ($i >= 3) {
                    break;
                }
            }
            $item[18] = "";
            if ($guest->guest_group) {
                $item[18] = $guest->guest_group->name;
            }
            $items[] = $item;
        }
        return new Collection($items);
    }
    public function headings(): array
    {
        return [
            "氏",
            "名",
            "敬称",
            "郵便番号",
            "住所1行目",
            "住所2行目",
            "連名1氏",
            "連名1名",
            "連名1敬称",
            "連名2氏",
            "連名2名",
            "連名2敬称",
            "連名3氏",
            "連名3名",
            "連名3敬称",
            "連名4氏",
            "連名4名",
            "連名4敬称",
            "グループ",
        ];
    }
}
