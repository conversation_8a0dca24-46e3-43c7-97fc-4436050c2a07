<?php

namespace App\Values;

use Carbon\CarbonImmutable;

class DateOperator
{
    /**
     * 事前決済の利用可否を判定する関数
     * @param string|null $from 開始日
     * @param string|null $to 終了日
     * @return bool 利用可能な場合はtrue、そうでない場合はfalse
     */
    public static function canUsePrepaymentByDateRange(?string $from, ?string $to): bool
    {
        $fromAt = $from ? CarbonImmutable::parse($from) : null;
        $toAt   = $to ? CarbonImmutable::parse($to) : null;

        // 両方 null → OK
        if (is_null($fromAt) && is_null($toAt)) {
            return true;
        }

        // from が未来 → OK（to が null or 未来でも）
        if (!is_null($fromAt) && $fromAt->isFuture()) {
            return true;
        }

        // from が過去 or 現在
        if (!is_null($fromAt) && $fromAt->isPast()) {
            // to が null → NG（終了予定なし、現在停止中）
            if (is_null($toAt)) {
                return false;
            }

            // to が未来 → NG（まだ停止中）
            if ($toAt->isFuture()) {
                return false;
            }

            // to が過去 → OK（停止期間終了）
            if ($toAt->isPast()) {
                return true;
            }
        }

        return true;
    }
}
