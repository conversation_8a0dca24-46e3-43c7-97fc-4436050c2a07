<?php

namespace App\Models;

use App\Models\GuestGroup;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Notifications\ResetPasswordNotification;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Builder;
use App\Enums\FamilyProfileTypeEnum;
use Illuminate\Support\Facades\DB;
use App\Enums\PaymentMethodEnum;
/**
 * 会員モデル
 */
class Member extends Authenticatable
{
    use HasFactory;
    use SoftDeletes;
    use Notifiable;
    use HasApiTokens;
    use Notifiable;
    use HasUuids;

    public const ALTERNATE_MEMBER_NUMBER_PREFIX = 'fa';

    //会員論理削除時に結婚式情報も削除
    protected static function boot()
    {
        parent::boot();
        self::deleting(function ($member) {
            $member->wedding_info()->delete();
        });
    }

    public static $token_ability = "role:member";

    protected $fillable = [
        "last_name",
        "first_name",
        "last_name_kana",
        "first_name_kana",
        "last_name_romaji",
        "first_name_romaji",
        "email",
        "password",
        "sns_login_id",
        "is_use_password",
        "birthdate",
        "member_number",
        "alternate_member_number",
        "is_regist",
        "tmp_uuid",
        "tmp_uuid_expires_at",
        "bank_account_verification_sent_at",
    ];

    /** ユーザ変更禁止カラム */
    // protected $guarded = ["created_at", "updated_at"];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = ["password"];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        "email_verified_at" => "datetime",
        "sns_login_id" => "json",
        "tmp_uuid_expires_at" => "datetime",
    ];

    public function setPasswordAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes["password"] = Hash::make($value);
        }
    }

    // 新会員番号を生成する
    public static function generateUniqueFaId(): string
    {
        do {
            $num8 = str_pad(mt_rand(0, ********), 8, '0', STR_PAD_LEFT);
            $candidate = self::ALTERNATE_MEMBER_NUMBER_PREFIX . $num8;
            $exists = self::where('alternate_member_number', $candidate)->exists();
        } while ($exists);

        return $candidate;
    }

    /**
     * 新郎の姓名を取得
     *
     * @return string|null
     */
    public function getGroomFamilyFullNameAttribute(): ?string
    {
        $groomFamily = $this->family_profiles()
            ->where("type", FamilyProfileTypeEnum::GROOM)
            ->first();

        if ($groomFamily) {
            return $groomFamily->last_name . " " . $groomFamily->first_name;
        }

        return null;
    }

    /**
     * 新郎と新婦情報を取得
     */
    public function getGroomAndBrideAttribute()
    {
        return $this->family_profiles()
            ->whereIn('type', [FamilyProfileTypeEnum::GROOM, FamilyProfileTypeEnum::BRIDE])
            ->orderBy('type', 'asc')
            ->get();
    }

    /**
     * 新婦の姓名を取得
     *
     * @return string|null
     */
    public function getBrideFamilyFullNameAttribute(): ?string
    {
        $brideFamily = $this->family_profiles()
            ->where("type", FamilyProfileTypeEnum::BRIDE)
            ->first();

        if ($brideFamily) {
            return $brideFamily->last_name . " " . $brideFamily->first_name;
        }

        return null;
    }

    /**
     * 新郎のローマ字の姓名を取得
     *
     * @return string|null
     */
    public function getGroomFamilyRomajiFullNameAttribute(): ?string
    {
        $groomFamily = $this->family_profiles()
            ->where("type", FamilyProfileTypeEnum::GROOM)
            ->first();

        if ($groomFamily) {
            return $groomFamily->last_name_romaji . " " . $groomFamily->first_name_romaji;
        }

        return null;
    }

    /**
     * 新婦のローマ字の姓名を取得
     *
     * @return string|null
     */
    public function getBrideFamilyRomajiFullNameAttribute(): ?string
    {
        $brideFamily = $this->family_profiles()
            ->where("type", FamilyProfileTypeEnum::BRIDE)
            ->first();

        if ($brideFamily) {
            return $brideFamily->last_name_romaji . " " . $brideFamily->first_name_romaji;
        }

        return null;
    }

    /**
     * 開催日を取得
     *
     * @return string|null
     */
    public function getEventDateAttribute(): ?string
    {
        $weddingInfo = $this->wedding_info;
        if ($weddingInfo && $weddingInfo->wedding_date) {
            return $weddingInfo->wedding_date;
        }

        return null;
    }

    /**
     * 会員番号を0埋め八桁で取得
     *
     * @return string
     */
    public function getNumberAttribute()
    {
        return str_pad(strval($this->member_number), 8, "0", STR_PAD_LEFT);
    }

    /**
     * 口座登録アラートフラグ
     *
     * @return boolean
     */
    public function getIsAccountAlertRegistAttribute()
    {
        $hasPaymentType1 = $this->guests()
            ->withTrashed()
            ->where("payment_method", PaymentMethodEnum::ADVANCE_PAYMENT)
            ->exists();

        if ($hasPaymentType1) {
            $hasAccount = $this->member_bank_account()
                ->withTrashed()
                ->exists();
            if (!$hasAccount) {
                return true;
            }
        }
        return false;
    }

    public function validPassword(string $password): bool
    {
        return Hash::check($password, $this->password);
    }

    /**
     * パスワードリセット通知をユーザーに送信
     *
     * @param  string  $token
     * @return void
     */
    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }

    // 認証トークンの発行
    public function createAuthToken(): string
    {
        return $this->createToken("authToken", [self::$token_ability])
            ->plainTextToken;
    }

    public static function findByEmail(string $email): ?Member
    {
        return self::where("email", $email)->first();
    }
    public static function findBySns(string $sns, string $sns_id): ?Member
    {
        return self::whereJsonContains(
            "sns_login_id->" . $sns,
            $sns_id
        )->first();
    }

    /**
     * 姓と名を結合して部分一致検索を行うスコープ
     *
     * @param Builder $query
     * @param string $name
     * @return Builder
     */
    public function scopeFullName(Builder $query, $name)
    {
        return $query->where(function ($query) use ($name) {
            $query->whereRaw("CONCAT(last_name, ' ', first_name) LIKE ?", [
                "%{$name}%",
            ]);
        });
    }

    /**
     * 会員番号、部分一致検索を行うスコープ
     *
     * @param Builder $query
     * @param string $number
     * @return Builder
     */
    public function scopeNumber(Builder $query, $number)
    {
        return $query
            ->where(
                DB::raw('LPAD(member_number, 8, "0")'),
                "LIKE",
                "%{$number}%"
            )->orWhere(
                'alternate_member_number',
                "LIKE",
                "%{$number}%"
            );
    }

    /**
     * 論理削除されたレコードも含めるスコープ
     *
     * @param $query
     * @param int $is_active
     * @return Builder
     */
    public function scopeIsActive($query, $is_active): Builder
    {
        if (!empty($is_active)) {
            return $query->withTrashed();
        }
        return $query;
    }

    /**
     * 会員登録完了済みのレコードのみを取得するスコープ
     *
     * @param Builder $query
     * @param bool $is_regist
     * @return Builder
     */
    public function scopeIsRegist(Builder $query, $is_regist = true): Builder
    {
        return $query->where('is_regist', $is_regist);
    }

    /**
     * ゲストグループへのリレーション：多対１
     */
    public function guest_groups(): HasMany
    {
        return $this->hasMany(GuestGroup::class, "member_id");
    }

    /**
     * ゲストリストへのリレーション：多対１
     */
    public function guest_lists(): HasMany
    {
        return $this->hasMany(GuestList::class, "member_id");
    }

    /**
     * ゲストへのリレーション：多対１
     */
    public function guests(): HasMany
    {
        return $this->hasMany(Guest::class, "member_id");
    }

    //手配前引き出物ラッピングへのリレーション: 1対多
    public function gift_wrappings(): HasMany
    {
        return $this->hasMany(GiftWrapping::class, "member_id");
    }

    //手配前引き出物送付先情報へのリレーション: 1対多
    public function gift_shipping_infos(): HasMany
    {
        return $this->hasMany(GiftShippingInfo::class);
    }

    //手配前引き出物送付先情報へのリレーション: 1対多
    public function gift_arrangement_histories(): HasMany
    {
        return $this->hasMany(GiftArrangementHistory::class);
    }

    //ゲストフリー項目値
    public function guest_free_item_values(): HasMany
    {
        return $this->hasMany(GuestFreeItemValue::class, "member_id");
    }

    //ゲストタグ
    public function guest_tags(): HasMany
    {
        return $this->hasMany(GuestTag::class, "member_id");
    }

    //会員登録時アンケート情報
    public function member_regist_questionnaires(): HasMany
    {
        return $this->hasMany(MemberRegistQuestionnaire::class, "member_id");
    }

    //結婚式情報
    public function wedding_info(): HasOne
    {
        return $this->HasOne(WeddingInfo::class, "member_id");
    }

    //会員銀行口座
    public function member_bank_account(): HasOne
    {
        return $this->hasOne(MemberBankAccount::class, "member_id");
    }

    //送金
    public function moneyTransfers(): HasMany
    {
        return $this->hasMany(MoneyTransfer::class, "member_id");
    }

    //家族プロフィール
    public function family_profiles(): HasMany
    {
        return $this->hasMany(FamilyProfile::class, "member_id");
    }

    //web招待状
    public function web_invitations(): HasMany
    {
        return $this->hasMany(WebInvitation::class, "member_id");
    }

    public function email_changes(): HasMany
    {
        return $this->hasMany(MemberEmailChange::class);
    }
}
