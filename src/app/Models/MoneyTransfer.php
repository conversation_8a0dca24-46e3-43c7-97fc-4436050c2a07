<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\MoneyTransferStatusEnum;
use App\Enums\MoneyTransferErrorStatusEnum;
use App\Enums\RegistrationEnum;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;

//送金
class MoneyTransfer extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        "member_id",
        "web_invitation_id",
        "admin_id",
        "status",
        "is_error",
        "deadline_date",
        "prepayment_amount",
        "system_fee",
        "member_system_fee",
        "guest_system_fee",
        "commission_fee",
        "transfer_amount",
        "transfer_date",
        "completion_datetime",
        "bank_code",
        "bank_name",
        "branch_code",
        "branch_name",
        "account_type",
        "account_name",
        "account_number",
    ];

    // ステータスで検索
    public function scopeStatus($query, $status)
    {
        if ($status->value !== MoneyTransferStatusEnum::ALL) {
            $query->where("status", $status->value);
        }

        return $query;
    }

    // 送金ステータスで検索
    public function scopeErrorStatus($query, $status)
    {
        if ($status->value !== MoneyTransferErrorStatusEnum::ALL) {
            $query->where("is_error", $status->value);
        }

        return $query;
    }

    //会員番号(ID)で検索
    public function scopeWithMemberId($query, $searchId)
    {
        return $query->whereHas("member", function ($query) use ($searchId) {
            $query->where(
                DB::raw('LPAD(member_number, 8, "0")'),
                "LIKE",
                "%{$searchId}%"
            )->orWhere(
                'alternate_member_number',
                "LIKE",
                "%{$searchId}%"
            );
        });
    }

    //会員名で検索
    public function scopeWithMemberName($query, $searchName)
    {
        $searchName = Str::of($searchName)
            ->replaceMatches("/[\s\x{3000}]+/u", "")
            ->toString();
        return $query->whereHas("member", function ($query) use ($searchName) {
            $query->where(
                DB::raw("concat(last_name, '', first_name)"),
                "LIKE",
                "%{$searchName}%"
            );
        });
    }

    //口座登録済みか
    public function scopeAccount($query, $account)
    {
        if ($account->value == RegistrationEnum::Unregistered) {
            return $query->whereDoesntHave("member.member_bank_account");
        } elseif ($account->value == RegistrationEnum::Registered) {
            return $query->whereHas("member.member_bank_account");
        }
        return $query;
    }

    //送金金額　小数点切り捨て
    public function getFloorTransferAmountAttribute()
    {
        return floor($this->transfer_amount);
    }

    //システム利用料のアクセサ
    public function getAccessorSystemFeeAttribute()
    {
        return is_null($this->guest_system_fee) ? null : $this->guest_system_fee + $this->system_fee;
    }

    //ゲストシステム利用料のアクセサ
    public function getAccessorGuestSystemFeeAttribute()
    {
        return is_null($this->guest_system_fee) ? null : $this->guest_system_fee;
    }

    //会員システム利用料のアクセサ
    public function getAccessorMemberSystemFeeAttribute()
    {
        return $this->system_fee;
    }

    /**
     * 会員へのリレーション：1対多
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }

    /**
     * web招待状へのリレーション：1対多
     */
    public function web_invitation(): BelongsTo
    {
        return $this->belongsTo(WebInvitation::class)->withTrashed();
    }

    /**
     * 管理者(担当)へのリレーション：1対多
     */
    public function admin(): BelongsTo
    {
        return $this->belongsTo(Admin::class)->withTrashed();
    }
}
