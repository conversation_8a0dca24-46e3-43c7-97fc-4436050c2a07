<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

//会員銀行口座
class MemberBankAccount extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "member_id",
        "bank_code",
        "bank_name",
        "branch_code",
        "branch_name",
        "account_type",
        "account_name",
        "account_number",
        "phone",
    ];

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }
}
