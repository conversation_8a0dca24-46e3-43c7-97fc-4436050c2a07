<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

//規格別商品マスタ
class MSpecificationProduct extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "product_id",
        "product_specification_detail_info_id1",
        "product_specification_detail_info_id2",
        "item_number",
        "regular_price",
        "sale_price",
        "sale_price_start",
        "sale_price_end",
    ];

    //商品タグIDによる絞り込み
    public function scopeWithProductTag($query, $productTagIds)
    {
        if (!empty($productTagIds)) {
            return $query->whereHas("product.tags", function ($query) use (
                $productTagIds
            ) {
                $query->whereIn("tags.id", $productTagIds);
            });
        }
        return $query;
    }

    //商品マスタ
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, "product_id")->withTrashed();
    }

    //商品マスタ(グローバルスコープを解除)
    public function no_period_product(): BelongsTo
    {
        return $this->belongsTo(
            Product::class,
            "product_id"
        )->withoutGlobalScopes();
    }

    //WEB招待状マスタ
    public function m_web_invitations(): HasOne
    {
        return $this->hasOne(
            MWebInvitation::class,
            "m_specification_product_id"
        );
    }

    //規格別商品画像
    public function product_images(): HasMany
    {
        return $this->hasMany(
            ProductImage::class,
            "m_specification_product_id"
        );
    }
}
