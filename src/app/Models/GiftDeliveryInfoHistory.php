<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物送付先情報履歴モデル
 */
class GiftDeliveryInfoHistory extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //引き出物手配履歴へのリレーション: 1対多
    public function gift_arrangement_history(): BelongsTo
    {
        return $this->belongsTo(GiftArrangementHistory::class);
    }

    //ゲストへのリレーション: 1対多
    public function guest(): BelongsTo
    {
        return $this->belongsTo(Guest::class);
    }

    //引き出物割り当てメインギフト履歴
    public function gift_assignment_history(): HasOne
    {
        return $this->hasOne(GiftAssignmentHistory::class);
    }
}
