<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

//WEB招待状共通テンプレートマスタ
class MWebInvitationTemplate extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //WEB招待状共通テンプレートブロック
    public function web_invitation_template_blocks(): HasMany
    {
        return $this->hasMany(
            WebInvitationTemplateBlock::class,
            "m_web_invitation_template_id"
        );
    }

    //WEB招待状マスタ
    public function m_web_invitations(): HasMany
    {
        return $this->hasMany(
            MWebInvitation::class,
            "m_web_invitation_template_id"
        );
    }
}
