<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物割り当てメインギフト履歴モデル
 */
class GiftAssignmentHistory extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //引き出物送付先情報履歴へのリレーション: 1対多
    public function gift_delivery_info_history(): BelongsTo
    {
        return $this->belongsTo(GiftDeliveryInfoHistory::class);
    }

    //決済履歴明細へのリレーション: 1対1
    public function payment_history_detail(): BelongsTo
    {
        return $this->belongsTo(PaymentHistoryDetail::class);
    }

    //引き出物割り当てサブギフトへのリレーション: 1対多
    public function sub_gift_assignment_histories(): HasMany
    {
        return $this->hasMany(SubGiftAssignmentHistory::class);
    }
}
