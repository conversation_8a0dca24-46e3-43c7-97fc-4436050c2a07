<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use App\Consts\SystemConst;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 商品モデル
 */
class Product extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * モデルの「起動」メソッド
     *
     * @return void
     */
    protected static function booted()
    {
        //認証されてないユーザーまたは会員なら商品情報は期間内のデータしか取得できない
        static::addGlobalScope("period", function (Builder $builder) {
            if (!Auth::check() || Auth::user() instanceof Member) {
                $builder
                    ->whereDate("sales_period_start", "<=", now())
                    ->where("sales_period_end", ">=", now());
            }
        });

        //非公開フラグ有効のデータはアクセスできないようにする
        static::addGlobalScope("unpublished", function (Builder $builder) {
            $builder->where("is_unpublished", 0);
        });
    }

    protected $fillable = [
        "name",
        "shipping_cost",
        "estimated_delivery_days",
        "is_sample_request_allowed",
        "is_use_component",
        "is_sales_period_specified",
        "sales_period_start",
        "sales_period_end",
        "is_reservation_period_specified",
        "reservation_period_start",
        "reservation_period_end",
        "product_inventory",
        "admin_notes",
        "product_description",
        "is_editor",
        "is_specification",
        "is_variation_specification",
        "product_type_code",
        "option_product_price_difference",
        "is_unpublished",
        "meta_title",
        "meta_description",
        "meta_canonical",
        "meta_keywords",
        "display_order",
    ];

    //グローバルスコープ解除
    public function scopeDeleteScope(Builder $query): Builder
    {
        return $query->withoutGlobalScopes();
    }

    //商品タグIDによる絞り込み
    public function scopeWithTag($query, $tagIds)
    {
        if (!empty($tagIds)) {
            return $query->whereHas("tags", function ($query) use ($tagIds) {
                $query->whereIn("tags.id", $tagIds);
            });
        }

        return $query;
    }

    //タグ
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, "product_tags")
            ->using(ProductTag::class)
            ->withPivot("product_id", "tag_id")
            ->withTimestamps();
    }

    //規格別商品マスタ
    public function m_specification_products(): HasMany
    {
        return $this->hasMany(MSpecificationProduct::class, "product_id");
    }
}
