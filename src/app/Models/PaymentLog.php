<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class PaymentLog extends Model
{
    use HasFactory, HasUuids;

    // フィールドの一括代入を許可する
    protected $fillable = [
        "member_id",
        "web_invitation_id",
        "type",
        "guest_name",
        "input_parameters",
        "payment_id",
        "payment_post_parameters",
        "payment_post_results",
        "api_results",
        "error_results",
        "ip_address",
    ];

    // キャストを定義して、適切な型に変換
    protected $casts = [
        "id" => "string",
        "input_parameters" => "json",
        "payment_post_parameters" => "json",
        "payment_post_results" => "json",
        "api_results" => "json",
        "ip_address" => "string",
    ];
}
