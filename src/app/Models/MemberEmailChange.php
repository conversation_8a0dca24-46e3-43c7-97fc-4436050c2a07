<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

// メールアドレス変更
class MemberEmailChange extends Model
{
    const UPDATED_AT = null;

    use HasFactory, HasUuids;

    protected $fillable = ["member_id", "new_email", "token"];

    //会員
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }
}
