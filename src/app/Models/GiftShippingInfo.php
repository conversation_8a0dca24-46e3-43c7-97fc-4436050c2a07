<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 手配前引き出物送付先情報モデル
 */
class GiftShippingInfo extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * 会員へのリレーション：1対多
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * ゲストへのリレーション：1対多
     */
    public function guest(): BelongsTo
    {
        return $this->belongsTo(Guest::class);
    }

    /**
     * 手配前引き出物割り当てメインギフト: 1対1
     */
    public function gift_assignment(): HasOne
    {
        return $this->hasOne(GiftAssignment::class);
    }
}
