<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\Pivot;

//商品タグ
class ProductTag extends Pivot
{
    use HasFactory, HasUuids;

    //商品
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    //タグ
    public function tag(): BelongsTo
    {
        return $this->belongsTo(Tag::class);
    }
}
