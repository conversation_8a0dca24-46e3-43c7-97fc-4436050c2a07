<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 決済履歴明細モデル
 */
class PaymentHistoryDetail extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //決済履歴: 1対多
    public function payment_history(): BelongsTo
    {
        return $this->belongsTo(PaymentHistory::class);
    }

    //商品へのリレーション: 1対多
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
