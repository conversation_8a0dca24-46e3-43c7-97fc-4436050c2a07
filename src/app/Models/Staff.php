<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * スタッフモデル
 */
class Staff extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $table = "staffs"; // モデルに対応するテーブル名を指定
}
