<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物ラッピング
 */
class GiftWrapping extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * 会員へのリレーション：1対多
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * 商品へのリレーション：1対多
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * 引き出物オプション商品へのリレーション: 1対多
     */
    public function gift_option_product(): BelongsTo
    {
        return $this->belongsTo(GiftOptionProduct::class);
    }

    //決済履歴明細へのリレーション: 1対多
    public function payment_history_detail(): BelongsTo
    {
        return $this->belongsTo(PaymentHistoryDetail::class);
    }
}
