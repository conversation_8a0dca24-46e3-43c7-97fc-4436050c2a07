<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

//ゲストリスト別イベントリスト
class EventList extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $table = "event_lists";

    protected $fillable = [
        "guest_list_id",
        "event_name",
        "event_date",
        "event_time",
        "venue_name",
        "venue_name_kana",
        "venue_postal_code",
        "venue_address",
        "venue_tel",
        "venue_url",
        "fees",
    ];

    protected $casts = [
        "event_time" => "json",
        "fees" => "json",
    ];

    //ゲストリストへのリレーション：多対１
    public function guest_list(): BelongsTo
    {
        return $this->belongsTo(
            GuestList::class,
            "guest_list_id"
        )->withTrashed();
    }

    //ゲストリストへのリレーション：多対１
    public function guest_event_answers(): HasMany
    {
        return $this->hasMany(GuestEventAnswer::class);
    }

    //同ゲストリストの日時を取得
    public function getGroupEventDate($guestListId)
    {
        $eventDateRecord = self::where("guest_list_id", $guestListId)
            ->select("event_date")
            ->groupBy("event_date")
            ->havingRaw("COUNT(DISTINCT event_date) = 1")
            ->first();

        return $eventDateRecord ? $eventDateRecord->event_date : null;
    }
}
