<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
class MasterBlock extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = ["name", "product_type", "json_data"];

    protected $casts = [
        "json_data" => "json",
    ];

    // バリエーションテンプレート
    public function variation_templates(): BelongsToMany
    {
        return $this->belongsToMany(
            VariationTemplate::class,
            "variation_templates"
        )
            ->using(VariationTemplateMasterBlock::class)
            ->withPivot("variation_template_id", "master_block_id")
            ->withTimestamps();
    }
}
