<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 手配前引き出物割り当てメインギフトモデル
 */
class GiftAssignment extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * 手配前引き出物送付先情報へのリレーション：1対1
     */
    public function gift_shipping_info(): BelongsTo
    {
        return $this->belongsTo(GiftShippingInfo::class);
    }

    /**
     * 引き出物割り当てサブギフトへのリレーション: 1対多
     */
    public function sub_gift_assignments(): HasMany
    {
        return $this->hasMany(SubGiftAssignment::class);
    }

    /**
     * 商品へのリレーション:1対多
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    //決済履歴明細へのリレーション: 1対多
    public function payment_history_detail(): BelongsTo
    {
        return $this->belongsTo(PaymentHistoryDetail::class);
    }
}
