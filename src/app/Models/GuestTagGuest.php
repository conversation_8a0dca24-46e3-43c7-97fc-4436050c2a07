<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\Pivot;

//ゲストタグとゲストの中間テーブル
class GuestTagGuest extends Pivot
{
    use HasFactory, HasUuids;

    protected $table = "guest_tag_guests";

    /**
     * ゲストへのリレーション
     */
    public function guest(): BelongsTo
    {
        return $this->belongsTo(Guest::class);
    }

    /**
     * ゲストタグへのリレーション
     */
    public function guest_tag(): BelongsTo
    {
        return $this->belongsTo(GuestTag::class);
    }
}
