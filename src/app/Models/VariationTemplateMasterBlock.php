<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\Relations\Pivot;

class VariationTemplateMasterBlock extends Pivot
{
    use HasFactory, HasUuids;

    protected $fillable = ["variation_template_id", "master_block_id"];

    // バリエーションテンプレート
    public function variation_template(): BelongsTo
    {
        return $this->belongsTo(VariationTemplate::class)->withTrashed();
    }

    // マスターブロック
    public function master_block(): BelongsTo
    {
        return $this->belongsTo(MasterBlock::class)->withTrashed();
    }
}
