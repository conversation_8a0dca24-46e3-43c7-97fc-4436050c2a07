<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物割り当てサブギフト履歴モデル
 */
class SubGiftAssignmentHistory extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * 引き出物割り当てメインギフト履歴へのリレーション：1対多
     */
    public function gift_assignment_history(): BelongsTo
    {
        return $this->belongsTo(GiftAssignmentHistory::class);
    }

    /**
     * 引き出物オプション商品へのリレーション: 1対多
     */
    public function gift_option_product(): BelongsTo
    {
        return $this->belongsTo(GiftOptionProduct::class);
    }
}
