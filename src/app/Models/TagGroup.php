<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
//タググループマスタ
class TagGroup extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //タグへのリレーション
    public function tags(): HasMany
    {
        return $this->hasMany(Tag::class);
    }
}
