<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Consts\SystemConst;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Enums\PaymentMethodEnum;
use App\Values\TrimmedString;
use Illuminate\Support\Facades\DB;

//ユーザー作成WEB招待状
class WebInvitation extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected static function boot()
    {
        parent::boot();

        static::updating(function ($webInvitation) {
            $webInvitation->transfer_date = static::calculateTransferDate($webInvitation->scheduled_transfer_date);
        });

        static::deleting(function ($webInvitation) {
            if (!empty($webInvitation->is_public)) {
                throw new \Exception("公開設定していますので削除できません。");
            }
        });
    }

    protected $table = "web_invitations";

    protected $casts = [
        "editor_settings" => "json",
        "block_settings" => "json",
    ];

    protected $fillable = [
        "member_id",
        "m_web_invitation_id",
        "guest_list_id",
        "name",
        "public_url",
        "password",
        "is_password",
        "is_public",
        "scheduled_date",
        "reply_deadline_date",
        "transfer_date",
        "scheduled_transfer_date",
        "prepayment_due_date",
        "editor_settings",
        "block_settings",
    ];

    //イベント情報からパーティ名の配列を返す
    public function getEventListAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );

        $eventInfo = $editorSettings->firstWhere("id", "information");

        if (!$eventInfo) {
            return [];
        }

        $type = $eventInfo["contents"]["type"] ?? null;

        $eventNames = collect();

        if ($type && array_key_exists($type, SystemConst::PARTY_INFO_LIST)) {
            $eventNames->push(SystemConst::PARTY_INFO_LIST[$type]);
        }

        $events = $eventInfo["contents"]["events"] ?? [];

        foreach ($events as $idx => $event) {
            if ($idx === 0 && $type !== "other_party") {
                continue;
            }
            if (!isset($event["eventName"])) {
                continue;
            }

            $eventNames->push(TrimmedString::mbTrim($event["eventName"]));
        }

        return $eventNames
            ->filter()
            ->values()
            ->flatten()
            ->all();
    }

    //イベント情報からパーティー開催日を取得
    public function getEventDateAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );
        $eventInfo = $editorSettings->firstWhere("id", "information");
        if (!$eventInfo) {
            return null;
        }

        if (!isset($eventInfo["contents"]["date"])) {
            return null;
        }

        // 日付の形式か判断
        if (
            preg_match(
                '/^[0-9]{4}.[0-9]{1,2}.[0-9]{1,2}$/',
                $eventInfo["contents"]["date"]
            )
        ) {
            return Carbon::parse($eventInfo["contents"]["date"])->format(
                "Y-m-d"
            );
        }
        return null;
    }

    //イベント情報から回答期日を取得
    public function getLimitDateAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );

        $eventInfo = $editorSettings->firstWhere("id", "guestAnswer");

        if (!$eventInfo) {
            return null;
        }

        if (isset($eventInfo["contents"]["limit"]["date"])) {
            if (
                preg_match(
                    '/^[0-9]{4}.[0-9]{1,2}.[0-9]{1,2}$/',
                    $eventInfo["contents"]["limit"]["date"]
                )
            ) {
                return Carbon::parse(
                    $eventInfo["contents"]["limit"]["date"]
                )->format("Y-m-d");
            }
            return null;
        } else {
            $event_date = $this->event_date;
            if (
                $event_date &&
                !empty($eventInfo["contents"]["limit"]["setting"])
            ) {
                return Carbon::parse($event_date)
                    ->subMonthsNoOverflow(
                        $eventInfo["contents"]["limit"]["setting"]
                    )
                    ->format("Y-m-d");
            }
            return $event_date;
        }
    }

    //ゲスト別イベントリスト取得
    public function getGuestListTypeEventListAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );
        $eventInfo = $editorSettings->firstWhere("id", "information");
        if (!$eventInfo) {
            return [];
        }

        $type = $eventInfo["contents"]["type"] ?? null;
        $date = $eventInfo["contents"]["date"] ?? null;
        $events = $eventInfo["contents"]["events"] ?? [];

        $result = collect();
        foreach ($events as $idx => $event) {
            if (
                $idx == 0 &&
                $type == SystemConst::WEDDING_RECEPTION_SAME_VENUE
            ) {
                $result[
                    SystemConst::PARTY_INFO_LIST[$type]
                ] = $this->formatEvent(
                    SystemConst::PARTY_INFO_LIST[$type],
                    $date,
                    $event,
                    $event["feeAmount"] ?? []
                );
            } elseif (
                $idx == 0 &&
                $type == SystemConst::WEDDING_RECEPTION_SEPARATE_VENUE
            ) {
                $result[
                    SystemConst::PARTY_INFO_LIST[$type][0]
                ] = $this->formatEvent(
                    SystemConst::PARTY_INFO_LIST[$type][0],
                    $date,
                    $event,
                    $event["feeAmount"] ?? [],
                    true
                );
                $result[
                    SystemConst::PARTY_INFO_LIST[$type][1]
                ] = $this->formatEvent(
                    SystemConst::PARTY_INFO_LIST[$type][1],
                    $date,
                    $event,
                    [],
                    false
                );
            } else {
                $result[TrimmedString::mbTrim($event["eventName"] ?? "")] = $this->formatEvent(
                    $event["eventName"] ?? "",
                    $date,
                    $event,
                    $event["feeAmount"] ?? []
                );
            }
        }

        return $result->toArray();
    }

    private function formatEvent(
        $eventName,
        $date,
        $event,
        $fees = [],
        $isOtherVenue = false
    ) {
        return [
            "event_name" => TrimmedString::mbTrim($eventName),
            "event_date" => $date,
            "event_time" => json_encode(
                $event[$isOtherVenue ? "otherPlans" : "plans"] ?? [],
                JSON_PRETTY_PRINT
            ),
            "venue_name" =>
                $event[$isOtherVenue ? "otherVenue" : "venue"] ?? "",
            "venue_name_kana" =>
                $event[$isOtherVenue ? "otherVenue_kana" : "venue_kana"] ?? "",
            "venue_postal_code" =>
                $event[$isOtherVenue ? "otherZip" : "zip"] ?? "",
            "venue_address" =>
                $event[$isOtherVenue ? "otherAddress" : "address"] ?? "",
            "venue_tel" => $event[$isOtherVenue ? "otherTel" : "tel"] ?? "",
            "venue_url" => $event[$isOtherVenue ? "otherUrl" : "url"] ?? "",
            "fees" => json_encode($fees, JSON_PRETTY_PRINT),
        ];
    }

    //紐づくゲストの事前支払いがあるかチェック
    public function getHasPrePaidGuestAttribute()
    {
        return $this->guests()
            ->where("payment_method", PaymentMethodEnum::ADVANCE_PAYMENT)
            ->exists();
    }

    //送金設定の値を取得
    public function getGiftScheduleDateAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );
        $eventInfo = $editorSettings->firstWhere("id", "gift");
        if (!$eventInfo) {
            return null;
        }

        return $eventInfo["contents"]["scheduleDate"] ?? "";
    }

    //回答の任意の日付の値を取得
    public function getGuestAnswerDateAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );
        $eventInfo = $editorSettings->firstWhere("id", "guestAnswer");
        if (!$eventInfo) {
            return null;
        }

        return $eventInfo["contents"]["limit"]["date"] ?? null;
    }

    //回答の任意の日付の値を取得
    public function getGuestAnswerSetAttribute()
    {
        $editorSettings = new Collection(
            $this->editor_settings["blocks"] ?? []
        );
        $eventInfo = $editorSettings->firstWhere("id", "guestAnswer");
        if (!$eventInfo) {
            return null;
        }

        return $eventInfo["contents"]["limit"]["setting"] ?? "";
    }

    // 送金日 の計算ロジック
    public static function calculateTransferDate($scheduledDate)
    {
        if (!$scheduledDate) {
            return null;
        }

        $targetDate = Carbon::parse($scheduledDate)->subDays(SystemConst::TRANSFER_DUE_BEFORE_BUSINESS_DAYS);

        $holidays = DB::table('calendars')
            ->where('date', '<=', $targetDate->toDateString())
            ->orderByDesc('date')
            ->limit(10)
            ->pluck('date')
            ->map(fn($date) => Carbon::parse($date));

        while (true) {
            $isModified = false;

            // 祝日の場合、前日へ
            if ($holidays->contains(fn($holiday) => $targetDate->isSameDay($holiday))) {
                $targetDate = $targetDate->subDay();
                $isModified = true;
            }
            // 土曜日なら前日の金曜日へ
            elseif ($targetDate->dayOfWeek === Carbon::SATURDAY) {
                $targetDate = $targetDate->subDay();
                $isModified = true;
            }
            // 日曜日なら前々日の金曜日へ
            elseif ($targetDate->dayOfWeek === Carbon::SUNDAY) {
                $targetDate = $targetDate->subDays(2);
                $isModified = true;
            }

            // 変更がなかったらループを抜ける
            if (!$isModified) {
                break;
            }
        }

        return $targetDate->format('Y-m-d');
    }

    // 招待状URLをフルパスで取得
    public function getFullUrlAttribute()
    {
        return config('mail.front_url_web_invitation') .$this->public_url. config('mail.open_external_browser');
    }

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }

    //ゲストリストへのリレーション：多対１
    public function guest_list(): BelongsTo
    {
        return $this->belongsTo(
            GuestList::class,
            "guest_list_id"
        )->withTrashed();
    }

    //ゲストへのリレーション
    public function guests(): HasMany
    {
        return $this->hasMany(Guest::class);
    }

    //Web招待状マスタ
    public function m_web_invitation(): BelongsTo
    {
        return $this->belongsTo(
            MWebInvitation::class,
            "m_web_invitation_id"
        )->withTrashed();
    }

    // 送金管理へのリレーション
    public function money_transfer(): HasOne
    {
        return $this->hasOne(MoneyTransfer::class, 'web_invitation_id');
    }

}
