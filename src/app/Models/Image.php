<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;
//ファイル管理
class Image extends Model
{
    use HasFactory, SoftDeletes;

    protected $primaryKey = "uuid";
    protected $keyType = "string"; // プライマリキーのデータ型
    public $incrementing = false; // インクリメンタルな値を使用しない

    protected $fillable = ["extension_type"];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            $model->uuid = (string) Str::uuid();
        });
    }

    /**
     * ファイル名を取得(uuid)
     *
     * @return string
     */
    public static function createFileName()
    {
        return (string) Str::uuid();
    }
}
