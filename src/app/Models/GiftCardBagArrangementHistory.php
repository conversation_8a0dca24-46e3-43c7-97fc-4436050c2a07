<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物カードバッグ手配履歴モデル
 */
class GiftCardBagArrangementHistory extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //引き出物手配履歴へのリレーション: 1対多
    public function gift_arrangement_history(): BelongsTo
    {
        return $this->belongsTo(GiftArrangementHistory::class);
    }

    //引き出物カードバッグ手配履歴] 1対多
    public function gift_card_bag_arrangement_history_Details(): HasMany
    {
        return $this->hasMany(GiftCardBagArrangementHistoryDetail::class);
    }
}
