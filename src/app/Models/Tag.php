<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
//タグマスタ
class Tag extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //タググループへのリレーション
    public function tag_group(): BelongsTo
    {
        return $this->belongsTo(TagGroup::class)->withTrashed();
    }

    //商品へのリレーション
    public function product(): BelongsToMany
    {
        return $this->belongsToMany(ProductTag::class, "product_tags")
            ->using(ProductTag::class)
            ->withPivot("product_id", "tag_id")
            ->withTimestamps();
    }
}
