<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
class VariationTemplate extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "name",
        "basic_variation_template_id",
        "product_type",
        "json_data",
    ];

    protected $casts = [
        "json_data" => "json",
    ];

    //基本バリエーションテンプレート リレーション
    public function basic_variation_template(): BelongsTo
    {
        return $this->belongsTo(BasicVariationTemplate::class)->withTrashed();
    }

    // マスターブロック
    public function master_blocks(): BelongsToMany
    {
        return $this->belongsToMany(
            MasterBlock::class,
            "variation_template_master_blocks"
        )
            ->using(VariationTemplateMasterBlock::class)
            ->withPivot("variation_template_id", "master_block_id")
            ->withTimestamps();
    }
}
