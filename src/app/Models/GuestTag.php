<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

//ゲストタグ
class GuestTag extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //起動時の処理
    public static function boot()
    {
        parent::boot();

        static::deleting(function ($guestTag) {
            $guestTag->guests()->detach();
        });
    }

    //ゲストへのリレーション
    public function guest_list(): BelongsTo
    {
        return $this->belongsTo(GuestList::class)->withTrashed();
    }

    //ゲストへのリレーション：多対多
    public function guests(): BelongsToMany
    {
        return $this->belongsToMany(
            Guest::class,
            "guest_tag_guests",
            "guest_tag_id",
            "guest_id"
        )
            ->using(GuestTagGuest::class)
            ->withPivot("guest_id", "guest_tag_id")
            ->withTimestamps();
    }

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }
}
