<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 決済履歴モデル
 */
class PaymentHistory extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * 決済履歴明細へのリレーション: 1対多
     */
    public function payment_history_details(): HasMany
    {
        return $this->hasMany(PaymentHistoryDetail::class);
    }

    //会員へのリレーション: 1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }

    //引き出物手配履歴へのリレーション: 多対多
    public function gift_arrangement_histories(): BelongsToMany
    {
        return $this->belongsToMany(
            GiftArrangementHistory::class,
            "gift_arrangement_histories"
        );
    }
}
