<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物手配履歴-決済履歴モデル
 */
class GiftArrangementHistoryPaymentHistory extends Model
{
    use HasFactory, HasUuids;

    //決済履歴へのリレーション
    public function payment_history(): BelongsTo
    {
        return $this->belongsTo(PaymentHistory::class);
    }

    //引き出物手配り歴へのリレーション
    public function gift_arrangement_history(): BelongsTo
    {
        return $this->belongsTo(GiftArrangementHistory::class);
    }
}
