<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

class GuestGroup extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //起動時の処理
    public static function boot()
    {
        parent::boot();

        // ゲストグループが削除される時に、紐づくゲストのguest_group_idをNULLにする
        static::deleting(function ($guestGroup) {
            $guestGroup->guests()->update(["guest_group_id" => null]);
        });
    }

    //ゲストリストへのリレーション
    public function guest_list(): BelongsTo
    {
        return $this->belongsTo(GuestList::class)->withTrashed();
    }

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }

    //ゲストグループへのリレーション
    public function guests(): HasMany
    {
        return $this->hasMany(Guest::class, "guest_group_id", "id");
    }
}
