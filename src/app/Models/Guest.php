<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * ゲストモデル
 */
class Guest extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "member_id",
        "guest_list_id",
        "guest_group_id",
        "parent_guest_id",
        "web_invitation_id",
        "m_web_invitation_id",
        "guest_type",
        "last_name",
        "first_name",
        "last_name_kana",
        "first_name_kana",
        "last_name_romaji",
        "first_name_romaji",
        "gender",
        "allergies",
        "allergy",
        "birthdate",
        "image_url",
        "postal_code",
        "prefecture",
        "city",
        "address",
        "building",
        "phone",
        "email",
        "message",
        "media_type",
        "media_uuid",
        "invitation_delivery",
        "guest_title",
        "guest_honor",
        "relationship",
        "relationship_name",
        "web_invite_reply_datetime",
        "member_confirm_type",
        "payment_method",
        "is_system_fee",
        "system_fee",
        "system_fee_rate",
        "gift_amount",
        "total_amount",
        "settlement_amount",
        "card_settlement_id",
        "is_update_web_invitation_complet_mail",
        "is_previousday_party_mail",
    ];

    protected $casts = [
        "allergies" => "json",
    ];

    /** ユーザ変更禁止カラム */
    // protected $guarded = ["created_at", "updated_at"];

    // 出席情報チェック
    public function getIsAttendingAttribute()
    {
        return $this->guest_event_answers->contains(function ($answer) {
            return $answer->attendance === "出席";
        });
    }

    // 保留情報チェック
    public function getIsPendingAttribute()
    {
        return $this->guest_event_answers->contains(function ($answer) {
            return $answer->attendance === "保留";
        });
    }

    //ゲスト出席データ　存在しないイベントはnullにする
    public function getGuestEventAttendanceAttribute()
    {
        $eventAnswers = [];
        foreach ($this->guest_event_answers as $row) {
            if (is_string($row->name) || is_int($row->name)) {
                $eventAnswers[$row->name] = [
                    "id" => $row->id,
                    "attendance" => $row->attendance,
                    "date" => $row->date,
                ];
            }
        }

        $ret["guest_event_attendances"] = [];
        foreach ($this->guest_list->event_lists ?? [] as $event) {
            $ret["guest_event_attendances"][] = [
                "id" => $eventAnswers[$event->event_name]["id"] ?? null,
                "name" => $event->event_name,
                "attendance" =>
                    $eventAnswers[$event->event_name]["attendance"] ?? null,
            ];
        }

        return $ret["guest_event_attendances"];
    }

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    //ゲストリストへのリレーション：多対１
    public function guest_list(): BelongsTo
    {
        return $this->belongsTo(
            GuestList::class,
            "guest_list_id"
        )->withTrashed();
    }

    //ゲストグループへのリレーション: 多対1
    public function guest_group(): BelongsTo
    {
        return $this->belongsTo(
            GuestGroup::class,
            "guest_group_id"
        )->withTrashed();
    }

    //ゲストへの自己結合 親
    public function parent_guest(): BelongsTo
    {
        return $this->belongsTo(Guest::class, "parent_guest_id")->withTrashed();
    }

    //ゲストへの自己結合 子
    public function children_guests(): HasMany
    {
        return $this->hasMany(Guest::class, "parent_guest_id", "id");
    }

    //ゲストフリー項目値へのリレーション: 1対1
    public function guest_free_item_values(): HasMany
    {
        return $this->hasMany(GuestFreeItemValue::class, "guest_id");
    }

    //タグへのリレーション：多対多
    public function guest_tags(): BelongsToMany
    {
        return $this->belongsToMany(
            GuestTag::class,
            "guest_tag_guests",
            "guest_id",
            "guest_tag_id"
        )
            ->using(GuestTagGuest::class)
            ->withPivot("guest_id", "guest_tag_id")
            ->withTimestamps();
    }

    //ユーザー作成WEB招待状
    public function web_invitation(): BelongsTo
    {
        return $this->belongsTo(
            WebInvitation::class,
            "web_invitation_id"
        )->withTrashed();
    }

    //WEB招待状マスタ
    public function m_web_invitation(): BelongsTo
    {
        return $this->belongsTo(
            MWebInvitation::class,
            "m_web_invitation_id"
        )->withTrashed();
    }

    //ゲストイベント別回答
    public function guest_event_answers(): HasMany
    {
        return $this->hasMany(GuestEventAnswer::class);
    }

    //ゲストアンケート回答
    public function guest_survey_answers(): HasMany
    {
        return $this->hasMany(GuestSurveyAnswer::class);
    }
}
