<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物カードバッグ手配履歴明細モデル
 */
class GiftCardBagArrangementHistoryDetail extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //引き出物カードバッグ手配履歴へのリレーション: 1対多
    public function gift_card_bag_arrangement_history(): BelongsTo
    {
        return $this->belongsTo(GiftCardBagArrangementHistory::class);
    }

    //商品へのリレーション: 1対多
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
