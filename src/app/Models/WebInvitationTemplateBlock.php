<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

// WEB招待状共通テンプレートブロック
class WebInvitationTemplateBlock extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //WEB招待状共通テンプレートマスタ
    public function m_web_invitation_template(): BelongsTo
    {
        return $this->belongsTo(MWebInvitationTemplate::class)->withTrashed();
    }
}
