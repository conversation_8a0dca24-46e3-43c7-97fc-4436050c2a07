<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

//規格別商品画像
class ProductImage extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //規格別商品マスタ
    public function m_specification_product(): BelongsTo
    {
        return $this->belongsTo(MSpecificationProduct::class)->withTrashed();
    }

    //画像
    public function image(): BelongsTo
    {
        return $this->belongsTo(Image::class, "uuid", "uuid")->withTrashed();
    }
}
