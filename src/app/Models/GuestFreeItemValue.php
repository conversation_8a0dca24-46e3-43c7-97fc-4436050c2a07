<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * ゲストフリー項目値モデル
 */
class GuestFreeItemValue extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /** ユーザ変更禁止カラム */
    protected $guarded = ["created_at", "updated_at"];

    /**
     * ゲストへのリレーション：1対多
     */
    public function guest(): BelongsT<PERSON>
    {
        return $this->belongsTo(Guest::class)->withTrashed();
    }

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }
}
