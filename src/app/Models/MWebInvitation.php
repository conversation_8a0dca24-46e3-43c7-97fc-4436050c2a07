<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use App\Consts\SystemConst;

// WEB招待状マスタ
class MWebInvitation extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "id",
        "m_specification_product_id",
        "m_web_invitation_template_id",
        "m_web_invitation_visual_block_id",
        "first_view_id",
        "is_main_visual_image_disabled",
        "is_profile_image_disabled",
        "is_main_visual_image_replaceable",
        "css_code",
        "editor_settings_json",
        "image_aspect_settings_json",
    ];

    protected $casts = [
        "editor_settings_json" => "json",
        "image_aspect_settings_json" => "json",
    ];

    //JSONエンコードした画像アスペクト比を取得
    public function getEncodeImageAspectSettingsJsonAttribute()
    {
        return json_encode($this->image_aspect_settings_json);
    }

    //cssコードの予約語を商品IDに置換して取得
    public function getCssCodeAttribute()
    {
        return str_replace(
            SystemConst::CSS_CODE_KEYWORD,
            $this->m_specification_product->no_period_product->id,
            $this->attributes["css_code"]
        );
    }

    //cssコードの予約語のまま取得し
    public function getCssCodeReplaceAttribute()
    {
        return $this->attributes["css_code"];
    }

    //WEB招待状共通テンプレートマスタ
    public function m_web_invitation_template(): BelongsTo
    {
        return $this->belongsTo(MWebInvitationTemplate::class)->withTrashed();
    }

    //Web招待状メインビジュアルブロックマスタ
    public function m_web_invitation_visual_block(): BelongsTo
    {
        return $this->belongsTo(
            MWebInvitationVisualBlock::class
        )->withTrashed();
    }

    //WEB招待状デザイン画像
    public function web_invitation_design_images(): HasMany
    {
        return $this->hasMany(
            WebInvitationDesignImage::class,
            "m_web_invitation_id"
        );
    }

    //規格別商品マスタ
    public function m_specification_product(): BelongsTo
    {
        return $this->belongsTo(MSpecificationProduct::class)->withTrashed();
    }

    //ユーザー作成WEB招待状
    public function web_invitations(): HasMany
    {
        return $this->hasMany(WebInvitation::class, "m_web_invitation_id");
    }
}
