<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
class BasicVariationTemplate extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "name",
        "width",
        "height",
        "unit",
        "color_extension",
        "fold_line_type",
        "watermark",
        "json_data",
    ];

    protected $casts = [
        "json_data" => "json",
    ];
}
