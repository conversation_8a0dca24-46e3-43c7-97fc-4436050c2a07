<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物割り当てサブギフトモデル
 */
class SubGiftAssignment extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /**
     * 手配前引き出物割り当てメインギフトへのリレーション：1対多
     */
    public function gift_assignment(): BelongsTo
    {
        return $this->belongsTo(GiftAssignment::class);
    }

    /**
     * 引き出物オプション商品へのリレーション: 1対多
     */
    public function gift_option_product(): BelongsTo
    {
        return $this->belongsTo(GiftOptionProduct::class);
    }
}
