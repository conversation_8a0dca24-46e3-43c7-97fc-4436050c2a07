<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

//システム設定
class System extends Model
{
    use HasFactory, HasUuids;

    /**
     * システム設定の最初の一件を取得する
     *
     * @return self|null
     */
    public static function getFirstSystem(): ?self
    {
        return self::first();
    }
}
