<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Notifications\TmpCreateMemberQuestionnaireNotification;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;

//会員登録時アンケート情報
class MemberRegistQuestionnaire extends Model
{
    use HasFactory, SoftDeletes, Notifiable, HasUuids;

    protected $fillable = ["member_id", "question", "answer"];

    //会員
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }
}
