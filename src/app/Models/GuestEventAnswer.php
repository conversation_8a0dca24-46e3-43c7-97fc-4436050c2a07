<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
// ゲストイベント別回答
class GuestEventAnswer extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $fillable = [
        "uuid",
        "member_id",
        "guest_id",
        "event_list_id",
        // "name",
        // "date",
        "payment_amount",
        "attendance",
    ];

    /**
     * ゲストへのリレーション：1対多
     */
    public function guest(): BelongsTo
    {
        return $this->belongsTo(Guest::class)->withTrashed();
    }

    /**
     * イベントリストへのリレーション
     *
     * @return BelongsTo
     */
    public function event_list(): BelongsTo
    {
        return $this->belongsTo(EventList::class)->withTrashed();
    }

    /**
     * イベント名を取得
     *
     * @return string|null
     */
    public function getNameAttribute()
    {
        return $this->event_list->event_name ?? null;
    }

    /**
     * イベント開催日を取得
     *
     * @return string|null
     */
    public function getDateAttribute()
    {
        return $this->event_list->event_date ?? null;
    }
}
