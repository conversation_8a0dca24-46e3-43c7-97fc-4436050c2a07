<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * カウントダウン画像設定
 */
class CountdownImage extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    protected $table = 'countdown_image';

    protected $primaryKey = 'uuid';
    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'member_id',
        'instagram_type',
        'image_data',
        'show_names',
        'show_event_date',
    ];

    protected $casts = [
        'image_data' => 'json',
        'show_names' => 'boolean',
        'show_event_date' => 'boolean',
    ];

    /**
     * 会員との関連
     */
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class, 'member_id', 'id');
    }

    /**
     * 会員とInstagram種別でユニークな設定を取得
     */
    public static function findByMemberAndType(string $memberId, int $instagramType): ?self
    {
        return self::where('member_id', $memberId)
                   ->where('instagram_type', $instagramType)
                   ->first();
    }

    /**
     * 会員の全設定を取得
     */
    public static function getByMember(string $memberId): \Illuminate\Database\Eloquent\Collection
    {
        return self::where('member_id', $memberId)
                   ->orderBy('instagram_type')
                   ->get();
    }
}
