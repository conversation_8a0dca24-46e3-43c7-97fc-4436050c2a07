<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Support\Collection;
use App\Consts\SystemConst;
use App\Values\TrimmedString;

/**
 * ゲストリストモデル
 */
class GuestList extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    /** ユーザ変更禁止カラム */
    protected $guarded = ["created_at", "updated_at"];

    // イベント情報からパーティ名の配列を返す
    public function getEventListAttribute()
    {
        $event_list = [];
        foreach ($this->web_invitations as $web_invitation) {
            $event_list[] = $web_invitation->event_list;
        }
        return array_unique(array_merge(...$event_list));
    }

    // 最新のゲスト更新日を取得(ゲストが存在しない場合は、ゲストリストの登録日時を返す)
    public function getLatestGuestUpdatedAtAttribute()
    {
        $latestGuest = $this->guests()
            ->orderBy("updated_at", "desc")
            ->first();

        if (!$latestGuest) {
            return $this->created_at;
        }

        return $latestGuest->updated_at;
    }

    //紐づくweb招待状からイベントリスト作成
    public function getEventListDataAttribute()
    {
        $result = [];
        foreach ($this->web_invitations as $web_invitation) {
            $editorSettings = new Collection(
                $web_invitation->editor_settings["blocks"] ?? []
            );
            $eventInfo = $editorSettings->firstWhere("id", "information");
            if (!$eventInfo) {
                continue;
            }

            $type = $eventInfo["contents"]["type"] ?? null;
            $date = $eventInfo["contents"]["date"] ?? null;
            $events = $eventInfo["contents"]["events"] ?? [];
            foreach ($events as $idx => $event) {
                if (
                    $idx == 0 &&
                    $type == SystemConst::WEDDING_RECEPTION_SAME_VENUE
                ) {
                    $result[
                        SystemConst::PARTY_INFO_LIST[$type]
                    ] = $this->formatEvent(
                        SystemConst::PARTY_INFO_LIST[$type],
                        $date,
                        $event,
                        $event["feeAmount"] ?? []
                    );
                } elseif (
                    $idx == 0 &&
                    $type == SystemConst::WEDDING_RECEPTION_SEPARATE_VENUE
                ) {
                    $result[
                        SystemConst::PARTY_INFO_LIST[$type][0]
                    ] = $this->formatEvent(
                        SystemConst::PARTY_INFO_LIST[$type][0],
                        $date,
                        $event,
                        $event["feeAmount"] ?? [],
                        true
                    );
                    $result[
                        SystemConst::PARTY_INFO_LIST[$type][1]
                    ] = $this->formatEvent(
                        SystemConst::PARTY_INFO_LIST[$type][1],
                        $date,
                        $event,
                        [],
                        false
                    );
                } else {
                    $result[TrimmedString::mbTrim($event["eventName"] ?? "")] = $this->formatEvent(
                        $event["eventName"] ?? "",
                        $date,
                        $event,
                        $event["feeAmount"] ?? []
                    );
                }
            }
        }

        return $result;
    }

    private function formatEvent(
        $eventName,
        $date,
        $event,
        $fees = [],
        $isOtherVenue = false
    ) {
        return [
            "event_name" => TrimmedString::mbTrim($eventName),
            "event_date" => $date,
            "event_time" => json_encode(
                $event[$isOtherVenue ? "otherPlans" : "plans"] ?? [],
                JSON_PRETTY_PRINT
            ),
            "venue_name" =>
                $event[$isOtherVenue ? "otherVenue" : "venue"] ?? "",
            "venue_name_kana" =>
                $event[$isOtherVenue ? "otherVenue_kana" : "venue_kana"] ?? "",
            "venue_postal_code" =>
                $event[$isOtherVenue ? "otherZip" : "zip"] ?? "",
            "venue_address" =>
                $event[$isOtherVenue ? "otherAddress" : "address"] ?? "",
            "venue_tel" => $event[$isOtherVenue ? "otherTel" : "tel"] ?? "",
            "venue_url" => $event[$isOtherVenue ? "otherUrl" : "url"] ?? "",
            "fees" => json_encode($fees, JSON_PRETTY_PRINT),
        ];
    }

    //該当ゲストリストのイベント情報をメールテンプレートに渡す値として加工
    public function getEventMailListAttribute()
    {
        $resultFormatStringArray = [];
        foreach ($this->event_lists as $event) {
            $receptionTime = sprintf(
                "%02d時%02d分",
                $event->event_time[0]["hour"],
                $event->event_time[0]["minute"]
            );
            $startTime = sprintf(
                "%02d時%02d分",
                $event->event_time[1]["hour"],
                $event->event_time[1]["minute"]
            );
            $resultFormatStringArray[
                $event->event_name
            ] = "{$event->event_name}:{$startTime}(受付:{$receptionTime})";
        }
        return $this->event_lists
            ->mapWithKeys(function ($event) use ($resultFormatStringArray) {
                return [
                    $event->event_name => [
                        "date" => $event->event_date,
                        "time" => $resultFormatStringArray,
                        "venue_name" => $event->venue_name,
                        "venue_name_kana" => $event->venue_name_kana,
                        "venue_postal_code" => $event->venue_postal_code,
                        "venue_address" => $event->venue_address,
                        "venue_tel" => $event->venue_tel,
                        "venue_url" => $event->venue_url,
                    ],
                ];
            })
            ->toArray();
    }

    //紐づくイベントリストのname => uuidリストを作成
    public function getEventNameUuidListAttribute()
    {
        $eventListsArray = [];
        foreach ($this->event_lists as $eventList) {
            $eventListsArray[$eventList->event_name] = $eventList->id;
        }

        return $eventListsArray;
    }

    //会員へのリレーション：1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }

    //ゲストへのリレーション：多対１
    public function guests(): HasMany
    {
        return $this->hasMany(Guest::class, "guest_list_id");
    }

    //ゲストグループへのリレーション: 多対1
    public function guest_groups(): HasMany
    {
        return $this->hasMany(GuestGroup::class, "guest_list_id");
    }

    //ゲストタグへのリレーション: 多対1
    public function guest_tags(): HasMany
    {
        return $this->hasMany(GuestTag::class, "guest_list_id");
    }

    //web招待状へのリレーション: 多対1
    public function web_invitations(): HasMany
    {
        return $this->hasMany(WebInvitation::class, "guest_list_id");
    }

    //ゲストリスト別イベントリスト: 多対1
    public function event_lists(): HasMany
    {
        return $this->hasMany(EventList::class, "guest_list_id");
    }
}
