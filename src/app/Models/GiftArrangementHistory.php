<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Str;
/**
 * 引き出物手配履歴モデル
 */
class GiftArrangementHistory extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //会員へのリレーション: 1対多
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class);
    }

    //担当者へのリレーション
    public function staff(): BelongsTo
    {
        return $this->belongsTo(Staff::class);
    }

    //決済履歴へのリレーション: 多対多
    public function payment_histories(): BelongsToMany
    {
        return $this->belongsToMany(PaymentHistory::class, "payment_histories");
    }

    //引き出物カードバッグ手配履歴: 1対1
    public function gift_card_bag_arrangement_history(): HasOne
    {
        return $this->hasOne(GiftCardBagArrangementHistory::class);
    }

    //引き出物のし情報履歴: 1対1
    public function gift_wrap_info_history(): HasOne
    {
        return $this->hasOne(GiftWrapInfoHistory::class);
    }

    //引き出物Box情報履歴: 1対1
    public function gift_box_info_history(): HasOne
    {
        return $this->hasOne(GiftBoxInfoHistory::class);
    }

    //引き出物送付先情報履歴: 1対1
    public function gift_delivery_info_histories(): HasMany
    {
        return $this->HasMany(GiftDeliveryInfoHistory::class);
    }
}
