<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

//WEB招待状デザイン画像
class WebInvitationDesignImage extends Model
{
    use HasFactory, SoftDeletes, HasUuids;

    //WEB招待状共通マスタ
    public function m_web_invitation(): BelongsTo
    {
        return $this->belongsTo(MWebInvitation::class)->withTrashed();
    }

    //ファイルのパス取得
    public function getFilePathAttribute()
    {
        $filePath = env('AWS_DIR_WI') .
            $this->m_web_invitation->m_specification_product->product_id .
            "/" .
            $this->file_name;
        if (Storage::disk("s3_public")->exists($filePath)) {
            return Storage::disk("s3_public")->url($filePath);
        }
        return "";
    }
}
