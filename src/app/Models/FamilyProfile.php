<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\SoftDeletes;

class FamilyProfile extends Model
{
    use HasFactory, HasUuids, SoftDeletes;

    // ID以外のフィールドを一括で割り当て可能なフィルラブル属性
    protected $fillable = [
        "member_id",
        "last_name",
        "first_name",
        "last_name_kana",
        "first_name_kana",
        "last_name_romaji",
        "first_name_romaji",
        "birth_date",
        "order",
        "type",
    ];

    // リレーション：会員
    public function member(): BelongsTo
    {
        return $this->belongsTo(Member::class)->withTrashed();
    }
}
