<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class FileS3CrossBucketService
{
    private $privateTargetS3;
    private $publicTargetS3;

    public function __construct(string $privateTargetBucket, string $publicTargetBucket)
    {
        // 対象のS3設定
        $this->privateTargetS3 = Storage::build([
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => $privateTargetBucket,
            'url' => env('AWS_URL'),
            'throw' => true,
        ]);

        // 対象のS3設定
        $this->publicTargetS3 = Storage::build([
            'driver' => 's3',
            'key' => env('AWS_ACCESS_KEY_ID'),
            'secret' => env('AWS_SECRET_ACCESS_KEY'),
            'region' => env('AWS_DEFAULT_REGION'),
            'bucket' => $publicTargetBucket,
            'url' => env('AWS_COPY_TARGET_URL'),
            'throw' => true,
        ]);
    }

    /**
     * 単一ファイルを別バケットにコピー (privateバケット)
     */
    public function privateCopyFile(string $path): bool
    {
        try {
            $sourceContents = Storage::disk('s3')->get($path);
            return $this->privateTargetS3->put($path, $sourceContents);
        } catch (\Exception $e) {
            Log::error("Failed to copy file {$path}: " . $e->getMessage());
            return $e->getMessage();
        }
    }

    /**
     * 単一ファイルを別バケットにコピー (publicバケット)
     */
    public function publicCopyFile(string $path): bool
    {
        try {
            $sourceContents = Storage::disk('s3_public')->get($path);
            return $this->publicTargetS3->put($path, $sourceContents);
        } catch (\Exception $e) {
            Log::error("Failed to copy file {$path}: " . $e->getMessage());
            return $e->getMessage();
        }
    }
}
