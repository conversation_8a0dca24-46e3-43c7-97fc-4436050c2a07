<?php

namespace App\Services;

use Carbon\Carbon;
use App\Enums\FileType;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Imagick\Driver;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Models\Image;

class InstagramImage
{
    private $manager;
    private $designConfig;
    private $baseImage;
    private $groomName = null;
    private $brideName = null;
    private $eventDate = null;

    public function __construct()
    {
        $this->manager = new ImageManager(new Driver());
    }

    /**
     * デザイン画像を取得
     *
     * @param string $instagramType
     * @return
     */
    public function loadImage($instagramType)
    {
        $configPath = "instagrams/images/{$instagramType}.png";
        if (!Storage::disk('s3_public')->exists($configPath)) {
            throw new \Exception("Instagram種別画像が見つかりません: {$instagramType}.png");
        }
        return Storage::disk('s3_public')->url($configPath);
    }

    /**
     * 本日のデザイン画像を取得
     *
     * @param string $instagramType
     * @return
     */
    public function loadTodayImage($instagramType)
    {
        $configPath = "instagrams/images/{$instagramType}-today.png";
        if (!Storage::disk('s3_public')->exists($configPath)) {
            throw new \Exception("Instagram種別画像が見つかりません: {$instagramType}-today.png");
        }
        return Storage::disk('s3_public')->url($configPath);
    }

    /**
     * サムネイル画像を取得
     *
     * @param string $instagramType
     * @return
     */
    public function loadThumbnailImage($instagramType)
    {
        $configPath = "instagrams/images/{$instagramType}-thumbnail.png";
        if (!Storage::disk('s3_public')->exists($configPath)) {
            throw new \Exception("Instagram種別画像が見つかりません: {$instagramType}-thumbnail.png");
        }
        return Storage::disk('s3_public')->url($configPath);
    }

    /**
     * 本日のサムネイル画像を取得
     *
     * @param string $instagramType
     * @return
     */
    public function loadThumbnailTodayImage($instagramType)
    {
        $configPath = "instagrams/images/{$instagramType}-thumbnail-today.png";
        if (!Storage::disk('s3_public')->exists($configPath)) {
            throw new \Exception("Instagram種別画像が見つかりません: {$instagramType}-thumbnail-today.png");
        }
        return Storage::disk('s3_public')->url($configPath);
    }



    /**
     * Instagram画像を生成（バイナリデータとして返す）
     *
     * @param string $instagramType Instagram種別
     * @param array $imageData 画像データ（UUID、トリミング情報を含む）
     * @param bool $useGroomBrideName 新郎新婦名を認証ユーザーから取得するかどうか
     * @param bool $useEventDate 開催日を認証ユーザーから取得するかどうか
     * @return string
     */
    public function generate($instagramType, $imageData, $useGroomBrideName = true, $useEventDate = true)
    {
        // デザイン設定を読み込み
        $this->loadDesignConfig($instagramType);

        // 認証ユーザーを取得
        $member = Auth::user();

        // 新郎名・新婦名を取得
        if ($useGroomBrideName && $member) {
            $this->groomName = $member->groom_family_romaji_full_name;
            $this->brideName = $member->bride_family_romaji_full_name;
        }

        // 開催日を取得
        if ($useEventDate && $member) {
            $this->eventDate = $member->event_date;
        }

        // 開催日までの日数を計算
        $countdownDays = $this->calculateCountdownDays();
        $isEventDay = $countdownDays === 0;

        // 画像データから画像を読み込み
        $this->loadUserImageFromUploadedFile($imageData);

        // テキスト要素を追加（オーバーレイ画像の下に）
        $this->addTextElements($countdownDays, $isEventDay);

        // Instagram種別画像をテキストの上に重ね合わせ
        $this->addInstagramTypeImage($instagramType, $isEventDay, $countdownDays);

        // バイナリデータとして返す
        return $this->generateBinaryResponse();
    }

    /**
     * UIDベースのファイル名を生成
     *
     * @param string $extension ファイル拡張子（デフォルト: jpg）
     * @return string
     */
    public function generateUidFileName($extension = 'jpg')
    {
        $uid = Str::uuid()->toString();
        $timestamp = now()->format('Ymd_His');

        return "instagram_{$timestamp}_{$uid}.{$extension}";
    }

    /**
     * 画像を保存
     *
     * @param \Intervention\Image\Image $image
     * @param string $filename
     * @param int $quality
     * @return string 保存パス
     */
    public function saveImage($image, $filename = null, $quality = 95)
    {
        if (!$filename) {
            $filename = 'instagram_' . date('Y-m-d') . '.jpg';
        }

        $savePath = storage_path('app/public/instagram_generated/' . $filename);

        // ディレクトリが存在しない場合は作成
        if (!file_exists(dirname($savePath))) {
            mkdir(dirname($savePath), 0755, true);
        }

        // 画質向上処理
        $image->sharpen(10); // 軽いシャープネス処理

        // 高品質JPEG形式で保存
        $image->toJpeg($quality)->save($savePath);

        return $filename;
    }

    /**
     * デザイン設定を読み込み
     *
     * @param string $instagramType
     */
    private function loadDesignConfig($instagramType)
    {
        $configPath = "instagrams/settings/{$instagramType}.json";

        if (!Storage::disk('s3_public')->exists($configPath)) {
            throw new \Exception("Instagram設定ファイルが見つかりません: {$instagramType}.json");
        }

        $configContent = Storage::disk('s3_public')->get($configPath);
        $this->designConfig = json_decode($configContent, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("Instagram設定ファイルの形式が正しくありません");
        }
    }

    /**
     * 開催日までの日数を計算
     *
     * @return int
     */
    private function calculateCountdownDays()
    {
        $today = Carbon::today();
        if ($this->eventDate) {
            $event = Carbon::parse($this->eventDate);
        } else {
            $eventDate = Auth::user()->wedding_info->event_date;
            $event = Carbon::parse($eventDate);
        }

        return $today->diffInDays($event, false);
    }

    /**
     * 高品質リサイズ処理
     *
     * @param \Intervention\Image\Image $image
     * @param int $width
     * @param int $height
     */
    private function resizeWithQuality($image, $width, $height)
    {
        // 元の画像サイズを取得
        $originalWidth = $image->width();
        $originalHeight = $image->height();

        // アスペクト比を計算
        $originalRatio = $originalWidth / $originalHeight;
        $targetRatio = $width / $height;

        if ($originalRatio > $targetRatio) {
            // 横長画像：高さを基準にリサイズしてクロップ
            $newHeight = $height;
            $newWidth = $height * $originalRatio;
            // Imagickドライバーで高品質リサイズ（Lanczosフィルター相当）
            $image->scale($newWidth, $newHeight);

            // 中央でクロップ
            $image->crop($width, $height, ($newWidth - $width) / 2, 0);
        } else {
            // 縦長画像：幅を基準にリサイズしてクロップ
            $newWidth = $width;
            $newHeight = $width / $originalRatio;
            // Imagickドライバーで高品質リサイズ（Lanczosフィルター相当）
            $image->scale($newWidth, $newHeight);

            // 中央でクロップ
            $image->crop($width, $height, 0, ($newHeight - $height) / 2);
        }
    }

    /**
     * Instagram種別に応じた画像を重ね合わせ
     *
     * @param string $instagramType
     * @param bool $isEventDay
     */
    private function addInstagramTypeImage($instagramType, $isEventDay)
    {
        $imagePath = null;
        if ($isEventDay) {
            // 開催日当日の場合、数字-today.png画像を使用
            $imagePath = "instagrams/images/{$instagramType}-today.png";
        } else {
            $imagePath = "instagrams/images/{$instagramType}.png";
        }

        if (!Storage::disk('s3_public')->exists($imagePath)) {
            throw new \Exception("Instagram種別画像が見つかりません: {$instagramType}.png");
        }

        $imageContent = Storage::disk('s3_public')->get($imagePath);
        $overlay = $this->manager->read($imageContent);

        // オーバーレイ画像を1080x1080にリサイズ
        $overlay->resize(1080, 1080);

        // アップロード画像の上に重ね合わせ
        $this->baseImage->place($overlay, 'top-left', 0, 0);
    }

    /**
     * テキスト要素を追加
     *
     * @param int $countdownDays
     * @param bool $isEventDay
     */
    private function addTextElements($countdownDays, $isEventDay)
    {
        $textElements = $this->designConfig['text_elements'] ?? [];

        foreach ($textElements as $element) {
            $this->addTextElement($element, $countdownDays, $isEventDay);
        }
    }

    /**
     * テキスト要素を追加
     *
     * @param array $element
     * @param int $countdownDays
     * @param bool $isEventDay
     */
    private function addTextElement($element, $countdownDays, $isEventDay)
    {
        $text = $this->generateTextContent($element, $countdownDays, $isEventDay);

        if (empty($text)) {
            return;
        }

        $x = $element['x'] ?? 0;
        $y = $element['y'] ?? 0;

        $this->baseImage->text($text, $x, $y, function ($font) use ($element) {
            // フォント設定
            $fontFile = $element['font_file'] ?? 'NotoSansJP-VariableFont_wght.ttf';
            $fontPath = "instagrams/fonts/{$fontFile}";
            if (Storage::disk('s3_public')->exists($fontPath)) {
                // S3からフォントファイルを一時的にダウンロードして使用
                $tempFontPath = tempnam(sys_get_temp_dir(), 'font_');
                file_put_contents($tempFontPath, Storage::disk('s3_public')->get($fontPath));
                $font->file($tempFontPath);
            }

            // フォントサイズ
            $fontSize = $element['font_size'] ?? 32;
            $font->size($fontSize);

            // 色設定
            $font->color($element['color'] ?? '#ffffff');

            // 配置設定
            $align = $element['align'] ?? 'center';
            $font->align($align);

            $valign = $element['valign'] ?? 'middle';
            $font->valign($valign);
        });
    }

    /**
     * テキスト内容を生成
     *
     * @param array $element
     * @param int $countdownDays
     * @param bool $isEventDay
     * @return string|null
     */
    private function generateTextContent($element, $countdownDays, $isEventDay)
    {
        $type = $element['type'] ?? '';

        switch ($type) {
            case 'couple_names':
                if ($this->groomName === null || $this->brideName === null) {
                    return null;
                }

                $middle = $element['middle'] ?? ' & ';
                return $this->groomName . $middle . $this->brideName;

            case 'event_date':
                if ($this->eventDate === null) {
                    return null;
                }
                $dateFormat = $element['date_format'] ?? 'Y.m.d';
                return Carbon::parse($this->eventDate)->format($dateFormat);

            case 'countdown_days':
                return $isEventDay ? '0' : (string)$countdownDays;

            default:
                return '';
        }
    }

    /**
     * バイナリレスポンスを生成
     *
     * @return string
     */
    private function generateBinaryResponse()
    {
        // 画質向上処理
        $this->baseImage->sharpen(10); // 軽いシャープネス処理

        // 高品質JPEG形式でバイナリデータを取得
        return $this->baseImage->toJpeg(95)->toString();
    }

    /**
     * トリミング処理
     *
     * @param array $imageData
     */
    private function cropImage(array $imageData)
    {
        // 拡大率をもとに座標を計算
        $scaleFactor = $imageData['scaleFactor'];

        // 実際のトリミング座標を計算
        $cropX = (int)($imageData['cropBoxLeft'] * $scaleFactor);
        $cropY = (int)($imageData['cropBoxTop'] * $scaleFactor);
        $cropWidth = (int)($imageData['cropBoxWidth'] * $scaleFactor);
        $cropHeight = (int)($imageData['cropBoxHeight'] * $scaleFactor);

        // 画像の境界内に収まるように調整
        $imageWidth = $this->baseImage->width();
        $imageHeight = $this->baseImage->height();

        $cropX = max(0, min($cropX, $imageWidth - 1));
        $cropY = max(0, min($cropY, $imageHeight - 1));
        $cropWidth = min($cropWidth, $imageWidth - $cropX);
        $cropHeight = min($cropHeight, $imageHeight - $cropY);

        // 実際のトリミング実行
        if ($cropWidth > 0 && $cropHeight > 0) {
            $this->baseImage->crop($cropWidth, $cropHeight, $cropX, $cropY);
        }
    }

    /**
     * 画像を読み込み
     *
     * @param array|null $imageData 画像データ
     */
    private function loadUserImageFromUploadedFile(?array $imageData = null)
    {
        // $imageDataがnullまたはuuidが指定されていない場合はグレー背景画像を作成
        if (!$imageData || !isset($imageData['uuid']) || empty($imageData['uuid'])) {
            $this->baseImage = $this->manager->create(1080, 1080)->fill('#D9D9D9');
            return;
        }

        // uuidから画像を取得
        $image = Image::where('uuid', $imageData['uuid'])
            ->where('hidden_at', null)
            ->where(function ($query) {
                $userId = Auth::user()->id;
                $query->where('owner_id', $userId)
                      ->orWhereIn('file_type', [FileType::FILE_TYPE_MATERIAL]);
            })
            ->first();

        // uuidが指定されているのに画像が見つからない場合はエラー
        if (!$image) {
            throw new \Exception("UUID「{$imageData['uuid']}」の画像が見つかりません");
        }

        // 画像の種類に応じてパスを決定
        $imagePath = null;
        if ($image->file_type == FileType::FILE_TYPE_MATERIAL) {
            $adminPath = "admin/{$image->name}_m.{$image->extension_type}";
            if (Storage::disk('s3')->exists($adminPath)) {
                $imagePath = $adminPath;
            }
        } else {
            $memberPath = "member/{$image->owner_id}/{$image->name}_m.{$image->extension_type}";
            if (Storage::disk('s3')->exists($memberPath)) {
                $imagePath = $memberPath;
            }
        }

        try {
            $imageContent = Storage::disk('s3')->get($imagePath);
            $this->baseImage = $this->manager->read($imageContent);
        } catch (\Exception $e) {
            throw new \Exception("UUID「{$imageData['uuid']}」の画像取得に失敗しました: " . $e->getMessage());
        }

        // 画像データが存在する場合のみ回転・トリミング処理を実行
        if ($imageData) {
            // 回転処理
            if (isset($imageData['rotate']) && $imageData['rotate'] != 0) {
                $this->baseImage->rotate(-$imageData['rotate']);
            }

            // トリミング処理
            if (
                isset($imageData['cropBoxLeft']) && isset($imageData['cropBoxTop']) &&
                isset($imageData['cropBoxWidth']) && isset($imageData['cropBoxHeight']) &&
                isset($imageData['scaleFactor'])
            ) {
                $this->cropImage($imageData);
            }
        }

        // 高品質リサイズ処理
        $this->resizeWithQuality($this->baseImage, 1080, 1080);
    }
}
