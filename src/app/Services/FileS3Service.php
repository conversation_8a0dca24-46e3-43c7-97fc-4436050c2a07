<?php

namespace App\Services;

use App\Consts\SystemConst;
use Illuminate\Support\Facades\Auth;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use App\Enums\OwnerType;
use App\Enums\ImageTypeEnum;
use App\Models\Image;
use Illuminate\Support\Str;
use Imagick;
use ProtoneMedia\LaravelFFMpeg\Support\FFMpeg;
use FFMpeg\Coordinate\TimeCode;

//AWS S3に保存 バイナリアップロード
class FileS3Service
{
    private $storage;
    private $config;
    private $userId;
    private $modelName;
    private $ownerType;
    private $dir;
    private $fileData;
    private $fileName;
    private $extension;
    private $imageType;

    public const LENGTH_S = 480;
    public const LENGTH_M = 1440;
    public const LENGTH_L = 2880;

    public const SIZE_LIST = [
        "s" => self::LENGTH_S,
        "m" => self::LENGTH_M,
        "l" => self::LENGTH_L,
    ];

    /**
     * 初期化
     * @param int|string $user_id 会員ID(ゲストアップロード時)
     */
    public function __construct($user_id = null)
    {
        //S3の設定を取得
        $this->storage = Storage::disk("s3");
        $this->config = $this->storage->getConfig();

        //オーナー種類取得
        if ($user_id) {
            $this->ownerType = OwnerType::OWNER_TYPE_MEMBER;
            $this->userId = $user_id;

            $this->dir =
                env("AWS_DIR_MEMBER") .
                "{$user_id}" .
                "/" .
                env("AWS_DIR_GUEST");
        } else {
            $user = Auth::user();
            if (!$user) {
                throw new AuthenticationException();
            }
            $this->modelName = class_basename($user);
            $this->ownerType = OwnerType::getValue($this->modelName);
            $this->userId = $user->id;

            //パス作成
            $this->dir = env("AWS_DIR_MEMBER") . "{$this->userId}";
            if ($this->ownerType == OwnerType::OWNER_TYPE_ADMIN) {
                $this->dir = env("AWS_DIR_ADMIN");
            }
        }
    }

    /**
     * ファイルタイプによる認証チェック
     *
     * @param int $fileType
     */
    public function checkUserFileType($fileType)
    {
        if (
            $this->ownerType == OwnerType::OWNER_TYPE_MEMBER &&
            $fileType >= 30
        ) {
            throw new AuthenticationException();
        } elseif (
            $this->ownerType == OwnerType::OWNER_TYPE_ADMIN &&
            $fileType < 30
        ) {
            throw new AuthenticationException();
        }
    }

    /**
     * ファイルをS3(minio)に保存 新しい関数
     *
     * @param $fileData
     * @param string $fileName ファイル名(UUID)
     * @return string ファイルパス
     */
    public function fileMove($fileData, $fileName)
    {
        return $this->storage->putFileAs(
            rtrim($this->dir, "/"),
            $fileData,
            $fileName . "." . $this->extension,
        );
    }

    /**
     * ファイル管理テーブルに保存
     *
     * @param string $fileName ファイル名(UUID)
     * @param int $fileType ファイルタイプ
     * @return object Image
     */
    public function imageSave($fileName, $fileType)
    {
        $image = new Image();
        $image->owner_type = $this->ownerType;
        $image->owner_id = $this->userId;
        $image->file_type = $fileType;
        $image->type = $this->imageType;
        $image->name = $fileName;
        $image->extension_type = $this->extension;
        $image->save();

        return $image;
    }

    /////////////////////////////////////
    // セッター
    /////////////////////////////////////

    /**
     *ファイル情報設定(拡張子も)
     *
     * @return void
     */
    public function setFileData($fileData)
    {
        $this->fileData = $fileData;
        $this->fileName = Image::createFileName();
        $this->extension = $this->fileData->getClientOriginalExtension();
        $this->imageType = str_starts_with($fileData->getMimeType(), "image/")
            ? ImageTypeEnum::IMAGE
            : ImageTypeEnum::VIDEO;
    }

    /////////////////////////////////////
    // ゲッター
    /////////////////////////////////////

    /**
     * ディレクト取得
     * @return string
     */
    public function getDir()
    {
        return rtrim($this->dir, "/");
    }

    /**
     * uuidを作成・取得
     * @return string
     */
    public function getUuid()
    {
        return (string) Str::uuid();
    }

    /**
     * ファイル名を取得
     * @return string
     */
    public function getFileName()
    {
        return $this->fileName;
    }

    /**
     * 拡張子を取得
     * @return string
     */
    public function getExtension()
    {
        return $this->extension;
    }

    /**
     * 画像・動画種別を取得
     * @return string
     */
    public function getImageType()
    {
        return $this->imageType;
    }

    /**
     * ファイル名+サイズ+拡張子を取得
     * @return string
     */
    public function getFileNameSizeExtension($size)
    {
        return "{$this->fileName}_{$size}.{$this->extension}";
    }

    /**
     * ファイル名+拡張子を取得
     * @return string
     */
    public function getFileNameExtension()
    {
        return "{$this->fileName}.{$this->extension}";
    }

    /**
     * 一時付き署名URL作成
     *
     * @param string $path
     * @return string
     */
    public function getPresignedUrl($path)
    {
        if ($this->config["use_path_style_endpoint"]) {
            $endpoint = Str::match("/https?:\/\/[^\/]*/", $this->config["url"]);
            $this->storage = Storage::build(
                Arr::set($this->config, "endpoint", $endpoint)
            );
        }

        return Storage::disk('s3')->url($path);
    }

    /**
     * ファイルとデータを複製する
     *
     * @param string $uuid 複製元のuuid
     * @return object
     */
    public function copyFile($uuid)
    {
        $image = Image::where("uuid", $uuid)->first();

        $newUuid = Image::createFileName();
        $clonedImage = $image->replicate();
        $clonedImage->name = $newUuid;
        $clonedImage->save();

        foreach (self::SIZE_LIST as $size => $length) {
            $sourcePath =
                $this->dir .
                "/" .
                $image->name .
                "_" .
                $size .
                "." .
                $image->extension_type;
            $clonePath =
                $this->dir .
                "/" .
                $newUuid .
                "_" .
                $size .
                "." .
                $image->extension_type;
            $this->storage->copy($sourcePath, $clonePath);
        }
        return $clonedImage;
    }

    /**
     * 複数画像削除
     *
     * @param array $uuids
     */
    public function deleteImages(array $uuids)
    {
        $images = Image::whereIn("uuid", $uuids)->get();
        if ($images->isNotEmpty()) {
            foreach ($images as $row) {
                foreach (self::SIZE_LIST as $size => $length) {
                    Storage::delete(
                        $this->dir .
                            "/" .
                            $row->name .
                            "_" .
                            $size .
                            "." .
                            $row->extension_type
                    );
                }
                $row->delete();
            }
        }
    }

    /**
     * 画像を圧縮してリサイズする
     *
     * @param object $fileData ファイル情報
     * @param int $length リサイズする長辺の長さ
     */
    public function imageCompression($fileData, $length)
    {
        // 画像を読み込む
        $imagick = new Imagick($fileData->getRealPath());

        // 画像の現在の寸法を取得
        $width = $imagick->getImageWidth();
        $height = $imagick->getImageHeight();

        // 長辺を計算
        $longestSide = max($width, $height);

        //長辺が$length以上ならリサイズ
        if ($longestSide > $length) {
            // 長辺が定数の値になるようにリサイズするための比率を計算
            $scale = $length / $longestSide;

            // 新しい幅と高さを計算
            $newWidth = $width * $scale;
            $newHeight = $height * $scale;

            // 画像をリサイズ
            $imagick->resizeImage(
                $newWidth,
                $newHeight,
                Imagick::FILTER_LANCZOS,
                1
            );
        }

        //圧縮(1 から 100 の間の int 1 = 高圧縮品質。 100 = 低圧縮品質。を表します。)
        // $imagick->setCompressionQuality(100);
        $imagick->setImageCompressionQuality(70);

        //一時ディレクトリに保存
        $tempPath =
            tempnam(sys_get_temp_dir(), "upload") .
            "." .
            $fileData->getClientOriginalExtension();
        $imagick->writeImage($tempPath);

        //リソース解放
        $imagick->clear();
        $imagick->destroy();

        return $tempPath;
    }

    /**
     * 画像をリサイズする
     *
     * @param object $fileData ファイル情報
     * @param int $length リサイズする長辺の長さ
     */
    public function resize($fileData, $length)
    {
        // 画像を読み込む
        $imagick = new Imagick($fileData->getRealPath());

        // 画像の現在の寸法を取得
        $width = $imagick->getImageWidth();
        $height = $imagick->getImageHeight();

        // 長辺を計算
        $longestSide = max($width, $height);

        $tempPath =
            tempnam(sys_get_temp_dir(), "upload") .
            "." .
            $fileData->getClientOriginalExtension();

        //長辺が$length超えるならリサイズ
        if ($longestSide > $length) {
            // 長辺が定数の値になるようにリサイズするための比率を計算
            $scale = $length / $longestSide;

            // 新しい幅と高さを計算
            $newWidth = (int) round($width * $scale);
            $newHeight = (int) round($height * $scale);

            // 画像をリサイズ
            $imagick->resizeImage(
                $newWidth,
                $newHeight,
                Imagick::FILTER_TRIANGLE,
                1
            );
        }
        //一時ディレクトリに保存
        $imagick->writeImage($tempPath);

        //リソース解放
        $imagick->clear();
        $imagick->destroy();

        return $tempPath;
    }

    /**
     * 一時ファイルに保存する
     *
     * @param object $fileData
     * @return string
     */
    public function saveToTemporaryFile($fileData)
    {
        $fileName =
            uniqid("upload_", true) .
            "." .
            $fileData->getClientOriginalExtension();
        $tempPath = "tmp/" . $fileName;

        // S3のtmpディレクトリにアップロード
        Storage::disk("s3")->putFileAs(
            "tmp",
            $fileData,
            $fileName,
        );

        // アップロードしたファイルのS3のパスを返す
        return $tempPath;
    }

    /**
     * 動画を圧縮して保存
     *
     * @param string $tempPath
     * @return string
     */
    public function saveVideoCompression($tempPath)
    {
        $path =
            $this->getDir() . "/" . $this->fileName . "." . $this->extension;
        FFMpeg::fromDisk("s3")
            ->open($tempPath)
            ->export()
            ->toDisk("s3")
            ->inFormat(new \FFMpeg\Format\Video\X264())
            ->save($path);
        return $path;
    }

    /**
     * 動画のサムネイルを取得して保存
     *
     * @param string $tempPath
     * @return string
     */
    public function saveVideoChangeImage($tempPath)
    {
        $path = $this->getDir() . "/" . $this->fileName . "_s.jpg";
        FFMpeg::fromDisk("s3")
            ->open($tempPath)
            ->export()
            ->toDisk("s3")
            ->frame(TimeCode::fromSeconds(1))
            ->save($path);

        $s3Content = Storage::disk("s3")->get($path);
        // Imagickを使用して画像をリサイズ
        $imagick = new Imagick();
        $imagick->readImageBlob($s3Content);
        $width = $imagick->getImageWidth();
        $height = $imagick->getImageHeight();
        $longestSide = max($width, $height);
        if ($longestSide > SystemConst::LENGTH_S) {
            $scale = SystemConst::LENGTH_S / $longestSide;

            $newWidth = (int) ($width * $scale);
            $newHeight = (int) ($height * $scale);

            $imagick->resizeImage(
                $newWidth,
                $newHeight,
                Imagick::FILTER_TRIANGLE,
                1
            );
            Storage::disk("s3")->put(
                $path,
                (string) $imagick->getImageBlob(),
            );
        }

        $imagick->clear();
        $imagick->destroy();

        return $path;
    }

    /**
     * ディレクトリ以下のファイルを全てコピー
     *
     * @param string $oldDirectory 複製元のディレクトリ
     * @param string $newDirectory 複製先のディレクトリ
     * @return void
     */
    function moveFilesToNewDirectory($oldDirectory, $newDirectory)
    {
        // 新しいディレクトリを作成
        if (!$this->storage->exists($newDirectory)) {
            $this->storage->makeDirectory($newDirectory);
        }

        // 元のディレクトリ内のファイルを取得
        $files = $this->storage->files($oldDirectory);

        // ファイルの新しいパスを生成してコピー
        foreach ($files as $file) {
            $newPath = str_replace($oldDirectory, $newDirectory, $file);
            $this->storage->copy($file, $newPath);
        }
    }
}
