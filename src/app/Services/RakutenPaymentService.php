<?php
namespace App\Services;

use GuzzleHttp\Client;
use Carbon\Carbon;

class RakutenPaymentService
{
    protected $serviceId;
    protected $secretKey;
    protected $apiEndpoint;

    public function __construct()
    {
        $this->serviceId = config("services.rakuten_payment.service_id");
        $this->secretKey = config("services.rakuten_payment.secret_key");
        $this->apiEndpoint = config("services.rakuten_payment.api_endpoint");
    }

    // https://docs.payment.global.rakuten.com/ja/guides/payvault/
    // https://docs.payment.global.rakuten.com/ja/library/
    // 楽天のAPIリクエストを作成し、送信するロジックを実装
    // 以下の3点を渡す
    // paymentId : 貴社が自由に指定できる決済取引に対するユニークなIDです 。
    // cardToken : ペイボルトで発行したクレジットカードのトークンを設定します。
    // amount : 決済金額
    public function makePayment($order = [])
    {
        $data = [];
        $data["serviceId"] = $this->serviceId;
        $data["subServiceId"] = $this->serviceId;
        $data["timestamp"] = Carbon::now("UTC")->format("Y-m-d H:i:s.000");
        $data["paymentId"] = $order["paymentId"];
        $data["currencyCode"] = "JPY";
        $data["grossAmount"] = $order["amount"];
        $data["agencyCode"] = "rakutencard";
        $data["callbackUrl"] = config("app.url");
        $data["cardToken"] = [];
        $data["cardToken"]["version"] = 2;
        $data["cardToken"]["amount"] = $order["amount"];
        $data["cardToken"]["cardToken"] = $order["cardToken"];
        $data["cardToken"]["withThreeDSecure"] = false;
        $data["order"] = [];
        $data["order"]["version"] = 1;
        $data["order"]["email"] = "rakuten@card";
        // ユーザーのIPアドレスを指定します。
        $data["order"]["ipAddress"] = $_SERVER["REMOTE_ADDR"];
        // カスタム : 貴社で自由にJSONデータを設定できる項目です。ただしクレジットカード番号などの機密情報は設定しないでください。
        // $data["custom"] = '';

        // AuthorizeAndCapture をする
        // AuthorizeAndCaptureを使用した場合、トランザクションの決済ステータスは決済完了後、即座に"captured"になります。
        // Responses は、↓を参考に
        // https://docs.payment.global.rakuten.com/ja/products/api-reference/pgw3/#tag/Credit-Card-Rakuten-Card/paths/~1Credit%20Card~1Authorize/postあhttps://docs.payment.global.rakuten.com/ja/products/api-reference/pgw3/#tag/Credit-Card-Rakuten-Card/paths/~1Credit%20Card~1AuthorizeAndCapture/post
        return $this->_request("V1/AuthorizeAndCapture", $data);
    }

    private function _request($uri = "", $data = [])
    {
        $url = $this->apiEndpoint . $uri;
        $json = json_encode($data);

        $post = [];
        $post["paymentinfo"] = base64_encode($json);
        $post["signature"] = hash_hmac("sha256", $json, $this->secretKey);
        $post["key_version"] = 1;

        $headers = ["Content-Type", "application/x-www-form-urlencoded"];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($post));
        $json = curl_exec($ch);
        return json_decode($json, true);
    }
}
