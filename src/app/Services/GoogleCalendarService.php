<?php

namespace App\Services;

class GoogleCalendarService
{
    /**
     * Googlカレンダーのリンクを作成して返す
     *
     * @param string $eventTitle イベント名
     * @param string $startDate 開催時刻
     * @param string $endDate 終了時刻
     * @param array $descriptions 詳細
     * @param string $location 住所
     * @return string
     */
    public static function getEventLink(
        $eventTitle,
        $startDate,
        $endDate,
        $descriptions,
        $location
    ): string {
        $template = <<<EOD
謹啓

皆様におかれましては
ますますご清祥のこととお慶び申し上げます

このたび　私たちは結婚をすることになりました
つきましては　日頃お世話になっております皆様に
感謝を込めて　ささやかな小宴を催したく存じます

ご多用中　誠に恐縮ではございますが
ぜひご出席をいただきたく　ご案内申し上げます

謹白
EOD;

        $details = urlencode($template) . "%0A";
        foreach ($descriptions as $description) {
            $details .= urlencode($description) . "%0A";
        }
        return "https://www.google.com/calendar/render?action=TEMPLATE&text={$eventTitle}&dates={$startDate}/{$endDate}&details={$details}&location={$location}";
    }
}
