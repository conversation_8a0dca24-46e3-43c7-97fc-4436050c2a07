<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\StreamedResponse;
use ZipArchive;
use Carbon\Carbon;
class CsvService
{
    /**
     * CSVファイルを配列に変換する
     *
     * @param string $filePath CSVファイルのパス
     * @param string $toEncoding
     * @param string $fromEncoding
     * @return array
     */
    public function convertToArray(
        string $filePath,
        string $toEncoding = "UTF-8",
        string $fromEncoding = "SJIS-win"
    ): array {
        $file = new \SplFileObject($filePath);
        $file->setFlags(
            \SplFileObject::READ_CSV |
                \SplFileObject::SKIP_EMPTY |
                \SplFileObject::DROP_NEW_LINE |
                \SplFileObject::READ_AHEAD
        );

        $csvData = [];
        $header = [];
        foreach ($file as $line) {
            if (empty($header)) {
                $header = $line;
                continue;
            }
            $csvData[] = $line;
        }

        return [
            mb_convert_encoding($header, $toEncoding, $fromEncoding),
            mb_convert_encoding($csvData, $toEncoding, $fromEncoding),
        ];
    }

    /**
     * CSV ダウンロード
     *
     * @param array $header
     * @param object $data
     * @param string $fileName
     * @param string $fileEncoding
     * @return StreamedResponse
     */
    public function export(
        $header,
        $data,
        $fileName = "sample.csv",
        $fileEncoding = "Shift-JIS"
    ) {
        return new StreamedResponse(
            function () use ($header, $data, $fileEncoding) {
                $handle = fopen("php://output", "w");

                if (!empty($header)) {
                    $encodedHeader = array_map(function ($item) use (
                        $fileEncoding
                    ) {
                        return mb_convert_encoding(
                            $item,
                            $fileEncoding,
                            "UTF-8"
                        );
                    }, $header);
                    fputcsv($handle, $encodedHeader);
                }

                foreach ($data as $row) {

                    if (is_array($row) && isset($row[0]) && is_array($row[0])) {
                        // 多次元配列の場合
                        foreach ($row as $subRow) {
                            $encodedRow = array_map(function ($item) use ($fileEncoding) {
                                return mb_convert_encoding(
                                    str_replace(' ', '　', $item),
                                    $fileEncoding,
                                    "UTF-8"
                                );
                            }, $subRow);
                            fputcsv($handle, $encodedRow);
                        }
                    } else {
                        // 一次元配列の場合
                        $encodedRow = array_map(function ($item) use ($fileEncoding) {
                            return mb_convert_encoding(
                                str_replace(' ', '　', $item),
                                $fileEncoding,
                                "UTF-8"
                            );
                        }, $row);
                        fputcsv($handle, $encodedRow);
                    }
                }

                fclose($handle);
            },
            200,
            [
                "Content-Type" => "text/csv; charset=" . $fileEncoding,
                "Content-Disposition" => "attachment; filename={$fileName}",
                "Access-Control-Expose-Headers" => "Content-Disposition",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0",
            ]
        );
    }

    /**
     * CSV を ZIP に圧縮してダウンロード（メモリ上で処理）
     *
     * @param array $groupedData [ "YYYYMMDD" => [$data], ... ]
     * @param string $zipFileName
     * @param string $fileEncoding
     * @return StreamedResponse
     */
    public function exportZip(
        array $header,
        array $groupedData,
        string $zipFileName = "csv_files.zip",
        string $fileEncoding = "Shift-JIS"
    )
    {
        $zip = new \ZipArchive();
        $zipStream = tempnam(sys_get_temp_dir(), 'zip'); // 一時ファイルを作成
        if ($zip->open($zipStream, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) !== true) {
            throw new \Exception("ZIP ファイルを作成できませんでした");
        }

        // メモリ上で CSV を作成し ZIP に追加
        foreach ($groupedData as $date => $data) {
            $fileName = "{$date}.csv";
            $csvContent = $this->generateCsvString($header, $data, $fileEncoding);
            $zip->addFromString($fileName, $csvContent);
        }

        $zip->close();

        // ZIP ファイルをストリームレスポンスで返す
        return response()->stream(
            function () use ($zipStream) {
                readfile($zipStream);
                unlink($zipStream); // ZIP ファイルを削除
            },
            200,
            [
                "Content-Type" => "application/zip",
                "Content-Disposition" => "attachment; filename={$zipFileName}",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0",
            ]
        );
    }

    /**
    * CSV を文字列として生成
    *
    * @param array $header
    * @param array $data
    * @param string $fileEncoding
    * @return string
    */
    private function generateCsvString(array $header, array $data, string $fileEncoding = "Shift-JIS"): string
    {
        ob_start();
        $handle = fopen("php://output", "w");

        if (!empty($header)) {
            $encodedHeader = array_map(fn($item) => mb_convert_encoding($item, $fileEncoding, "UTF-8"), $header);
            fputcsv($handle, $encodedHeader);
        }

        foreach ($data as $row) {
            $encodedRow = array_map(fn($item) => mb_convert_encoding(str_replace(' ', '　', $item), $fileEncoding, "UTF-8"), $row);
            fputcsv($handle, $encodedRow);
        }

        fclose($handle);
        return ob_get_clean(); // バッファ内容を取得しクリア
    }
}
