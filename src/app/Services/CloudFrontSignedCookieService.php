<?php

namespace App\Services;

use Aws\CloudFront\CloudFrontClient;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cookie;

// CloudFront署名付きCookieを生成
class CloudFrontSignedCookieService
{
    private CloudFrontClient $client;
    private $member_id = null;

    public function __construct($member_id)
    {
        $this->client = new CloudFrontClient([
            "region" => "us-east-1",
            "version" => "latest",
            "credentials" => false,
        ]);
        $this->member_id = $member_id;
    }

    /**
     * Cookieセット
     *
     * @return void
     */
    public function setSignedCookies()
    {
        $cookies = $this->createSignedCookies();
        foreach ($cookies as $name => $value) {
            setcookie(
                $name,
                $value,
                [
                    'expires' => time() + Config::get("services.cloud_front.expiry"),
                    'path' => '/',
                    'domain'   => Config::get("services.cloud_front.domain"),
                    'secure'   => Config::get("services.cloud_front.secure"),
                    'httponly' => true, // JavaScriptからアクセス禁止
                    'samesite' => 'None',
                ]
            );
        }
    }

    /**
     * Cookie作成
     *
     * @return array
     */
    private function createSignedCookies(): array
    {
        $expires =
            time() + Config::get("services.cloud_front.expiry");

        $policy = json_encode([
            "Statement" => [
                [
                    "Resource" => Config::get("services.cloud_front.resource_url").$this->member_id.'/*',
                    "Condition" => [
                        "DateLessThan" => ["AWS:EpochTime" => $expires],
                    ],
                ],
            ],
        ]);

        return $this->client->getSignedCookie([
            "policy" => $policy,
            'private_key' => storage_path(Config::get('services.cloud_front.private_key_path')),
            'key_pair_id' => Config::get("services.cloud_front.key_pair_id"),
        ]);
    }
}
