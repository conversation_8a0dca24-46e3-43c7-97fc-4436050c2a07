<?php

namespace App\Services;

use App\Models\PaymentLog;
use App\Consts\SystemConst;
use Illuminate\Support\Facades\Request;
use App\Services\UuidGeneratorService;

// 決済ログ
class PaymentLogService
{
    // 3D入力パラメーターのログを保存
    public function log3dInputParameters($guestName, $inputParameters)
    {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_3D_INPUT_PARAMETER,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "ip_address" => Request::ip(),
        ]);
    }

    // 取引登録
    public function logENTRY(
        $guestName,
        $paymentPostParameters,
        $orderId,
        $inputParameters,
        $gmoResult
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_3D_ENTRY,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $orderId,
            "payment_post_parameters" => $paymentPostParameters,
            "payment_post_results" => $gmoResult,
            "ip_address" => Request::ip(),
        ]);
    }

    // 取引開始
    public function logEXEC(
        $guestName,
        $paymentPostParameters,
        $orderId,
        $inputParameters,
        $gmoResult
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_3D_EXEC,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $orderId,
            "payment_post_parameters" => $paymentPostParameters,
            "payment_post_results" => $gmoResult,
            "ip_address" => Request::ip(),
        ]);
    }

    // 例外エラー(3Dセキュア)
    public function log3DError(
        $guestName,
        $paymentPostParameters,
        $orderId,
        $inputParameters,
        $gmoResultEntry,
        $gmoResultExec,
        $errorMessage
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_3D_ERROR,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $orderId,
            "payment_post_parameters" => $paymentPostParameters,
            "payment_post_results" => [
                "entry" => $gmoResultEntry,
                "exec" => $gmoResultExec,
            ],
            "error_results" => $errorMessage,
            "ip_address" => Request::ip(),
        ]);
    }

    // 入力パラメーターのログを保存
    public function logInputParameters($guestName, $inputParameters)
    {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_WEB_INVITAION_INPUT_PARAMETER,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "ip_address" => Request::ip(),
        ]);
    }

    // ゲスト登録前のログを保存
    public function logGuestRegistration(
        $memberId,
        $webInvitationId,
        $guestName,
        $inputParameters
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" =>
                SystemConst::PAYMENT_LOG_WEB_INVITAION_GUEST_PRE_REGISTRATION,
            "member_id" => $memberId,
            "web_invitation_id" => $webInvitationId,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "ip_address" => Request::ip(),
        ]);
    }

    // 決済済みログを保存
    public function logPaymentCompleted(
        $memberId,
        $webInvitationId,
        $guestName,
        $paymentId,
        $paymentPostParameters,
        $inputParameters
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_WEB_INVITAION_PAYMENT_COMPLETED,
            "member_id" => $memberId,
            "web_invitation_id" => $webInvitationId,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $paymentId,
            "payment_post_parameters" => $paymentPostParameters,
            "ip_address" => Request::ip(),
        ]);
    }

    // 3Dセキュアの結果（成功/失敗）のログを保存
    public function log3DSecureResult(
        $memberId,
        $webInvitationId,
        $guestName,
        $paymentPostParameters,
        $inputParameters,
        $gmoResult
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_WEB_INVITAION_SECURE_3D,
            "member_id" => $memberId,
            "web_invitation_id" => $webInvitationId,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $gmoResult["OrderID"] ?? null,
            "payment_post_parameters" => $paymentPostParameters,
            "payment_post_results" => $gmoResult,
            "ip_address" => Request::ip(),
        ]);
    }

    // APIの成功/失敗の結果をログ保存
    public function logApiResult(
        $memberId,
        $webInvitationId,
        $guestName,
        $paymentPostParameters,
        $inputParameters,
        $gmoResult,
        $apiResult
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_WEB_INVITAION_API_RESULT,
            "member_id" => $memberId,
            "web_invitation_id" => $webInvitationId,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $gmoResult["OrderID"] ?? null,
            "payment_post_parameters" => $paymentPostParameters,
            "payment_post_results" => $gmoResult,
            "api_results" => $apiResult,
            "ip_address" => Request::ip(),
        ]);
    }

    // 例外エラー(WEB招待状)
    public function logWebInvitationError(
        $memberId,
        $webInvitationId,
        $guestName,
        $paymentPostParameters,
        $inputParameters,
        $gmoResult,
        $apiResult,
        $errorMessage
    ) {
        return PaymentLog::create([
            "id" => UuidGeneratorService::generateOrderedUuid(),
            "type" => SystemConst::PAYMENT_LOG_WEB_INVITAION_ERROR,
            "member_id" => $memberId,
            "web_invitation_id" => $webInvitationId,
            "guest_name" => $guestName,
            "input_parameters" => $inputParameters,
            "payment_id" => $gmoResult["OrderID"] ?? null,
            "payment_post_parameters" => $paymentPostParameters,
            "payment_post_results" => $gmoResult,
            "api_results" => $apiResult,
            "error_results" => $errorMessage,
            "ip_address" => Request::ip(),
        ]);
    }
}
