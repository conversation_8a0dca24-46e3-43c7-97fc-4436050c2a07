<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;

//laravel ストレージ内に保存
class FileService
{
    private $storage;

    //
    public const DIR_M_WEB_INVITAION = "wi";

    /**
     * 初期化
     */
    public function __construct()
    {
        $this->storage = Storage::disk("public");
    }

    /**
     * ディレクトリの作成
     *
     * @param string $path
     */
    public function makeDirectory($path)
    {
        if (!$this->storage->exists($path)) {
            $this->storage->makeDirectory($path);
            chmod(storage_path("app/public/" . $path), 0777);
        }
    }

    /**
     * 指定したファイルに保存
     *
     * @param string $path ディレクトリパス
     * @param object $file ファイルのインスタンス
     * @param string $fileName ファイル名
     * @return string
     */
    public function save($path, $file, $fileName)
    {
        return $this->storage->putFileAs($path, $file, $fileName);
    }

    /**
     * ディレクトリ以下のファイルを全てコピー
     *
     * @param string $oldDirectory 複製元のディレクトリ
     * @param string $newDirectory 複製先のディレクトリ
     * @return void
     */
    function moveFilesToNewDirectory($oldDirectory, $newDirectory)
    {
        // 新しいディレクトリを作成
        $this->makeDirectory($newDirectory);

        // 元のディレクトリ内のファイルを取得
        $files = $this->storage->files($oldDirectory);

        // ファイルの新しいパスを生成してコピー
        foreach ($files as $file) {
            $newPath = str_replace($oldDirectory, $newDirectory, $file);
            $this->storage->copy($file, $newPath);
        }
    }

    /**
     * 複数画像削除
     *
     * @param array $filePaths
     */
    public function deleteImages(array $filePaths = [])
    {
        foreach ($filePaths as $path) {
            $this->storage->delete($path);
        }
    }

    /**
     * 特定のディレクト以下のファイルを全て削除
     *
     * @param string $dirPath
     */
    public function allDelete($dirPath)
    {
        $files = $this->storage->allFiles($dirPath);
        if ($files) {
            $this->storage->delete($files);
        }
    }
}
