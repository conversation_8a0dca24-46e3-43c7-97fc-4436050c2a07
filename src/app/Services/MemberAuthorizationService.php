<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Auth\AuthenticationException;

class MemberAuthorizationService
{
    /**
     * ログインユーザーのデータかチェックする関数
     *
     * @param int|string $memberId
     * @return void
     */
    public static function authorize($memberId)
    {
        if (!Auth::user() || $memberId != Auth::user()->id) {
            throw new AuthenticationException();
        }
    }
}
