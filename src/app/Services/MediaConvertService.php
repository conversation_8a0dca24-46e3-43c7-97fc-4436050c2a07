<?php

namespace App\Services;

use Aws\Exception\AwsException;
use Aws\MediaConvert\MediaConvertClient;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Exception;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;

//動画変換処理
class MediaConvertService
{
    // AWSの設定を取得
    private $awsConfig = [];

    //バケット名
    private $aws_disk_s3_bucket = "";

    //変換したい動画のパス
    private $inputPath;

    //変換後の動画のパス(拡張子は抜く)
    private $outputPath;

    public function __construct($inputPath, $outputPath)
    {
        $this->awsConfig = [
            "region" => config("services.media_convert.region"),
            "version" => "latest",
            "credentials" => [
                "key" => config("services.media_convert.key"),
                "secret" => config("services.media_convert.secret"),
            ],
        ];
        $this->aws_disk_s3_bucket = config("filesystems.disks.s3.bucket");
        $this->inputPath = $inputPath;
        $this->outputPath = $outputPath;
    }

    /**
     * 動画をストリーミングに変換
     *  @return bool
     */
    public function createJob(): bool
    {
        $mediaConvertClient = new MediaConvertClient($this->awsConfig);

        try {
            // MediaConvert のエンドポイントを取得
            $response = $mediaConvertClient->describeEndpoints();
            $endpointUrl = $response["Endpoints"][0]["Url"];

            // エンドポイントを使用して新しい MediaConvert クライアントを作成
            $mediaConvertClient = new MediaConvertClient(
                array_merge($this->awsConfig, ["endpoint" => $endpointUrl])
            );

            // MediaConvert ジョブの作成
            $result = $mediaConvertClient->createJob([
                "Role" => config(
                    "services.media_convert.aws_mediaconvert_decorator_role"
                ),
                "Settings" => $this->getStreamingSetting(),
            ]);

            return true;
        } catch (AwsException $e) {
            $this->sendErrorEmail($e);
            return false;
        }
    }

    /**
     * 動画変換の設定
     *
     * @return array
     */
    private function getStreamingSetting(): array
    {
        return [
            "Inputs" => [
                [
                    "FileInput" =>
                        "s3://" .
                        $this->aws_disk_s3_bucket .
                        "/{$this->inputPath}",
                    "AudioSelectors" => [
                        "Audio Selector 1" => [
                            "DefaultSelection" => "DEFAULT",
                        ],
                    ],
                    "VideoSelector" => [
                        "Rotate" => "AUTO", // ここで回転を自動に設定します
                    ],
                ],
            ],
            "OutputGroups" => [
                [
                    "Name" => "Apple HLS",
                    "OutputGroupSettings" => [
                        "Type" => "HLS_GROUP_SETTINGS",
                        "HlsGroupSettings" => [
                            "Destination" =>
                                "s3://" .
                                $this->aws_disk_s3_bucket .
                                "/{$this->outputPath}",
                            "SegmentLength" => 10,
                            "MinSegmentLength" => 0,
                        ],
                    ],
                    "Outputs" => [
                        [
                            "VideoDescription" => [
                                    "Rotate" => "DEGREE_AUTO",
                                "CodecSettings" => [
                                    "Codec" => "H_264",
                                    "H264Settings" => [
                                        "RateControlMode" => "QVBR",
                                        "MaxBitrate" => 5000000, // MaxBitrate を設定
                                        "SceneChangeDetect" =>
                                            "TRANSITION_DETECTION",
                                        "GopSize" => 90,
                                        "GopClosedCadence" => 1,
                                        "Slices" => 1,
                                        "NumberBFramesBetweenReferenceFrames" => 2,
                                        "Syntax" => "DEFAULT",
                                        "Softness" => 0,
                                        "AdaptiveQuantization" => "HIGH",
                                        "EntropyEncoding" => "CABAC",
                                        "UnregisteredSeiTimecode" => "DISABLED",
                                        "FlickerAdaptiveQuantization" =>
                                            "DISABLED",
                                        "SpatialAdaptiveQuantization" =>
                                            "ENABLED",
                                        "TemporalAdaptiveQuantization" =>
                                            "ENABLED",
                                    ],
                                ],
                            ],
                            "AudioDescriptions" => [
                                [
                                    "AudioSourceName" => "Audio Selector 1",
                                    "CodecSettings" => [
                                        "Codec" => "AAC",
                                        "AacSettings" => [
                                            "Bitrate" => 96000,
                                            "CodingMode" => "CODING_MODE_2_0",
                                            "SampleRate" => 48000,
                                        ],
                                    ],
                                ],
                            ],
                            "ContainerSettings" => [
                                "Container" => "M3U8",
                                "M3u8Settings" => [],
                            ],
                            "NameModifier" => "_hls",
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * エラーがあれば開発者にメール送信
     *
     * @param Exception $e
     * @return void
     */
    private function sendErrorEmail(Exception $e)
    {
        $errorMessage = $e->getMessage();
        $errorTrace = $e->getTraceAsString();
        $appName = Config::get("app.name");

        Mail::raw(
            "MediaConvert でエラーが発生しました。\n\nエラーメッセージ: {$errorMessage}\n\nスタックトレース:\n{$errorTrace}",
            function ($message) use ($appName) {
                $message
                    ->to(Config::get("mail.to_developer_address"))
                    ->subject("{$appName} MediaConvert エラー通知");
            }
        );
    }
}
