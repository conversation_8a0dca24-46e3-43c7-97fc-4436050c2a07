<?php declare(strict_types=1);

namespace App\Services;

use App\Models\Product;
use App\Services\FileS3CrossBucketService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\GraphQL\Exceptions\InValidException;
use Carbon\Carbon;
use Exception;
use App\Models\MWebInvitation;

final class ProductCrossDatabaseService
{
    /**
     * 商品データを別データベースにコピーし、画像を別バケットにコピーする（名前変更なし）
     *
     * @param int|string $id 商品ID
     * @param string $targetDatabase 対象データベース接続名
     * @param string $publicTargetBucket 対象S3バケット名
     * @param string $privateTargetBucket 対象S3バケット名
     * @return array 処理結果
     */
    public function copyProductWithoutRename($id, $targetDatabase, $publicTargetBucket, $privateTargetBucket = null)
    {
        try {
            return DB::transaction(function () use ($id, $targetDatabase, $publicTargetBucket, $privateTargetBucket) {
                // 商品データを取得
                $product = Product::withoutGlobalScopes()
                    ->with([
                        "m_specification_products",
                        "m_specification_products.product_images",
                        "m_specification_products.m_web_invitations",
                        "m_specification_products.m_web_invitations.web_invitation_design_images",
                    ])
                    ->find($id);

                if (!$product) {
                    throw new InValidException("商品情報が見つかりません。", [
                        "error" => ["商品情報が見つかりません。"],
                    ]);
                }

                $now = Carbon::now();

                // S3クロスバケットサービスのインスタンス化
                $s3Service = new FileS3CrossBucketService(
                    $publicTargetBucket,
                    $privateTargetBucket
                );

                // データベースへのコピー処理
                $targetDB = DB::connection($targetDatabase);

                // 商品データをコピー（名前変更なし、IDも同じ）
                $productData = $product->toArray();
                $productId = $productData['id']; // 元のIDを保存
                $productData['is_unpublished'] = true;
                $productData['created_at'] = $now;
                $productData['updated_at'] = $now;

                // withで取得したリレーション情報を削除
                unset(
                    $productData['m_specification_products'],
                    $productData['m_specification_products.product_images'],
                    $productData['m_specification_products.m_web_invitations'],
                    $productData['m_specification_products.web_invitation_design_images']
                );

                // 商品テーブル挿入
                $targetDB->table('products')->insert($productData);

                // Web招待状の画像をコピー
                $oldWiPath = env('AWS_DIR_WI') . $product->id;
                $newWiPath = env('AWS_DIR_WI') . $productId;

                // フォルダ内のファイル一覧を取得
                $files = Storage::disk('s3_public')->allFiles($oldWiPath);

                foreach ($files as $file) {
                    $s3Service->publicCopyFile($file);
                }

                foreach ($product->m_specification_products ?? [] as $m_specification_product) {
                    // 規格別商品データをコピー
                    $mSpecificationProductData = $m_specification_product->toArray();
                    $mSpecificationProductData['created_at'] = $now;
                    $mSpecificationProductData['updated_at'] = $now;

                    unset(
                        $mSpecificationProductData['product_images'],
                        $mSpecificationProductData['m_web_invitations'],
                        $mSpecificationProductData['web_invitation_design_images']
                    );

                    // 規格別商品挿入
                    $targetDB->table('m_specification_products')->insert($mSpecificationProductData);

                    // 商品画像をコピー
                    $productImagesData = [];
                    foreach ($m_specification_product->product_images ?? [] as $product_image) {

                        // 画像データをコピー
                        $productImageData = $product_image->toArray();

                        // IDを維持
                        $productImageData['created_at'] = $now;
                        $productImageData['updated_at'] = $now;
                        $productImagesData[] = $productImageData;

                        // imagesテーブルのデータを取得
                        $imageData = DB::table('images')
                            ->where('uuid', $product_image->uuid)
                            ->first();

                        if ($imageData) {
                            $imageData = (array)$imageData;
                            $imageData['created_at'] = $now;
                            $imageData['updated_at'] = $now;


                            // 転送先のimagesテーブルにデータをインサート
                            $targetDB->table('images')->insert($imageData);

                            // 画像ファイルをコピー
                            foreach (['s', 'm', 'l'] as $size) {
                                $sourcePath = env('AWS_DIR_ADMIN') . $imageData['name'] . "_" . $size . "." . $imageData['extension_type'];
                                $s3Service->privateCopyFile($sourcePath);
                            }
                        }
                    }

                    // 商品画像データ挿入
                    if (!empty($productImagesData)) {
                        $targetDB->table('product_images')->insert($productImagesData);
                    }

                    // Web招待状マスタをコピー
                    if ($m_specification_product->m_web_invitations) {
                        $mWebInvitationData = $m_specification_product->m_web_invitations->toArray();
                        $mWebInvitationData['created_at'] = $now;
                        $mWebInvitationData['updated_at'] = $now;

                        unset(
                            $mWebInvitationData['m_specification_product'],
                            $mWebInvitationData['web_invitation_design_images']
                        );

                        // Web招待状マスタデータ挿入
                        $invitation = new MWebInvitation($mWebInvitationData);
                        $invitation->setConnection($targetDatabase);
                        $invitation->save();

                        // Web招待状デザイン画像をコピー
                        $webInvitationDesignImagesData = [];
                        foreach ($m_specification_product->m_web_invitations->web_invitation_design_images ?? [] as $web_invitation_design_image) {
                            $webInvitationDesignImageData = $web_invitation_design_image->toArray();
                            $webInvitationDesignImageData['created_at'] = $now;
                            $webInvitationDesignImageData['updated_at'] = $now;
                            $webInvitationDesignImagesData[] = $webInvitationDesignImageData;
                        }

                        // Web招待状デザイン画像データ挿入
                        if (!empty($webInvitationDesignImagesData)) {
                            $targetDB->table('web_invitation_design_images')->insert($webInvitationDesignImagesData);
                        }
                    }
                }

                return '転送成功しました。';
            });
        } catch (\Exception $e) {
            Log::error('商品データのクロスデータベースコピー中にエラーが発生しました: ' . $e->getMessage());
            Log::error($e->getTraceAsString());
            return $e->getMessage();
        }
    }
}
