<?php

namespace App\Providers;

use Illuminate\Support\Facades\File;
use Illuminate\Support\ServiceProvider;
use Nuwave\Lighthouse\Exceptions\DefinitionException;
use Nuwave\Lighthouse\Schema\TypeRegistry;
use Nuwave\Lighthouse\Schema\Types\LaravelEnumType;

class GraphQLServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * @param TypeRegistry $typeRegistry
     * @throws DefinitionException
     */
    public function boot(TypeRegistry $typeRegistry): void
    {
        foreach ($this->getEnumClasses() as $enumClass) {
            $typeRegistry->register(new LaravelEnumType($enumClass));
        }
    }

    /**
     * @return array
     */
    private function getEnumClasses(): array
    {
        $classes = [];

        foreach (File::files(app_path("Enums")) as $file) {
            if ($file->getExtension() === "php") {
                $classes[] = "App\\Enums\\" . $file->getBasename(".php");
            }
        }

        return $classes;
    }
}
