<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Support\Facades\DB;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // // フロントエンド側で用意したリセットURLの生成ロジックを記述
        // ResetPassword::createUrlUsing(function ($user, string $token) {
        //     //URLに渡すトークンを保存
        //     DB::table("password_resets")
        //         ->where("email", $user->email)
        //         ->update(["email_token" => $token]);
        //     // フロントエンド側で用意したリセットURLの生成ロジックを記述
        //     $frontendResetUrl = env("MAIL_FRONT_URL_RESET");
        //     return $frontendResetUrl . "/" . $token;
        // });
    }
}
