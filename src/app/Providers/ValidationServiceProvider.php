<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Validator;

class ValidationServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        Validator::replacer("max", function (
            $message,
            $attribute,
            $rule,
            $parameters
        ) {
            if ($rule === "max") {
                $maxValue = $parameters[0];
                // KB を MB に変換
                $maxValueMB = $maxValue / 1024;
                $message = str_replace(":max", (string) $maxValueMB, $message);
            }
            return $message;
        });
    }
}
