<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WebInvitation;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\AbsentGuestReminderMail;

class SendAbsentGuestReminderMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:send_absent_guest_reminder_mail";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "挙式1週間前のリマインドメール（欠席ゲストへ）　リリース時は一旦なし";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // $today = Carbon::today();
        // $oneWeekLater = $today->addWeek();

        // // 今日の日付が期限日より前で、差が3日であるかをチェック
        // $webInvitations = WebInvitation::with([
        //     "member",
        //     "guests",
        //     "guests.guest_event_answers"
        // ])
        //     ->get()
        //     ->filter(function ($event) use ($oneWeekLater) {
        //         $eventDate = $event->event_date;
        //         if (empty($eventDate)) {
        //             return false;
        //         }
        //         $eventDate = Carbon::parse($eventDate);
        //         return $eventDate->isSameDay($oneWeekLater);
        //     });

        // foreach ($webInvitations as $webInvitation) {
        //     foreach($webInvitation->guests ?? [] as $guest) {
        //         foreach($guest->guest_event_answers ?? [] as $guest_event_answer) {
        //             if($guest_event_answer->attendance == "欠席") {
        //                 Mail::to($webInvitation->member->email)->send(
        //                     new AbsentGuestReminderMail(
        //                         $webInvitation->member->first_name,
        //                         $webInvitation->member->last_name,
        //                         Carbon::parse($webInvitation->limit_date)->format("Y年m月d日")
        //                     )
        //                 );
        //                 break;
        //             }
        //         }
        //     }
        // }

        return Command::SUCCESS;
    }
}
