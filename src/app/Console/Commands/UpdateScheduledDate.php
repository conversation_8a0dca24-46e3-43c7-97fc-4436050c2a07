<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WebInvitation;
use Illuminate\Support\Facades\DB;
use App\Enums\MoneyTransferStatusEnum;

class UpdateScheduledDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update:transfer-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '手動バッチ : Web招待状と送金テーブルの送金日を更新';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            DB::transaction(function () {
                WebInvitation::withoutEvents(function () {
                    $invitations = WebInvitation::with('money_transfer')->whereNull('transfer_date')->get();

                    foreach ($invitations as $inv) {


                        // 送金日を更新
                        $inv->update([
                            'transfer_date' => WebInvitation::calculateTransferDate(
                                $inv->scheduled_transfer_date
                            ),
                        ]);

                        // 送金テーブルが存在しない場合はスキップ
                        if (!$inv->money_transfer) {
                            continue;
                        }

                        // 送金テーブルが存在するかつ送金完了でない場合は送金日を更新
                        if ($inv->money_transfer->status != MoneyTransferStatusEnum::COMPLETED) {
                            $inv->money_transfer->update([
                                'transfer_date' => $inv->transfer_date,
                            ]);
                        } else {
                            // 送金完了の場合は更新日時を更新
                            $inv->money_transfer->touch();
                        }
                    }
                });
            });

            $this->info('送金日更新バッチが完了しました。');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error("バッチ処理でエラーが発生しました: {$e->getMessage()}");
            $this->error($e->getTraceAsString());
            return Command::FAILURE;
        }
    }
}
