<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Models\MoneyTransfer;
use App\Models\WebInvitation;
use App\Enums\MoneyTransferStatusEnum;
use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\DB;
use App\Consts\SystemConst;
use Illuminate\Support\Facades\Log;
use App\Services\UuidGeneratorService;
use App\Enums\PaymentMethodEnum;
use Carbon\Carbon;

class ProcessPrepaidPayment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:process_prepaid_payment";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "事前支払い締め処理 毎日00:00バッチ";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            DB::beginTransaction();

            // 事前支払い締め日を取得(送金予定日から◯日前)
            $targetDate = CarbonImmutable::now()->addDays(
                SystemConst::ADVANCE_PAYMENT_CLOSING_DAY
            );

            // 事前支払い締め日と送金予定日が合致するデータをID配列で取得
            $webInvitations = WebInvitation::with([
                "member",
                "member.member_bank_account",
                "guests" => function ($query) {
                    $query
                        ->withTrashed()
                        ->where(
                            "payment_method",
                            PaymentMethodEnum::ADVANCE_PAYMENT
                        )
                        ->withSum(
                            [
                                "guest_event_answers" => function ($query) {
                                    $query->withTrashed();
                                },
                            ],
                            "payment_amount"
                        );
                },
            ])
                ->whereDate("scheduled_transfer_date", $targetDate)
                ->get();

            $moneyTransfers = [];
            foreach ($webInvitations as $webInvitation) {
                $gift_amount = 0;
                $event_total_amount = 0;
                $system_guest_total_fee = 0;
                $total_advance_payment = 0;
                foreach ($webInvitation->guests ?? [] as $guest) {
                    $guestGiftAmount = $guest->gift_amount ?? 0;
                    $guestPaymentAmount = $guest->guest_event_answers_sum_payment_amount ?? 0;

                    $gift_amount += $guestGiftAmount;
                    $event_total_amount += $guestPaymentAmount;
                    $system_guest_total_fee += $guest->system_fee ?? 0;

                    // 事前支払い額 * システム利用料率合計金額
                    $total_advance_payment += floor(($guestGiftAmount + $guestPaymentAmount) * SystemConst::SYSTEM_FEE_RATE);
                }

                //web招待状決済金額
                $total_amount = $gift_amount + $event_total_amount;

                if ($total_amount == 0) {
                    continue;
                }

                // システム使用料 = 切り捨て(事前支払い額 * システム利用料率 - システム利用料)
                $system_fee = floor(
                    $total_advance_payment -
                        $system_guest_total_fee
                );
                // 送金予定金額 = 事前支払い額 - システム使用料 - 手数料
                $transfer_amount = max(
                    $total_amount - $system_fee - SystemConst::TRANSFER_FEE,
                    0
                );

                $moneyTransfers[] = [
                    "id" => UuidGeneratorService::generateOrderedUuid(),
                    "member_id" => $webInvitation->member_id,
                    "web_invitation_id" => $webInvitation->id,
                    "admin_id" => null,
                    "status" => MoneyTransferStatusEnum::NOT_TRANSFERRED,
                    "deadline_date" => $targetDate,
                    "prepayment_amount" => $total_amount,
                    "system_fee" => $system_fee,
                    "guest_system_fee" => $system_guest_total_fee,
                    "commission_fee" =>
                        $transfer_amount < 0 ? 0 : SystemConst::TRANSFER_FEE,
                    "transfer_amount" => $transfer_amount,
                    "transfer_date" => $webInvitation->transfer_date,
                    "completion_datetime" => null,
                    "bank_code" =>
                        $webInvitation->member->member_bank_account
                            ->bank_code ?? null,
                    "bank_name" =>
                        $webInvitation->member->member_bank_account
                            ->bank_name ?? null,
                    "branch_code" =>
                        $webInvitation->member->member_bank_account
                            ->branch_code ?? null,
                    "branch_name" =>
                        $webInvitation->member->member_bank_account
                            ->branch_name ?? null,
                    "account_type" =>
                        $webInvitation->member->member_bank_account
                            ->account_type ?? null,
                    "account_name" =>
                        $webInvitation->member->member_bank_account
                            ->account_name ?? null,
                    "account_number" =>
                        $webInvitation->member->member_bank_account
                            ->account_number ?? null,
                    "created_at" => now(),
                    "updated_at" => now(),
                ];
            }

            MoneyTransfer::insert($moneyTransfers);

            $this->info("事前支払い処理バッチ 成功");
            DB::commit();
            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->info("事前支払い処理バッチ 失敗");
            Log::error(
                "トランザクション中にエラーが発生しました: " . $e->getMessage()
            );
            return Command::FAILURE;
        }
    }
}
