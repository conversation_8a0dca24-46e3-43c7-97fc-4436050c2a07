<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WebInvitation;
use App\Models\MoneyTransfer;
use App\Enums\MoneyTransferStatusEnum;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class UpdateWebInvitationAndMoneyTransferDates extends Command
{
    protected $signature = 'batch:update-dates';
    protected $description = 'web招待状の送金日更新と送金テーブルと送金日を更新';

    public function handle()
    {
        try {
            DB::beginTransaction();

            $now = Carbon::now();

            // WebInvitationの更新日時を現在時刻に更新（modelイベントが発火して送金日が更新される）
            WebInvitation::whereNotNull('scheduled_transfer_date')->get()->each(function ($invitation) use ($now) {
                $invitation->updated_at = $now;
                $invitation->save();
            });

            // MoneyTransferのステータスが完了以外のデータを処理
            $moneyTransfers = MoneyTransfer::where('status', '!=', MoneyTransferStatusEnum::COMPLETED)
                ->with('web_invitation')
                ->get();

            foreach ($moneyTransfers as $moneyTransfer) {
                if (!$moneyTransfer->web_invitation && !$moneyTransfer->web_invitation->transfer_date) continue;
                $moneyTransfer->transfer_date = $moneyTransfer->web_invitation->transfer_date;
                $moneyTransfer->updated_at = $now;
                $moneyTransfer->save();
            }

            DB::commit();
            $this->info('web招待状の送金日更新と送金テーブルと送金日を更新成功');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('web招待状の送金日更新と送金テーブルと送金日を更新失敗: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
