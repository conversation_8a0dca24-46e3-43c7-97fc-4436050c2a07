<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class BatchMailSequenceCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:batch_mail_sequence";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "メール処理を呼び出し";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Artisan::call("batch:send_bank_account_regist_mail");//口座登依頼メール
        Artisan::call("batch:send_today_party_mail");//パーティー当日のお祝い (会員宛)
        Artisan::call("batch:send_previousday_party_mail");//挙式前日　パーティー開催のご案内 (ゲスト宛)
        Artisan::call("batch:send_pending_guest_reminder_mail");//回答締切日のリマインドメール（保留中のゲスト）

        return Command::SUCCESS;
    }
}
