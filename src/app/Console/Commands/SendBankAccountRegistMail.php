<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Mail\BankAccountRegistMail;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use App\Models\Member;
use App\Enums\PaymentMethodEnum;
use Illuminate\Support\Facades\DB;
use App\Consts\SystemConst;

class SendBankAccountRegistMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:send_bank_account_regist_mail";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "事前支払いの送金日間近となったときに対象の会員の銀行口座情報が未設定であればメール送信";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $members = Member::whereNull("bank_account_verification_sent_at")
                ->whereDoesntHave("member_bank_account")
                ->whereHas("web_invitations.guests", function ($query) {
                    $query->where(
                        "payment_method",
                        PaymentMethodEnum::ADVANCE_PAYMENT
                    );
                })
                ->with([
                    "web_invitations" => function ($query) {
                        $query->orderBy("scheduled_transfer_date");
                    },
                ])
                ->get();

            foreach ($members as $member) {
                $webInvitation = $member->web_invitations->first();
                $formattedDate = Carbon::parse(
                    $webInvitation->scheduled_transfer_date
                )
                    ->subDays(SystemConst::ADVANCE_PAYMENT_CLOSING_DAY)
                    ->format("Y年m月d日");
                Mail::to($member->email)->send(
                    new BankAccountRegistMail(
                        $member->first_name,
                        $member->last_name,
                        $formattedDate
                    )
                );
                $member->bank_account_verification_sent_at = Carbon::now();
                $member->save();
                usleep(100000);
            }

            $this->info("銀行口座情報確認メール 成功");
            DB::commit();
            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->info("銀行口座情報確認メール 失敗");
            return Command::FAILURE;
        }
    }
}
