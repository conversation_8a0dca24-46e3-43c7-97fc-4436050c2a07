<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ProductCrossDatabaseService;

class ProductSync extends Command
{
    // 引数を定義（IDを必須とする）
    protected $signature = 'product:sync {id}';

    protected $description = '商品IDを指定して同期処理を行う';

    public function handle()
    {
        $id = $this->argument('id');

        if(empty($id)){
            $this->info("IDがないので処理を終了");
            return Command::FAILURE;
        }

        $this->info("ID: {$id} の商品を同期中です...");
        $productCrossDatabaseService = new ProductCrossDatabaseService();
        $ret = $productCrossDatabaseService->copyProductWithoutRename(
            $id,
            'copy_target_db',
            env('AWS_COPY_TARGET_BUCKET'),
            env('AWS_COPY_TARGET_BUCKET_WI')
        );

        if ($ret) {
            $this->info("ID: {$id} の商品の同期が完了しました。");
            $this->info($ret);
            return Command::SUCCESS;
        } else {
            $this->error("ID: {$id} の商品の同期に失敗しました。ログを確認してください。");
            $this->info($ret);
            return Command::FAILURE;
        }
    }
}
