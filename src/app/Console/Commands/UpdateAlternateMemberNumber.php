<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Member;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UpdateAlternateMemberNumber extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'batch:update_alternate_member_number {--dry-run : 実際の更新を行わずに確認のみ}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '既存会員のalternate_member_numberを更新するバッチ';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $isDryRun = $this->option('dry-run');

            if ($isDryRun) {
                $this->info('=== DRY RUN モード ===');
            }

            // alternate_member_numberがnullの会員を取得
            $members = Member::whereNull('alternate_member_number')
                ->where('is_regist', true)
                ->get();

            if ($members->isEmpty()) {
                $this->info('更新対象の会員が見つかりませんでした。');
                return Command::SUCCESS;
            }

            $this->info("更新対象会員数: {$members->count()}件");

            if (!$isDryRun) {
                DB::beginTransaction();
            }

            $successCount = 0;
            $errorCount = 0;

            foreach ($members as $member) {
                try {
                    $alternateNumber = Member::generateUniqueFaId();

                    if (!$isDryRun) {
                        $member->alternate_member_number = $alternateNumber;
                        $member->save();
                    }

                    $successCount++;
                } catch (\Exception $e) {
                    $errorCount++;
                    $this->error("会員ID {$member->id} の更新に失敗: " . $e->getMessage());
                    Log::error("UpdateAlternateMemberNumber: 会員ID {$member->id} の更新エラー", [
                        'error' => $e->getMessage(),
                        'member_id' => $member->id
                    ]);
                }
            }

            if (!$isDryRun) {
                DB::commit();
                $this->info("バッチ処理完了: 成功 {$successCount}件, エラー {$errorCount}件");
            } else {
                $this->info("DRY RUN完了: 対象 {$successCount}件, エラー {$errorCount}件");
            }

            return Command::SUCCESS;

        } catch (\Exception $e) {
            if (!$this->option('dry-run')) {
                DB::rollBack();
            }

            $this->error('バッチ処理中にエラーが発生しました: ' . $e->getMessage());
            Log::error('UpdateAlternateMemberNumber: バッチ処理エラー', [
                'error' => $e->getMessage()
            ]);

            return Command::FAILURE;
        }
    }
}
