<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Product;
use Carbon\Carbon;

class ShowProductsAfterDate extends Command
{
    protected $signature = 'products:show-after-date {date}';
    protected $description = '特定の作成日時以降のProductモデルのidとnameを表示する';

    public function handle()
    {
        $date = $this->argument('date');

        // 日付のフォーマットを確認
        try {
            $carbonDate = Carbon::parse($date);
        } catch (\Exception $e) {
            $this->error('無効な日付形式です。');
            return;
        }

        // Productモデルからデータを取得
        $products = Product::withoutGlobalScopes()->where('created_at', '>=', $carbonDate)->get(['id', 'name', 'created_at']);

        // 結果を表示
        if ($products->isEmpty()) {
            $this->info('指定された日時以降の製品はありません。');
        } else {
            $this->line("Name, Created_At, Command");
            foreach ($products as $product) {
                $this->line("{$product->name}, {$product->created_at}, php artisan product:sync {$product->id}");
            }
        }
    }
}