<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WebInvitation;
use App\Models\Member;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\PendingGuestReminderMail;
use App\Consts\SystemConst;

class SendPendingGuestReminderMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:send_pending_guest_reminder_mail";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "回答締切日のリマインドメール（保留中のゲスト）";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 今日の日付を取得
        $today = Carbon::today();

        // 7日後の日付を計算
        $sevenDaysLater = $today
            ->copy()
            ->addDays(SystemConst::MAIL_SEND_LIMIT_PENDING_GUEST_REMINDER_DATE);

        // 7日後回答締め切りかつ保留のゲスト持ちの会員を取得
        $members = Member::whereHas("web_invitations", function ($query) use (
            $sevenDaysLater
        ) {
            $query
                ->whereDate("reply_deadline_date", $sevenDaysLater)
                ->whereHas("guests.guest_event_answers", function ($query) {
                    $query->where("attendance", "保留");
                });
        })->get();

        foreach ($members as $member) {
            Mail::to($member->email)->send(
                new PendingGuestReminderMail(
                    $member->first_name,
                    $member->last_name,
                    $sevenDaysLater->format("Y年m月d日")
                )
            );
            usleep(100000);
        }
        return Command::SUCCESS;
    }
}
