<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Member;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\TodayPartyInvitationMemberMail;

class SendTodayPartyMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:send_today_party_mail";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "パーティー当日のお祝い (会員宛)";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $today = Carbon::now();

        $members = Member::whereHas('web_invitations', function ($query) use ($today) {
                $query->whereDate('scheduled_date', '=', $today->toDateString())
                    ->where('is_public', true);
            })
            ->get();

        foreach ($members as $member) {
            Mail::to($member->email)->send(
                new TodayPartyInvitationMemberMail(
                    $member->first_name,
                    $member->last_name
                )
            );
            usleep(100000);
        }

        return Command::SUCCESS;
    }
}
