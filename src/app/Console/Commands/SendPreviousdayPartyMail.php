<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Guest;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use App\Mail\PreviousdayPartyGuestMail;

class SendPreviousdayPartyMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "batch:send_previousday_party_mail";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "挙式前日 パーティー開催のご案内 (出席ゲスト宛)";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $tomorrow = Carbon::tomorrow();

        $guests = Guest::with([
            "member",
            "web_invitation",
            "guest_list.event_lists",
            "guest_event_answers",
        ])
            ->whereHas("web_invitation", function ($query) use ($tomorrow) {
                $query->where("scheduled_date", $tomorrow->toDateString());
            })
            ->whereHas("guest_list.event_lists", function ($query) use (
                $tomorrow
            ) {
                $query->where("event_date", $tomorrow->toDateString());
            })
            ->whereHas("guest_event_answers", function ($query) {
                $query->where("attendance", "出席");
            })
            ->get();

        foreach ($guests as $guest) {
            if (!$guest->is_previousday_party_mail && !empty($guest->email)) {
                Mail::to($guest->email)->send(
                    new PreviousdayPartyGuestMail(
                        $guest->first_name,
                        $guest->last_name,
                        $tomorrow->isoFormat("YYYY年MM月DD日(ddd)"),
                        $guest->web_invitation->full_url
                    )
                );
                usleep(100000);
            }
        }

        return Command::SUCCESS;
    }
}
