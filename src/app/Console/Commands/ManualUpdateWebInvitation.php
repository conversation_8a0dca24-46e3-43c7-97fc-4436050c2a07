<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\WebInvitation;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;
use Carbon\Carbon;

//手動でweb招待状のエディタ設定JSONを更新
class ManualUpdateWebInvitation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = "command:manual-update-web-invitation";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "手動でweb招待状のエディタ設定JSONを更新するプログラム";

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            DB::beginTransaction();

            $now = Carbon::now();
            $invitations = WebInvitation::all();

            foreach ($invitations as $invitation) {
                $editorSettings = new Collection(
                    $invitation->editor_settings["blocks"] ?? []
                );

                $guestAnswerBlock = $editorSettings->firstWhere(
                    "id",
                    "guestAnswer"
                );

                if (isset($guestAnswerBlock["contents"]["selectList"])) {
                    $status = false;
                    $guestAnswerBlock["contents"]["selectList"] = collect(
                        $guestAnswerBlock["contents"]["selectList"]
                    )
                        ->map(function ($selectItem) use (&$status) {
                            if (
                                $selectItem["title"] !== "性別" &&
                                $selectItem["disabled"] === true
                            ) {
                                $selectItem["disabled"] = false;
                                $status = true;
                            }
                            return $selectItem;
                        })
                        ->toArray();

                    if (!$status) {
                        continue;
                    }

                    $updateEditorSettings["blocks"] = $editorSettings
                        ->map(function ($block) use ($guestAnswerBlock) {
                            return $block["id"] == "guestAnswer"
                                ? $guestAnswerBlock
                                : $block;
                        })
                        ->toArray();

                    $invitation->editor_settings = $updateEditorSettings;
                    $invitation->updated_at = $now;
                    $invitation->save();
                } else {
                    $this->info(
                        "web_invitations ID {$invitation->id}: selectList not found."
                    );
                }
            }

            $this->info("手動でweb招待状のエディタ設定JSONを更新処理完了");
            DB::commit();
            return Command::SUCCESS;
        } catch (\Exception $e) {
            DB::rollBack();
            $this->info($e->getMessage());
            return Command::FAILURE;
        }
    }
}
