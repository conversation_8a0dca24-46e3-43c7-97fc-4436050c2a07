<?php

namespace App\GraphQL\Exceptions;

use Exception;
use GraphQL\Error\ClientAware;
use GraphQL\Error\ProvidesExtensions;

class InValidException extends Exception implements
    ClientAware,
    ProvidesExtensions
{
    public const KEY = "validation";
    public function __construct(string $message, protected array $messages)
    {
        parent::__construct($message);
    }

    public function isClientSafe(): bool
    {
        return true;
    }

    public function getExtensions(): array
    {
        return [
            self::KEY => $this->messages,
        ];
    }
}
