<?php

namespace App\GraphQL\ErrorHandlers;

use GraphQL\Error\Error;
use Nuwave\Lighthouse\Execution\ErrorHandler;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Request;
use App\Mail\ExceptionReport;
use Illuminate\Support\Facades\Auth;

class LogErrorHandler implements ErrorHandler
{
    public function __invoke(?Error $error, \Closure $next): ?array
    {
        if ($error === null) {
            return $next(null);
        }

        $underlyingException = $error->getPrevious();
        $file = $underlyingException?->getFile();
        $line = $underlyingException?->getLine();

        $body = $error->getSource()->body ?? '';

        // クライアントのIPアドレス取得（TrustProxiesが正しく設定されている前提）
        $ipAddress = Request::getClientIp();

        // GraphQLのリクエスト内容（パラメータ含む）
        $requestContent = Request::getContent();

        // 会員情報取得
        $member = Auth::user();
        $memberInfo = $member ? "会員ID: {$member->id}\n会員番号: {$member->alternate_member_number}\n氏名: {$member->last_name} {$member->first_name}" : '未ログイン';

        // ログ記録
        Log::channel('log_error_daily')->error("[GraphQL Error] IP: {$ipAddress}\n{$memberInfo}\nParams: {$requestContent}\n");
        Log::channel('log_error_daily')->error("[Exception Message] IP: {$ipAddress}\n" . $underlyingException?->getMessage());

        // メール送信
        try {
            $plain = "【IPアドレス】\n{$ipAddress}\n\n"
            . "【ユーザー】\n{$memberInfo}\n\n"
            . "【例外メッセージ】\n" . $underlyingException?->getMessage() . "\n\n"
            . "【ファイル】\n{$file}\n\n"
            . "【行番号】\n{$line}\n\n"
            . "【GraphQLリクエスト本体】\n{$body}\n\n";


            Mail::to(config('mail.to_developer_address'))
                ->queue(new ExceptionReport($plain, config('app.name')));

        } catch (\Throwable $e) {
            Log::channel('log_error_daily')
                ->error("[例外通知メール送信失敗] IP: {$ipAddress}\n" . $e->getMessage());
        }

        return $next($error);
    }
}
