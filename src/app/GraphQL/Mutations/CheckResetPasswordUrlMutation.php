<?php

namespace App\GraphQL\Mutations;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;

final class CheckResetPasswordUrlMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["token"]);

        $passwordReset = DB::table("password_resets")
            ->where("email_token", $credentials["token"])
            ->first();

        if (!$passwordReset) {
            throw new InValidException("トークンが無効です", [
                "error" => ["トークンが無効です"],
            ]);
        }

        $createdAt = Carbon::parse($passwordReset->created_at);
        $currentDateTime = Carbon::now();

        $elapsedTimeInDays = $createdAt->diffInDays($currentDateTime);

        // 一日以上経過している場合の処理
        if ($elapsedTimeInDays >= 1) {
            DB::table("password_resets")
                ->where("email_token", $credentials["token"])
                ->delete();
            throw new InValidException("有効期限が切れています", [
                "error" => ["有効期限が切れています"],
            ]);
        }

        return true;
    }
}
