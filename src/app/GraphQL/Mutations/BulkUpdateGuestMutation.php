<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\Guest;
use App\Models\GuestEventAnswer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Illuminate\Support\Facades\Auth;
use App\Services\UuidGeneratorService;

//ゲスト複数更新
final class BulkUpdateGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $guests = Arr::only($args, ["input"]);

        return DB::transaction(function () use ($guests) {
            $memberId = Auth::user()->id;
            foreach ($guests["input"] ?? [] as $guest) {
                $guestModel = Guest::with([
                    "member",
                    "guest_list",
                    "web_invitation",
                    "guest_event_answers",
                ])->findorfail($guest["id"] ?? 0);

                if ($memberId != $guestModel->member_id) {
                    throw new AuthenticationException();
                }

                //ゲストタグ
                if (isset($guest["guest_tag_guests"])) {
                    $guestTagIds = array_column(
                        $guest["guest_tag_guests"],
                        "guest_tag_id"
                    );
                    $guestModel->guest_tags()->sync($guestTagIds);
                }

                //ゲストイベント報告
                $eventList = $guestModel->guest_list->event_name_uuid_list;
                if (isset($guest["guest_event_answers"])) {
                    $guestEventAnswers = Arr::pull(
                        $guest,
                        "guest_event_answers"
                    );
                    foreach ($guestEventAnswers as &$item) {
                        // UUIDを生成する
                        $item["id"] =
                            $item["id"] ??
                            UuidGeneratorService::generateOrderedUuid();
                        $item["member_id"] = $memberId;
                        $item["guest_id"] = $guestModel->id;
                        if (isset($item["name"])) {
                            $item["event_list_id"] =
                                $eventList[$item["name"]] ?? null;
                        }
                        unset($item["date"]);
                        unset($item["name"]);

                        // 新規登録または更新
                        $guestEventAnswer = GuestEventAnswer::find($item["id"]);
                        if ($guestEventAnswer) {
                            $guestEventAnswer->update($item);
                        } else {
                            $guestEventAnswer = new GuestEventAnswer($item);
                            $guestEventAnswer->save();
                        }
                    }
                }

                unset($guest["guest_event_answers"]);
                unset($guest["guest_tag_guests"]);

                // 一括更新時に Normal に自動で変わっちゃうので修正
                // $guest["member_confirm_type"] = MemberConfirmTypeEnum::Normal;
                $guestModel->update($guest);
            }

            return true;
        });
    }
}
