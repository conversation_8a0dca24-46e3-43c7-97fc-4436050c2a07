<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Services\FileS3Service;
use App\Jobs\ResizeAndCompressImage;
use App\Consts\SystemConst;
use App\Enums\ImageTypeEnum;
use App\Services\MediaConvertService;
final class BinaryUploadImageMutation2
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["file_type", "file_data"]);

        return DB::transaction(function () use ($credentials) {
            $fileType = $credentials["file_type"]->value;
            $fileData = $credentials["file_data"];

            //認証・オーナー種類取得・パス作成とフォルダが存在しない場合に作成
            $fileS3Service = new FileS3Service();

            //ファイル種別 認証チェック
            $fileS3Service->checkUserFileType($fileType);

            //画像保存処理
            $fileS3Service->setFileData($fileData);
            $fileName = $fileS3Service->getFileName();

            //画像テーブルに保存
            $imageModel = $fileS3Service->imageSave($fileName, $fileType);

            if ($fileS3Service->getImageType() == ImageTypeEnum::IMAGE) {
                //画像のリサイズ
                $fileResize = $fileS3Service->resize(
                    $fileData,
                    SystemConst::LENGTH_M
                );

                //画像保存
                $path = Storage::disk("s3")->putFileAs(
                    $fileS3Service->getDir(),
                    $fileResize,
                    $fileS3Service->getFileNameSizeExtension("m")
                );

                //有効期限URL取得
                $presigned_url = $fileS3Service->getPresignedUrl($path);

                //圧縮とリサイズのキュー実行
                $tempPath = $fileS3Service->saveToTemporaryFile($fileData);

                ResizeAndCompressImage::dispatch(
                    SystemConst::IMAGE_SIZES,
                    $tempPath,
                    $fileS3Service->getDir(),
                    $fileName
                );
            } else {
                //一時画像取得
                $tempPath = $fileS3Service->saveToTemporaryFile($fileData);

                //サムネイル画像取得
                $pathImage = $fileS3Service->saveVideoChangeImage($tempPath);

                $path =
                    $fileS3Service->getDir() .
                    "/" .
                    $fileS3Service->getFileName() .
                    "." .
                    $fileS3Service->getExtension();

                //動画移動
                Storage::disk("s3")->move($tempPath, $path);

                $mediaConvert = new MediaConvertService(
                    $path,
                    $fileS3Service->getDir() .
                        "/" .
                        $fileS3Service->getFileName()
                );
                $mediaConvert->createJob();

                //有効期限URL取得
                $presigned_url_main = $fileS3Service->getPresignedUrl($path);
                $presigned_url = $fileS3Service->getPresignedUrl($pathImage);
            }

            return [
                "uuid" => $imageModel->uuid,
                "type" => $imageModel->type,
                "file_extension" => $fileS3Service->getExtension(),
                "presigned_url" => $presigned_url,
                "presigned_url_s" => $presigned_url,
                "presigned_url_m" => $presigned_url,
                "presigned_url_l" => $presigned_url,
                "presigned_url_main" => $presigned_url_main ?? null,
            ];
        });
    }
}
