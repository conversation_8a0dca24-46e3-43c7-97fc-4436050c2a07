<?php

namespace App\GraphQL\Mutations;

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use App\GraphQL\Exceptions\InValidException;
use App\Models\MemberEmailChange;
use App\Mail\ChangeEmailMailDone;

final class CheckChangeEmailUrlMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["token"]);

        $emailChange = MemberEmailChange::where("token", $credentials["token"])->first();

        if (!$emailChange) {
            throw new InValidException("トークンが無効です", [
                "error" => ["トークンが無効です"],
            ]);
        }


        // 一日以上経過している場合の処理
        // $createdAt = Carbon::parse($passwordReset->created_at);
        // $currentDateTime = Carbon::now();

        // $elapsedTimeInDays = $createdAt->diffInDays($currentDateTime);
        // if ($elapsedTimeInDays >= 1) {
        //     DB::table("password_resets")
        //         ->where("email_token", $credentials["token"])
        //         ->delete();
        //     throw new InValidException("有効期限が切れています", [
        //         "error" => ["有効期限が切れています"],
        //     ]);
        // }

        // 会員情報を変更
        $member = $emailChange->member;
        $member->email = $emailChange->new_email;
        $member->save();

        $emailChange->delete();

        Mail::to($member->email)->send(
            new ChangeEmailMailDone($member)
        );

        return true;
    }
}
