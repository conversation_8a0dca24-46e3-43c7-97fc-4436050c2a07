<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestTag;
use App\Models\GuestFreeItemValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Services\UuidGeneratorService;

//ゲストコピー
final class CopyGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["guest_list_id", "guest_ids"]);

        return DB::transaction(function () use ($credentials) {
            $guestList = GuestList::with(["guest_groups", "guest_tags"])->find(
                $credentials["guest_list_id"]
            );

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            //認証チェック
            if ($guestList->member_id != Auth::user()->id) {
                throw new AuthenticationException();
            }

            //ゲストグループのデータを整形
            $guestGroupsData = $guestList->guest_groups->reduce(function (
                $carry,
                $group
            ) {
                $carry[$group->name] = $group->id;
                return $carry;
            }, []);

            // ゲストタグのデータを整形
            $guestTagsData = $guestList->guest_tags->reduce(function (
                $carry,
                $tag
            ) {
                $carry[$tag->tag] = $tag->id;
                return $carry;
            }, []);

            //ゲスト情報取得
            $guests = Guest::with([
                "guest_group",
                "guest_tags",
                "guest_free_item_values",
            ])
                ->whereIn("id", $credentials["guest_ids"])
                // ->orderByRaw("ISNULL(parent_guest_id), parent_guest_id")
                ->get();

            $guestReplacementIds = $guests
                ->mapWithKeys(function ($guest) {
                    return [$guest->id => null];
                })
                ->toArray();

            //ゲスト(連名者の筆頭者IDはNULLにする)
            foreach ($guests as $guest) {
                $copyGuest = $guest->replicate();
                $copyGuest->guest_list_id = $guestList->id;

                // 複製処理の際にコピーしない
                $copyGuest->web_invite_reply_datetime = null; //Web招待状返信日時
                $copyGuest->web_invitation_id = null; //web招待状ID
                $copyGuest->m_web_invitation_id = null; //web招待状マスタID
                $copyGuest->payment_method = null; //事前支払い方法
                $copyGuest->gift_amount = 0; // お気持ち金額
                $copyGuest->is_system_fee = false; //システム利用料負担フラグ
                $copyGuest->system_fee = 0; //システム利用料負担金額
                $copyGuest->system_fee_rate = 0; //システム利用料率
                $copyGuest->total_amount = 0; // 会費・ご祝儀・お気持ち金額合計金額
                $copyGuest->settlement_amount = 0; // 決算金額
                $copyGuest->card_settlement_id = null; //クレジットカード

                //ゲストグループコピー
                if (!empty($guest->guest_group)) {
                    if (isset($guestGroupsData[$guest->guest_group->name])) {
                        $copyGuest->guest_group_id =
                            $guestGroupsData[$guest->guest_group->name];
                    } else {
                        $copyGuestGroup = $guest->guest_group->replicate();
                        $copyGuestGroup->guest_list_id = $guestList->id;
                        $copyGuestGroup->save();
                        $copyGuest->guest_group_id = $copyGuestGroup->id;
                        $guestGroupsData[$copyGuestGroup->name] =
                            $copyGuestGroup->id;
                    }
                }

                //連名関係付け替え
                if (
                    !empty($copyGuest->parent_guest_id) &&
                    isset($guestReplacementIds[$guest->parent_guest_id])
                ) {
                    $copyGuest->parent_guest_id =
                        $guestReplacementIds[$guest->parent_guest_id];
                } else {
                    $copyGuest->parent_guest_id = null;
                }
                $copyGuest->save();

                //旧ゲストID[新ゲストID]
                $guestReplacementIds[$guest->id] = $copyGuest->id;

                //フリー項目
                $guestFreeItemValues = [];
                foreach (
                    $guest->guest_free_item_values ?? []
                    as $guestFreeItemValue
                ) {
                    $copyGuestFreeItemValue = $guestFreeItemValue
                        ->replicate()
                        ->toArray();
                    $copyGuestFreeItemValue[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $copyGuestFreeItemValue["guest_id"] = $copyGuest->id;
                    $copyGuestFreeItemValue["created_at"] = Carbon::parse(
                        $guestFreeItemValue->created_at
                    )->format("Y-m-d H:i:s");
                    $copyGuestFreeItemValue["updated_at"] = Carbon::parse(
                        $guestFreeItemValue->updated_at
                    )->format("Y-m-d H:i:s");

                    $guestFreeItemValues[] = $copyGuestFreeItemValue;
                }

                GuestFreeItemValue::insert($guestFreeItemValues);

                //ゲストタグコピー
                $guestTagIds = [];
                foreach ($guest->guest_tags ?? [] as $guestTag) {
                    if (isset($guestTagsData[$guestTag->tag])) {
                        $guestTagIds[] = $guestTagsData[$guestTag->tag];
                    } else {
                        $copyGuestTag = $guestTag->replicate();
                        $copyGuestTag->guest_list_id = $guestList->id;
                        $copyGuestTag->save();
                        $guestTagIds[] = $copyGuestTag->id;
                        $guestTagsData[$copyGuestTag->tag] = $copyGuestTag->id;
                    }
                }
                $copyGuest->guest_tags()->sync($guestTagIds);
            }
            return $guestList->guests ?? [];
        });
    }
}
