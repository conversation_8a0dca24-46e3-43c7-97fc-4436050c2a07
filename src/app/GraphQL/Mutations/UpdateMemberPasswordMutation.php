<?php

namespace App\GraphQL\Mutations;

use App\Models\Member;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;
use Illuminate\Auth\AuthenticationException;

final class UpdateMemberPasswordMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args, GraphQLContext $context)
    {
        $credentials = Arr::only($args, ["password", "new_password"]);
        $member = Auth::user();

        // SNSログインで、 is_use_password =0の場合は、パスワードを新規登録なので、パスワードチェックしない
        if ($member->sns_login_id && ! $member->is_use_password) {
            $member->is_use_password = 1;
            $member->save();
        } else {
            // パスワードの確認
            if (!$member->validPassword($credentials["password"])) {
                throw new AuthenticationException("現在のパスワードが違います。", [
                    "member",
                ]);
            }
        }

        $member->update([
            "password" => $credentials["new_password"],
        ]);
        return true;
    }
}
