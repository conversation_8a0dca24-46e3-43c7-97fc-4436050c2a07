<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use App\Enums\OwnerType;
use App\Enums\FileType;
use App\Models\Image;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\GraphQL\Exceptions\InValidException;
use Illuminate\Support\Str;
use Aws\S3\S3Client;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

final class BinaryUploadImageMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["file_type", "file_data"]);

        return DB::transaction(function () use ($credentials) {
            $fileType = $credentials["file_type"]->value;
            $fileData = $credentials["file_data"];

            $user = Auth::user();
            if (!$user) {
                throw new AuthenticationException();
            }

            //オーナー種類取得
            $modelName = class_basename($user);
            $ownerType = OwnerType::getValue($modelName);

            //認証チェック
            if ($ownerType == OwnerType::OWNER_TYPE_MEMBER && $fileType >= 30) {
                throw new AuthenticationException();
            } elseif (
                $ownerType == OwnerType::OWNER_TYPE_ADMIN &&
                $fileType < 30
            ) {
                throw new AuthenticationException();
            }

            //S3の設定を取得
            $storage = Storage::disk("s3");
            $config = $storage->getConfig();

            //パス作成
            $dir = env("AWS_DIR_MEMBER") . "{$user->id}";
            if ($ownerType == OwnerType::OWNER_TYPE_ADMIN) {
                $dir = env("AWS_DIR_ADMIN");
            }

            // フォルダが存在しない場合に作成
            if (!$storage->exists($dir)) {
                $storage->makeDirectory($dir);
            }

            //ファイル名取得
            $fileName = $fileData->getClientOriginalName();

            //拡張子取得
            $extension = $fileData->getClientOriginalExtension();

            //画像保存
            $path = $storage->putFileAs(
                rtrim($dir, "/"),
                $fileData,
                $fileName . "." . $extension
            );

            //DBに保存
            $imageModel = new Image();
            $imageModel->owner_type = $ownerType;
            $imageModel->owner_id = $user->id;
            $imageModel->file_type = $fileType;
            $imageModel->name = $fileName;
            $imageModel->extension_type = $extension;
            $imageModel->save();

            //minio環境の場合
            if ($config["use_path_style_endpoint"]) {
                $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
                $storage = Storage::build(
                    Arr::set($config, "endpoint", $endpoint)
                );
            }

            //一時署名付きURL作成
            $presignedUrl = $storage->temporaryUrl(
                $path,
                now()->addSeconds($config["url_member_expiration"])
            );
            return [
                "uuid" => $imageModel->uuid,
                "presigned_url" => $presignedUrl,
            ];
        });
    }
}
