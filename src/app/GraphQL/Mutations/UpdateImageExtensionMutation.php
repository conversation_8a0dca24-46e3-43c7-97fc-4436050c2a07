<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Illuminate\Support\Facades\Config;
use App\Models\Image;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Exception;

// Imageテーブル拡張子更新
final class UpdateImageExtensionMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        DB::beginTransaction();

        try {
            $apiKey = request()->header("X-API-KEY");

            if ($apiKey !== Config::get("services.api_key")) {
                throw new AuthenticationException();
            }

            $credentials = Arr::only($args, ["name"]);

            $name_without_hls = str_replace("_hls", "", $credentials["name"]);
            $image = Image::where("name", $name_without_hls)->first();

            if (!$image) {
                throw new InValidException("ファイル情報が見つかりません。", [
                    "error" => ["ファイル情報が見つかりません。"],
                ]);
            }

            $image->update([
                "extension_type" => "m3u8",
            ]);

            DB::commit();
            return true;
        } catch (Exception $e) {
            DB::rollBack();
            $this->sendErrorEmail($e);
            throw $e;
        }
    }

    private function sendErrorEmail(Exception $e)
    {
        $errorMessage = $e->getMessage();
        $errorTrace = $e->getTraceAsString();
        $appName = Config::get("app.name");

        Mail::raw(
            "UpdateImageExtensionMutation でエラーが発生しました。\n\nエラーメッセージ: {$errorMessage}\n\nスタックトレース:\n{$errorTrace}",
            function ($message) use ($appName) {
                $message
                    ->to(Config::get("mail.to_developer_address"))
                    ->subject(
                        "{$appName} UpdateImageExtensionMutation エラー通知"
                    );
            }
        );
    }
}
