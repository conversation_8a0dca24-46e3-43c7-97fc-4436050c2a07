<?php

namespace App\GraphQL\Mutations;

use App\Models\Member;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Illuminate\Support\Facades\Auth;
use App\Mail\DeleteMemberMail;
use Illuminate\Support\Facades\Mail;

final class DeleteMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args, GraphQLContext $context)
    {
        Auth::user()
            ->tokens()
            ->delete();
        $member = Auth::user();
        $member->delete();
        Mail::to($member->email)->send(
            new DeleteMemberMail($member->first_name, $member->last_name)
        );
        return $member;
    }
}
