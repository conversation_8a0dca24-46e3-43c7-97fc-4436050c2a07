<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestFreeItemValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Carbon\Carbon;

final class CreateWebToolGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["input", "guests"]);

        return DB::transaction(function () use ($credentials) {
            $guest = $credentials["input"] ?? [];
            $guests = $credentials["guests"] ?? [];

            $guestList = GuestList::find($guest["guest_list_id"] ?? 0);

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            $member_id = $guestList->member_id;

            //代表ゲスト
            $guestModel = new Guest();
            $guestModel->fill($guest);
            $guestModel->member_id = $member_id;
            $guestModel->save();

            //連名者
            foreach ($guests as $key => $row) {
                $row["member_id"] = $guestModel->member_id;
                $row["parent_guest_id"] = $guestModel->id;
                $guests[$key] = $row;
            }
            Guest::upsert($guests, "id");

            return $guestModel;
        });
    }
}
