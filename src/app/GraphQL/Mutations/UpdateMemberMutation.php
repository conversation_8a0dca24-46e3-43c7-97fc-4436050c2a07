<?php

namespace App\GraphQL\Mutations;

use App\Models\WeddingInfo;
use App\Models\FamilyProfile;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;

final class UpdateMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args, GraphQLContext $context)
    {
        return DB::transaction(function () use ($args) {
            $member = $args;
            if (isset($member["wedding_info"])) {
                unset($member["wedding_info"]);
            }
            $wedding_info = $args["wedding_info"] ?? [];

            $memberModel = Auth::user();

            if (!$memberModel) {
                throw new InValidException("会員情報が見つかりません。", [
                    "error" => ["会員情報が見つかりません。"],
                ]);
            }

            //会員情報
            if (!empty($member)) {
                $memberModel->update($member);
            }

            //家族プロフィール(あなたのプロフィール)作成or更新
            $familyProfileData = [
                "first_name" => $memberModel->first_name,
                "last_name" => $memberModel->last_name,
                "first_name_romaji" => $memberModel->first_name_romaji,
                "last_name_romaji" => $memberModel->last_name_romaji,
                "birth_date" => $memberModel->birthdate,
            ];
            $familyProfile = FamilyProfile::where(
                "member_id",
                $memberModel->id
            )->first();
            if ($familyProfile) {
                $familyProfile->update($familyProfileData);
            } else {
                FamilyProfile::create(
                    array_merge(
                        ["member_id" => $memberModel->id],
                        $familyProfileData,
                        ["order" => 1]
                    )
                );
            }

            //結婚式情報
            if (!empty($wedding_info)) {
                $weddingInfoModel = WeddingInfo::find($wedding_info["id"] ?? 0);

                if (!$weddingInfoModel) {
                    throw new InValidException("結婚式情報が見つかりません。", [
                        "error" => ["結婚式情報が見つかりません。"],
                    ]);
                }
                $weddingInfoModel->update($wedding_info);
            }

            return $memberModel;
        });
    }
}
