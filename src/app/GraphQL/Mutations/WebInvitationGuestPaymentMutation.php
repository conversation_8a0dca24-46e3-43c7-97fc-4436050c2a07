<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use App\GraphQL\Exceptions\InValidException;
use App\Services\GmoPGService;
use Illuminate\Support\Str;
use App\Services\PaymentLogService;

// 事前支払いの場合、3DセキュアでリダイレクトURLを取得
final class WebInvitationGuestPaymentMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["input", "guests", "payment"]);

        $guestLastName = $credentials["input"]["last_name"] ?? "";
        $guestFirstName = $credentials["input"]["first_name"] ?? "";
        $guestName = $guestLastName . $guestFirstName;

        // 決済ログ
        $paymentLogService = new PaymentLogService();
        $paymentLogService->log3dInputParameters($guestName, $credentials);

        $guest = $credentials["input"] ?? [];
        $guests = $credentials["guests"] ?? [];
        $payment = $credentials["payment"] ?? [];

        try {
            $post = [];
            $post["token"] = $payment["card_token"];
            $post["amount"] = $guest["settlement_amount"];
            foreach ($guests as $item) {
                if (!isset($item["settlement_amount"])) {
                    continue;
                }
                if (!$item["settlement_amount"]) {
                    continue;
                }
                $post["amount"] += $item["settlement_amount"];
            }
            $post["tax"] = 0;
            $post["orderId"] = Str::random(8) . "-" . date("YhmdHis");
            $gmoPG = new GmoPGService();
            // EntryTran 取引登録
            $result1 = $gmoPG->entryTran($post);
            $paymentLogService->logENTRY(
                $guestName,
                $post,
                $post["orderId"],
                $credentials,
                $result1
            );
            if (isset($result1["ErrInfo"])) {
                $result1["message"] = $gmoPG->getErrorMessage(
                    $result1["ErrInfo"]
                );
                throw new InValidException($result1["message"], [
                    "error" => [$result1],
                ]);
            }

            // 成功の場合
            $post["AccessID"] = $result1["AccessID"];
            $post["AccessPass"] = $result1["AccessPass"];
            $post["RetUrl"] = $payment["callback_url"];
            $result2 = $gmoPG->execTran($post);
            $paymentLogService->logEXEC(
                $guestName,
                $post,
                $post["orderId"],
                $credentials,
                $result2
            );

            if (isset($result2["ErrInfo"])) {
                $result2["message"] = $gmoPG->getErrorMessage(
                    $result2["ErrInfo"]
                );
                throw new InValidException($result2["message"], [
                    "error" => [$result2],
                ]);
            }
            if (isset($result2["RedirectUrl"])) {
                // 3Dセキュアが有効な場合、リダイレクトURLを返す
                return [
                    "access_id" => $result1["AccessID"],
                    "access_pass" => $result1["AccessPass"],
                    "redirect_url" => $result2["RedirectUrl"],
                ];
            } else {
                // 3Dセキュアが無効なカードの場合、この時点で決済完了なので、OrderIDを返す
                return [
                    "access_id" => $result1["AccessID"],
                    "access_pass" => $result1["AccessPass"],
                    "order_id" => $post["orderId"],
                ];
            }
        } catch (\Exception $e) {
            $paymentLogService->log3DError(
                $guestName,
                $post ?? [],
                $post["orderId"] ?? null,
                $credentials,
                $result1 ?? [],
                $result2 ?? [],
                $e->getMessage()
            );

            // エラー処理
            throw new InValidException($e->getMessage(), [
                "error" => [$e->getMessage()],
            ]);
        }
    }
}
