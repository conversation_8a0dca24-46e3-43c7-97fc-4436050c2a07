<?php

namespace App\GraphQL\Mutations;

use App\Models\InstagramCountdownSetting;
use Illuminate\Support\Facades\DB;
use App\Services\MemberAuthorizationService;

class DeleteInstagramCountdownSettingMutation
{
    /**
     * Instagramカウントダウン画像設定を削除
     *
     * @param null $_
     * @param array $args
     * @return bool
     */
    public function __invoke($_, array $args)
    {
        $uuid = $args['uuid'];

        return DB::transaction(function () use ($uuid) {
            $setting = InstagramCountdownSetting::findOrFail($uuid);
            
            // 認証チェック
            MemberAuthorizationService::authorize($setting->member_id);

            return $setting->delete();
        });
    }
}
