<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\WebInvitation;
use App\Models\GuestList;
use App\Models\EventList;
use App\Models\GuestEventAnswer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\MemberAuthorizationService;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use App\GraphQL\Exceptions\InValidException;
use App\Services\UuidGeneratorService;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Calculation\Logical\Boolean;
use App\Values\TrimmedString;

//web招待状更新
final class UpdateWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["is_synced", "input"]);

        return DB::transaction(function () use ($credentials) {
            $member = Auth::user();
            if (!$member) {
                throw new AuthenticationException();
            }
            MemberAuthorizationService::authorize($member->id);

            $input = $credentials["input"] ?? [];
            $webInvitation = WebInvitation::find($input["id"] ?? "");
            if (!$webInvitation) {
                throw new InValidException("web招待状情報が見つかりません。", [
                    "error" => ["web招待状情報が見つかりません。"],
                ]);
            }

            //変更前のゲストリストID
            $beforeGuestListId = "";
            if ($webInvitation->guest_list_id != $input["guest_list_id"]) {
                $beforeGuestListId = $webInvitation->guest_list_id;
            }
            $webInvitation->fill($input);

            $scheduledDate = $webInvitation->event_date;

            //開催日付のチェック
            if (is_null($scheduledDate)) {
                throw new InValidException("開催日が設定されていません。", [
                    "error" => ["開催日が設定されていません。"],
                ]);
            }

            $webInvitation->scheduled_date = $scheduledDate;
            $webInvitation->reply_deadline_date = $webInvitation->limit_date;

            //自他Web招待状の出欠回答カウント数
            $otherInvitationsCount = $this->getOtherInvitationsCount(
                $webInvitation->guest_list_id
            );

            //他Web招待状の開催日 != 自招待状の開催日 のチェック
            $isCheckEventDate = $this->checkEventDate(
                $webInvitation->guest_list_id,
                $webInvitation->scheduled_date
            );

            //同期のチェックと「同期確認済みFLG」
            $is_synced = $credentials["is_synced"] ?? true;

            //開催日変更不可のチェック
            if ($isCheckEventDate && $otherInvitationsCount > 0) {
                throw new InValidException(
                    "移動先ゲストリストのWEB招待状と挙式日（開催日）が異なるため、ゲストリストを変更できません",
                    [
                        "error" => [
                            "移動先ゲストリストのWEB招待状と挙式日（開催日）が異なるため、ゲストリストを変更できません",
                        ],
                    ]
                );
            }

            //特定のゲストリストに紐づくweb招待状のカウント数
            $webInvitationCount =
                WebInvitation::where([
                    ["guest_list_id", $webInvitation->guest_list_id],
                    ["id", "!=", $webInvitation->id],
                ])->count() + 1;
            if (
                !$is_synced &&
                $webInvitationCount > 1 &&
                $isCheckEventDate &&
                $otherInvitationsCount == 0
            ) {
                throw new InValidException(
                    "他のWeb招待状の開催日が異なります",
                    [
                        "error" => ["他のWeb招待状の開催日が異なります"],
                    ]
                );
            }

            $webInvitation->update();

            //イベント(開催日)同期
            $this->eventUpdate(
                $webInvitation->guest_list_id,
                $otherInvitationsCount,
                $scheduledDate,
                $webInvitation->gift_schedule_date,
                $webInvitation->guest_answer_date,
                $webInvitation->guest_answer_set,
                $webInvitation->reply_deadline_date,
                $webInvitation->scheduled_transfer_date
            );

            //ゲストリスト変更した場合は変更前のイベントも変更
            if (!empty($beforeGuestListId)) {
                $this->eventUpdate($beforeGuestListId, $otherInvitationsCount);
            }

            return $webInvitation;
        });
    }

    /**
     * ゲストイベントの更新
     *
     * @param string $guestListId
     * @param int $otherInvitationsCount
     * @param $scheduledDate
     * @param string|null $giftScheduleDate 送金日設定
     * @param string|null $guestAnswerDate 回答の任意の日付
     * @param string|null $guestAnswerSet 回答日設定
     * @param string|null $replyDeadlineDate 回答日
     * @param string|null $scheduledTransferDate 送金日
     * @return void
     */
    private function eventUpdate(
        $guestListId,
        $otherInvitationsCount,
        $scheduledDate = null,
        $giftScheduleDate = null,
        $guestAnswerDate = null,
        $guestAnswerSet = null,
        $replyDeadlineDate = null,
        $scheduledTransferDate = null
    ) {
        //ゲストリスト取得
        $guestList = GuestList::with([
            "web_invitations",
            "event_lists",
            "guests.guest_event_answers",
        ])->find($guestListId);

        if (!$guestList) {
            throw new InValidException("ゲストリスト情報が見つかりません。", [
                "error" => ["ゲストリスト情報が見つかりません。"],
            ]);
        }

        //仮想イベントリスト
        $event_list_data = $guestList->event_list_data;

        //現在のイベント名の配列
        $currentEventList = $guestList->event_lists
            ->keyBy("event_name")
            ->toArray();
        //削除するイベント取得
        $deleteEventListIds = [];
        foreach ($currentEventList as $event) {
            if (!isset($event_list_data[$event["event_name"]])) {
                $deleteEventListIds[] = $event["id"];
            }
        }

        //新しいイベントと既存のイベントを同期する変数
        $newEventList = [];

        //新しいイベントを追加
        foreach ($event_list_data as $event) {
            if (isset($currentEventList[$event["event_name"]])) {
                $newEventList[$event["event_name"]]["id"] =
                    $currentEventList[$event["event_name"]]["id"];
                $newEventList[$event["event_name"]][
                    "created_at"
                ] = Carbon::parse(
                    $currentEventList[$event["event_name"]]["created_at"]
                )
                    ->timezone(config("app.timezone"))
                    ->format("Y-m-d H:i:s");
            } else {
                $newEventList[$event["event_name"]][
                    "id"
                ] = UuidGeneratorService::generateOrderedUuid();
                $newEventList[$event["event_name"]]["created_at"] = now();
            }
            $newEventList[$event["event_name"]]["guest_list_id"] = $guestListId;
            $newEventList[$event["event_name"]]["event_name"] =
            TrimmedString::mbTrim($event["event_name"]);
            $newEventList[$event["event_name"]]["event_date"] = !empty(
                $scheduledDate
            )
                ? $scheduledDate
                : $event["event_date"];
            $newEventList[$event["event_name"]]["event_time"] =
                $event["event_time"];
            $newEventList[$event["event_name"]]["venue_name"] =
                $event["venue_name"];
            $newEventList[$event["event_name"]]["venue_name_kana"] =
                $event["venue_name_kana"];
            $newEventList[$event["event_name"]]["venue_postal_code"] =
                $event["venue_postal_code"];
            $newEventList[$event["event_name"]]["venue_address"] =
                $event["venue_address"];
            $newEventList[$event["event_name"]]["venue_tel"] =
                $event["venue_tel"];
            $newEventList[$event["event_name"]]["venue_url"] =
                $event["venue_url"];
            $newEventList[$event["event_name"]]["fees"] = $event["fees"];
            $newEventList[$event["event_name"]]["updated_at"] = now();
        }

        //イベント回答論理削除
        if (!empty($deleteEventListIds)) {
            GuestEventAnswer::whereIn(
                "event_list_id",
                $deleteEventListIds
            )->delete();
        }

        EventList::whereIn("id", $deleteEventListIds)->delete();
        EventList::upsert($newEventList, "id");

        //ゲストリストに他のWeb招待状が存在し、出欠回答が無い場合 開催日を同期
        if (
            $guestList->web_invitations->count() > 1 &&
            $otherInvitationsCount == 0 &&
            $scheduledDate
        ) {
            $web_invitations = $guestList->web_invitations;
            foreach ($web_invitations as $web_invitation) {
                if (
                    !Carbon::parse($scheduledDate)->isSameDay(
                        Carbon::parse($web_invitation->scheduled_date)
                    )
                ) {
                    $editorSettings = $web_invitation->editor_settings ?? [];
                    $blocks = collect($editorSettings["blocks"] ?? []);

                    // 送金日
                    $this->updateBlockContent(
                        $blocks,
                        "gift",
                        "contents.scheduleDate",
                        $giftScheduleDate
                    );

                    // 開催日
                    $this->updateBlockContent(
                        $blocks,
                        "information",
                        "contents.date",
                        $scheduledDate
                    );

                    // 回答日付
                    $this->updateBlockContent(
                        $blocks,
                        "guestAnswer",
                        "contents.limit.date",
                        $guestAnswerDate
                    );

                    // 回答設定値
                    $this->updateBlockContent(
                        $blocks,
                        "guestAnswer",
                        "contents.limit.setting",
                        $guestAnswerSet
                    );

                    $editorSettings["blocks"] = $blocks->toArray();
                    $web_invitation->update([
                        "editor_settings" => $editorSettings,
                        "scheduled_date" => $scheduledDate,
                        "reply_deadline_date" => $replyDeadlineDate,
                        "scheduled_transfer_date" => $scheduledTransferDate,
                    ]);
                }
            }
        }
    }

    //同ゲストリストのWeb招待状の出欠回答カウント
    private function getOtherInvitationsCount($guestListId)
    {
        return WebInvitation::whereHas("guest_list", function ($query) use (
            $guestListId
        ) {
            $query->where("id", $guestListId);
        })
            ->with([
                "guests" => function ($query) {
                    $query->withCount("guest_event_answers");
                },
            ])
            ->get()
            ->pluck("guests")
            ->flatten()
            ->sum("guest_event_answers_count");
    }

    //他Web招待状の開催日 != 自招待状の開催日 のチェック
    private function checkEventDate($guestListId, $scheduledDate)
    {
        $guestList = GuestList::find($guestListId);

        if (!$guestList) {
            throw new InValidException("ゲストリスト情報が見つかりません。", [
                "error" => ["ゲストリスト情報が見つかりません。"],
            ]);
        }

        $dates = $guestList->event_lists()->pluck("event_date");

        $carbonDates = $dates->map(function ($date) {
            return Carbon::parse($date);
        });

        foreach ($carbonDates as $carbonDate) {
            if (!$carbonDate->isSameDay($scheduledDate)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 汎用的なブロック更新関数
     *
     * @param object $blocks
     * @param string $blockId
     * @param string $contentPath
     * @param string $value
     * @return void
     */
    public function updateBlockContent(&$blocks, $blockId, $contentPath, $value)
    {
        $block = $blocks->firstWhere("id", $blockId);
        if ($block) {
            data_set($block, $contentPath, $value);
            $blocks = $blocks->map(function ($b) use ($block, $blockId) {
                return $b["id"] === $blockId ? $block : $b;
            });
        }
    }
}
