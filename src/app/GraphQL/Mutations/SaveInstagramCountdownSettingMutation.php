<?php

namespace App\GraphQL\Mutations;

use App\Models\InstagramCountdownSetting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;

class SaveInstagramCountdownSettingMutation
{
    /**
     * Instagramカウントダウン画像設定を作成・更新
     *
     * @param null $_
     * @param array $args
     * @return InstagramCountdownSetting
     */
    public function __invoke($_, array $args)
    {
        $input = $args['input'];
        $user = Auth::user();

        return DB::transaction(function () use ($input, $user) {
            // 既存の設定があるかチェック
            $existing = InstagramCountdownSetting::findByMemberAndType(
                $user->id,
                $input['instagram_type']
            );

            if ($existing) {
                // 更新
                $existing->update([
                    'image_data' => $input['image_data'] ?? null,
                    'show_names' => $input['show_names'] ?? true,
                    'show_event_date' => $input['show_event_date'] ?? true,
                ]);
                
                return $existing->fresh();
            } else {
                // 新規作成
                return InstagramCountdownSetting::create([
                    'member_id' => $user->id,
                    'instagram_type' => $input['instagram_type'],
                    'image_data' => $input['image_data'] ?? null,
                    'show_names' => $input['show_names'] ?? true,
                    'show_event_date' => $input['show_event_date'] ?? true,
                ]);
            }
        });
    }
}
