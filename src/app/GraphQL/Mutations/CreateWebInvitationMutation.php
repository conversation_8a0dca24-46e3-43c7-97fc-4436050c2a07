<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\WebInvitation;
use App\Models\GuestList;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\MemberAuthorizationService;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use App\GraphQL\Exceptions\InValidException;
use App\Models\System;
use Carbon\Carbon;
use App\Values\DateOperator;

//Web招待状作成
final class CreateWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "m_web_invitation_id",
            "guest_list_id",
            "name",
            "public_url",
            "password",
            "is_password",
            "is_public",
            "scheduled_date",
            "reply_deadline_date",
            "scheduled_transfer_date",
            "prepayment_due_date",
            "editor_settings",
            "block_settings",
        ]);

        return DB::transaction(function () use ($credentials) {
            $member = Auth::user();
            if (!$member) {
                throw new AuthenticationException();
            }
            MemberAuthorizationService::authorize($member->id);

            $guestList = GuestList::find(
                $credentials["guest_list_id"] ?? ""
            );
            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            //editor_settings を加工
            $editorSettings = $credentials['editor_settings'] ?? [];

            if ($editorSettings && is_array($editorSettings)) {

                $system = System::getFirstSystem();

                // 期間内の場合
                if (!DateOperator::canUsePrepaymentByDateRange(
                    $system->prepayment_pause_from_at,
                    $system->prepayment_pause_to_at
                )) {
                    foreach ($editorSettings["blocks"] ?? [] as $key => $block) {
                        if (
                            isset($block['contents']['isUseGift']) &&
                            $block['contents']['isUseGift'] === true
                        ) {
                            $editorSettings["blocks"][$key]['contents']['isUseGift'] = false;
                        }
                    }
                }
                $credentials['editor_settings'] = $editorSettings;
            }

            $webInvitation = new WebInvitation();
            $webInvitation->member_id = $member->id;
            $webInvitation->fill($credentials);
            $webInvitation->save();

            return $webInvitation;
        });
    }
}
