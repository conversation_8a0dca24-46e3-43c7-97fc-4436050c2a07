<?php

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use Illuminate\Auth\AuthenticationException;
use App\Models\Member;
use Illuminate\Support\Facades\Request;

//会員ログイン
final class LoginMemberMutaion
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["email", "password"]);

        // ユーザーの存在確認
        $member = Member::findByEmail($credentials["email"]);

        if (!$member) {
            throw new AuthenticationException(
                "メールアドレスもしくは、パスワードが違います。",
                ["member"]
            );
        }

        // SNSで会員登録されてる場合
        if ($member->sns_login_id && ! $member->is_use_password) {
            $sns = array_key_first($member->sns_login_id);
            if ($sns == "line") {
                throw new AuthenticationException(
                    "LINEで会員登録されたアカウントです。LINEでログインしてください。",
                    ["member"]
                );
            } elseif ($sns == "google") {
                throw new AuthenticationException(
                    "Googleで会員登録されたアカウントです。Googleでログインしてください。",
                    ["member"]
                );
            }
        }

        // パスワードの確認
        if (!$member->validPassword($credentials["password"])) {
            throw new AuthenticationException(
                "メールアドレスもしくは、パスワードが違います。",
                ["member"]
            );
        }

        // 仮登録状態の場合
        if ($member->is_regist == 0) {
            throw new AuthenticationException(
                "仮登録の状態です。\n".
                "送信された会員登録URLをクリックして登録を完了してください",
                ["member"]
            );
        }

        // トークンの発行
        $token = $member->createAuthToken();

        // IP保存
        $member->tokens()->latest()->first()?->forceFill([
            'ip_address' => Request::getClientIp(),
        ])->save();

        return $token;
    }
}
