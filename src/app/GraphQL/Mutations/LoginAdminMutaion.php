<?php

namespace App\GraphQL\Mutations;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Arr;
use App\Models\Admin;
use Illuminate\Auth\AuthenticationException;

final class LoginAdminMutaion
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["email", "password"]);

        // ユーザーの存在確認
        $admin = Admin::findByEmail($credentials["email"]);

        if (!$admin) {
            throw new AuthenticationException(
                "メールアドレスもしくは、パスワードが違います。",
                ["admin"]
            );
        }

        // パスワードの確認
        if (!$admin->validPassword($credentials["password"])) {
            throw new AuthenticationException(
                "メールアドレスもしくは、パスワードが違います。",
                ["admin"]
            );
        }

        // トークンの発行
        $token = $admin->createAuthToken();
        return $token;
    }
}
