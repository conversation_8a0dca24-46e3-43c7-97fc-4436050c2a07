<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\WebInvitation;
use App\Models\GuestList;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\MemberAuthorizationService;
use App\GraphQL\Exceptions\InValidException;
use App\Models\System;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use App\Values\DateOperator;


//コピーweb招待状
final class CopyWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id"]);
        return DB::transaction(function () use ($credentials) {
            $webInvitation = WebInvitation::find($credentials["id"]);

            if (!$webInvitation) {
                throw new InValidException("WEB招待状情報が見つかりません。", [
                    "error" => ["WEB招待状情報が見つかりません。"],
                ]);
            }

            MemberAuthorizationService::authorize($webInvitation->member_id);

            $newWebInvitation = $webInvitation->replicate();
            $newWebInvitation->name = $webInvitation->name . "のコピー";
            $newWebInvitation->password = null;
            $newWebInvitation->is_password = false;
            $newWebInvitation->is_public = false;
            $newWebInvitation->public_url = null;
            $newWebInvitation->save();

            $system = System::getFirstSystem();

            if (
                !DateOperator::canUsePrepaymentByDateRange(
                    $system->prepayment_from,
                    $system->prepayment_to
            )) {
                $editorSettings = new Collection(
                    $newWebInvitation->editor_settings["blocks"] ?? []
                );

                $updateEditorSettings["blocks"] = $editorSettings->map(function ($block) {
                    if ($block['id'] === 'gift') {
                        $block['contents']['isUseGift'] = false;
                    }
                    return $block;
                });
                $newWebInvitation->editor_settings = $updateEditorSettings;
            }

            $newWebInvitation->save();

            return $newWebInvitation;
        });
    }
}
