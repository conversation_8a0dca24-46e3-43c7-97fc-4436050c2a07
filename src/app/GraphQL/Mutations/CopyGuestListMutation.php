<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\GuestGroup;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use App\Models\GuestFreeItemValue;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Services\UuidGeneratorService;

//ゲストリストコピー
final class CopyGuestListMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id", "name"]);

        return DB::transaction(function () use ($credentials) {
            $guestList = GuestList::with([
                "guests",
                "guests.guest_group",
                "guests.guest_event_answers",
                "guests.guest_survey_answers",
                "guest_groups",
                "guest_tags",
                "guest_tags.guests",
            ])->find($credentials["id"]);

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            if ($guestList->member_id != Auth::user()->id) {
                throw new AuthenticationException();
            }

            //ゲストリスト
            $copyGuestList = $guestList->replicate();
            $copyGuestList->name = $credentials["name"];
            $copyGuestList->is_default = false;
            $copyGuestList->created_at = $guestList->created_at;
            $copyGuestList->updated_at = $guestList->updated_at;
            $copyGuestList->save();

            //ゲストグループ
            $guestGroupToInsert = [];
            foreach ($guestList->guest_groups ?? [] as $guestGroup) {
                $copyGuestGroup = $guestGroup->replicate()->toArray();
                $copyGuestGroup[
                    "id"
                ] = UuidGeneratorService::generateOrderedUuid();
                $copyGuestGroup["guest_list_id"] = $copyGuestList->id;
                $copyGuestGroup["created_at"] = Carbon::parse(
                    $guestGroup->created_at
                )->format("Y-m-d H:i:s");
                $copyGuestGroup["updated_at"] = Carbon::parse(
                    $guestGroup->updated_at
                )->format("Y-m-d H:i:s");
                $guestGroupToInsert[] = $copyGuestGroup;
            }
            GuestGroup::insert($guestGroupToInsert);

            $newGuestGroupList = GuestGroup::where(
                "guest_list_id",
                $copyGuestList->id
            )->pluck("id", "name");

            //ゲストタグ
            $newGuestTagGuestList = [];
            foreach ($guestList->guest_tags ?? [] as $guestTag) {
                $copyGuestTag = $guestTag->replicate();
                $copyGuestTag->guest_list_id = $copyGuestList->id;
                $copyGuestTag->created_at = $guestTag->created_at;
                $copyGuestTag->updated_at = $guestTag->updated_at;
                $copyGuestTag->save();

                foreach ($guestTag->guests->pluck("id") ?? [] as $guest_id) {
                    $newGuestTagGuestList[$guest_id][] = $copyGuestTag->id;
                }
            }

            //ゲストコピー
            $guestReplacementIds = [];
            foreach ($guestList->guests as $guest) {
                $copyGuest = $guest->replicate();
                $copyGuest->guest_list_id = $copyGuestList->id;
                $copyGuest->created_at = $guest->created_at;
                $copyGuest->updated_at = $guest->updated_at;

                // 複製処理の際にコピーしない
                $copyGuest->web_invite_reply_datetime = null; //Web招待状返信日時
                $copyGuest->web_invitation_id = null; //web招待状ID
                $copyGuest->m_web_invitation_id = null; //web招待状マスタID
                $copyGuest->payment_method = null; //事前支払い方法
                $copyGuest->gift_amount = 0; // お気持ち金額
                $copyGuest->is_system_fee = false; //システム利用料負担フラグ
                $copyGuest->system_fee = 0; //システム利用料負担金額
                $copyGuest->system_fee_rate = 0; //システム利用料率
                $copyGuest->total_amount = 0; // 会費・ご祝儀・お気持ち金額合計金額
                $copyGuest->settlement_amount = 0; // 決算金額
                $copyGuest->card_settlement_id = null; //クレジットカード

                //ゲストグループ付け替え
                if ($guest->guest_group) {
                    $copyGuest->guest_group_id =
                        $newGuestGroupList[$guest->guest_group->name] ?? null;
                }
                //連名関係付け替え
                if (
                    !empty($copyGuest->parent_guest_id) &&
                    isset($guestReplacementIds[$guest->parent_guest_id])
                ) {
                    $copyGuest->parent_guest_id =
                        $guestReplacementIds[$guest->parent_guest_id];
                } else {
                    $copyGuest->parent_guest_id = null;
                }
                $copyGuest->save();

                //旧ゲストID[新ゲストID]
                $guestReplacementIds[$guest->id] = $copyGuest->id;

                //フリー項目
                $guestFreeItemValues = [];
                foreach (
                    $guest->guest_free_item_values ?? []
                    as $guestFreeItemValue
                ) {
                    $copyGuestFreeItemValue = $guestFreeItemValue
                        ->replicate()
                        ->toArray();
                    $copyGuestFreeItemValue[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $copyGuestFreeItemValue["guest_id"] = $copyGuest->id;
                    $copyGuestFreeItemValue["created_at"] = Carbon::parse(
                        $guestFreeItemValue->created_at
                    )->format("Y-m-d H:i:s");
                    $copyGuestFreeItemValue["updated_at"] = Carbon::parse(
                        $guestFreeItemValue->updated_at
                    )->format("Y-m-d H:i:s");

                    $guestFreeItemValues[] = $copyGuestFreeItemValue;
                }

                GuestFreeItemValue::insert($guestFreeItemValues);

                //ゲストタグ付け替え
                if (isset($newGuestTagGuestList[$guest->id])) {
                    $copyGuest
                        ->guest_tags()
                        ->sync($newGuestTagGuestList[$guest->id]);
                }
            }

            return $copyGuestList;
        });
    }
}
