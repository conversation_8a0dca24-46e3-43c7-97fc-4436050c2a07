<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\Member;
use App\Models\GuestList;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use App\Notifications\CreateMemberNotification;
use Carbon\Carbon;
use App\Consts\SystemConst;

final class CreateMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["uuid"]);

        return DB::transaction(function () use ($credentials) {

            // 会員を検索
            $member = Member::where("tmp_uuid", $credentials["uuid"])
                ->where("is_regist", false)
                ->where("tmp_uuid_expires_at", ">=", Carbon::now())
                ->first();

            // 会員が存在しない場合はエラーメッセージを返す
            if (!$member) {
                throw new InValidException("無効なURLです。", [
                    "error" => ["無効なURLです。"],
                ]);
            }

            // 本会員フラグをtrueに設定
            $member->is_regist = true;

            // 会員番号を設定
            $member->alternate_member_number = Member::generateUniqueFaId();

            $member->save();

            //デフォルトゲストリスト作成
            $guestList = new GuestList();
            $guestList->member_id = $member->id;
            $guestList->name = SystemConst::DEFAULT_GUEST_LIST_NAME;
            $guestList->is_default = true;
            $guestList->save();

            //メール送信
            $member->notify(
                new CreateMemberNotification($member->email, $member->alternate_member_number)
            );

            // トークンの発行
            $token = $member->createAuthToken();
            return $token;
        });
    }
}
