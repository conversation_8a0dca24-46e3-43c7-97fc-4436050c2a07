<?php

namespace App\GraphQL\Mutations;

use Illuminate\Support\Facades\Password;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Illuminate\Auth\AuthenticationException;
use App\Models\Member;
use App\Models\MemberEmailChange;
use App\Mail\ChangeEmailMail;
use Illuminate\Support\Str;

final class SendChangeEmailUrlMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["email", "password"]);

        $member = Auth::user();
        if (!$member->validPassword($credentials["password"])) {
          throw new AuthenticationException(
              "パスワードが違います。",
              ["member"]
          );
        }
        $memberModel = Member::findByEmail($credentials["email"]);
        if ($memberModel) {
          throw new AuthenticationException(
            "すでに会員登録されているメールアドレスです",
            ["member"]
          );
        }


        // 過去のは削除
        foreach ($member->email_changes as $emailChange) {
          $emailChange->delete();
        }

        $token = Str::random(60);
        MemberEmailChange::create([
          'member_id' => $member->id,
          'new_email' => $credentials['email'],
          'token' => $token,
        ]);
        
        $url = str_replace('/login/password', "/email/change" , env("MAIL_FRONT_URL_RESET")).'/'.$token;
        Mail::to($credentials["email"])->send(
          new ChangeEmailMail($credentials["email"], $member, $url)
        );

        return true;
    }
}
