<?php

namespace App\GraphQL\Mutations;

use App\Models\InstagramCountdownSetting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Services\MemberAuthorizationService;

class UpdateInstagramCountdownSettingMutation
{
    /**
     * Instagramカウントダウン画像設定を更新
     *
     * @param null $_
     * @param array $args
     * @return InstagramCountdownSetting
     */
    public function __invoke($_, array $args)
    {
        $input = $args['input'];
        $uuid = $input['uuid'];

        return DB::transaction(function () use ($input, $uuid) {
            $setting = InstagramCountdownSetting::findOrFail($uuid);
            
            // 認証チェック
            MemberAuthorizationService::authorize($setting->member_id);

            // 更新データを準備
            $updateData = [];
            
            if (isset($input['image_data'])) {
                $updateData['image_data'] = $input['image_data'];
            }
            
            if (isset($input['show_names'])) {
                $updateData['show_names'] = $input['show_names'];
            }
            
            if (isset($input['show_event_date'])) {
                $updateData['show_event_date'] = $input['show_event_date'];
            }

            $setting->update($updateData);
            
            return $setting->fresh();
        });
    }
}
