<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use App\Enums\OwnerType;
use App\Enums\FileType;
use App\Models\Image;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

final class GuestUploadImageMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["dir_name", "file_data"]);

        return DB::transaction(function () use ($credentials) {
            $dirName = $credentials["dir_name"];
            $fileName = (string) Str::uuid();
            $fileData = $credentials["file_data"];

            //S3の設定を取得
            $storage = Storage::disk("s3");
            $config = $storage->getConfig();

            //パス作成
            $dir = env("AWS_DIR_MEMBER") . "{$dirName}/" . env("AWS_DIR_GUEST");
            // フォルダが存在しない場合に作成
            if (!$storage->exists($dir)) {
                $storage->makeDirectory($dir);
            }

            //画像をbase64からデコード
            preg_match("/data:image\/(\w+);base64,/", $fileData, $matches);
            $extension = $matches[1];
            $img = preg_replace("/^data:image.*base64,/", "", $fileData);
            $img = str_replace(" ", "+", $img);
            $fileData = base64_decode($img);

            //画像保存
            $path = $dir . $fileName . "." . $extension;
            $storage->put($path, $fileData);

            //DBに保存
            $imageModel = new Image();
            $imageModel->owner_type = OwnerType::OWNER_TYPE_MEMBER;
            $imageModel->owner_id = $dirName;
            $imageModel->file_type = 0;
            $imageModel->name = $fileName;
            $imageModel->extension_type = $extension;
            $imageModel->save();

            //minio環境の場合
            if ($config["use_path_style_endpoint"]) {
                $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
                $storage = Storage::build(
                    Arr::set($config, "endpoint", $endpoint)
                );
            }

            //一時署名付きURL作成
            $presignedUrl = $storage->temporaryUrl(
                $path,
                now()->addSeconds($config["url_member_expiration"])
            );

            return [
                "uuid" => $imageModel->uuid,
                "presigned_url" => $presignedUrl,
            ];
        });
    }
}
