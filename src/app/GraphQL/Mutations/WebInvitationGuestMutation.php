<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\Member;
use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestFreeItemValue;
use App\Models\GuestEventAnswer;
use App\Models\GuestSurveyAnswer;
use App\Models\WebInvitation;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use App\Mail\WebInvitationGuestCompleteMemberMail;
use App\Mail\WebInvitationGuestCompleteGuestMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Nuwave\Lighthouse\Exceptions\ValidationException;
use App\Enums\MemberConfirmTypeEnum;
use App\Enums\PaymentMethodEnum;
use App\Services\GmoPGService;
use App\Services\UuidGeneratorService;
use App\Services\PaymentLogService;
use App\Enums\InvitationDeliveryEnum;
use Carbon\Carbon;
use App\Rules\MaxWordCountValidation;
use App\Rules\RecentGuestSubmission;

//ゲスト自動登録(WEB招待状回答)
final class WebInvitationGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "is_save",
            "input",
            "guests",
            "free_item_values",
            "guest_event_answers",
            "guest_survey_answers",
            "payment",
        ]);

        $guestLastName = $credentials["input"]["last_name"] ?? "";
        $guestFirstName = $credentials["input"]["first_name"] ?? "";
        $guestName = $guestLastName . $guestFirstName;

        // 決済ログ
        $paymentLogService = new PaymentLogService();
        $paymentLogService->logInputParameters($guestName, $credentials);

        // バリデーションを実行
        $validator = Validator::make(
            $args,
            $this->getRules(),
            [],
            $this->getAttributes()
        );

        // バリデーションが失敗した場合、エラーメッセージを含む例外を投げる
        if (!$validator->passes()) {
            throw new ValidationException(
                "Validation failed for the field [createWebInvitationGuest].",
                $validator
            );
        }

        // 保存フラグがfalseの場合はここで処理を終了
        if (!$credentials["is_save"]) {
            return null;
        }

        try {
            DB::beginTransaction();

            $guest = $credentials["input"] ?? [];
            $guests = $credentials["guests"] ?? [];
            $freeItemValues = $credentials["free_item_values"] ?? [];
            $guestEventAnswers = $credentials["guest_event_answers"] ?? [];
            $guestSurveyAnswers = $credentials["guest_survey_answers"] ?? [];
            $payment = $credentials["payment"] ?? [];

            //ゲストリスト情報取得
            $guestList = GuestList::find($guest["guest_list_id"] ?? 0);

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            $memberId = $guestList->member_id;

            //Web招待状情報取得
            $webInvitation = WebInvitation::find(
                $guest["web_invitation_id"] ?? 0
            );
            if (!$webInvitation) {
                throw new InValidException("web招待状情報が見つかりません。", [
                    "error" => ["web招待状情報が見つかりません。"],
                ]);
            }

            $webInvitationId = $webInvitation->id;

            //代表ゲスト
            $guestModel = new Guest();
            $guestModel->fill($guest);
            $guestModel->member_id = $memberId;
            $guestModel->member_confirm_type = MemberConfirmTypeEnum::New;
            $guestModel->invitation_delivery =
                InvitationDeliveryEnum::WEB_INVITATION;
            if (!empty($guest["media_type"])) {
                $guestModel->media_type = $guest["media_type"]->value;
            }

            $paymentLogService->logGuestRegistration(
                $memberId,
                $webInvitationId,
                $guestName,
                $credentials
            );

            // 事前決済実行
            $payment_method = 0;
            if ($guestModel->payment_method instanceof PaymentMethodEnum) {
                $payment_method = $guestModel->payment_method->value;
            }
            if ($payment_method === PaymentMethodEnum::ADVANCE_PAYMENT) {
                try {
                    $gmoPG = new GmoPGService();
                    $post = [];
                    $post["AccessID"] = $payment["access_id"];
                    $post["AccessPass"] = $payment["access_pass"];
                    $order_id = "";
                    if (isset($payment["order_id"])) {
                        $order_id = $payment["order_id"];
                    }
                    if ($order_id) {
                        // order_id が指定されてる場合は決済済み
                        $result = [];
                        $result["OrderID"] = $order_id;
                        $paymentLogService->logPaymentCompleted(
                            $memberId,
                            $webInvitationId,
                            $guestName,
                            $order_id,
                            $post,
                            $credentials
                        );
                    } else {
                        // 3Dセキュア決済を実行
                        $result = $gmoPG->secureTran2($post);
                        $paymentLogService->log3DSecureResult(
                            $memberId,
                            $webInvitationId,
                            $guestName,
                            $post,
                            $credentials,
                            $result
                        );
                        // エラーの場合
                        if (isset($result["ErrInfo"])) {
                            $result["message"] = $gmoPG->getErrorMessage(
                                $result["ErrInfo"]
                            );
                            throw new InValidException($result["message"], [
                                "error" => [$result],
                            ]);
                        }
                    }

                    if (isset($result["OrderID"])) {
                        // 成功の場合
                        // 決済IDを保存
                        $guestModel->card_settlement_id = $result["OrderID"];
                    } else {
                        // エラーの場合
                        throw new InValidException("決済に失敗しました。", [
                            "error" => ["決済に失敗しました。"],
                        ]);
                    }
                } catch (\Exception $e) {
                    // エラー処理
                    throw new InValidException($e->getMessage(), [
                        "error" => [$e->getMessage()],
                    ]);
                }
            } else {
                $guestModel->gift_amount = null;
                $guestModel->is_system_fee = false;
                $guestModel->system_fee = 0;
                $guestModel->system_fee_rate = 0;
                $guestModel->total_amount = 0;
                $guestModel->settlement_amount = 0;
                $guestModel->card_settlement_id = null;
            }

            $guestModel->save();

            //筆頭ゲストのフリー項目値
            foreach ($freeItemValues as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $memberId;
                $row["guest_id"] = $guestModel->id;
                $freeItemValues[$key] = $row;
            }
            GuestFreeItemValue::upsert($freeItemValues, "id");

            //ゲストアンケート回答
            foreach ($guestSurveyAnswers as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $memberId;
                $row["guest_id"] = $guestModel->id;
                $guestSurveyAnswers[$key] = $row;
            }
            GuestSurveyAnswer::upsert($guestSurveyAnswers, "id");

            //メールに渡す情報まとめ
            $member = Member::find($memberId);
            $groom_family_full_name = "";
            $bride_family_full_name = "";

            $groom_and_bride = $member->groom_and_bride;
            if(!empty($groom_and_bride->get(0))){
                $groom_family_full_name = $groom_and_bride->get(0)->last_name . " " . $groom_and_bride->get(0)->first_name;
            }
            if(!empty($groom_and_bride->get(1))){
                $bride_family_full_name = $groom_and_bride->get(1)->last_name . " " . $groom_and_bride->get(1)->first_name;
            }

            //ゲストイベント回答
            $eventList = $guestList->event_name_uuid_list;
            $mailList = [];
            $mailList = [
                "last_name" => $guestModel->last_name,
                "first_name" => $guestModel->first_name,
                "groom_full_name" => $groom_family_full_name,
                "bride_full_name" => $bride_family_full_name,
                "scheduled_date" => Carbon::parse(
                    $webInvitation->scheduled_date
                )->isoFormat("YYYY年MM月DD日(ddd)"),
                "publicUrl" => $webInvitation->full_url,
                "paymentMethod" => $guestModel->payment_method,
                "attendance" => [],
                "guests" => [],
                "payment_list" => [],
            ];
            $mailList["payment_list"][$guestModel->id]["name"] =
                $guestModel->last_name . " " . $guestModel->first_name;
            $mailList["payment_list"][$guestModel->id]["settlement_amount"] =
                $guestModel->settlement_amount;
            $guestEventAnswerList = [];
            $guestEventList = [];
            foreach ($guestEventAnswers as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $memberId;
                $row["guest_id"] = $guestModel->id;
                $row["event_list_id"] = $eventList[$row["name"]] ?? null;
                $row["payment_amount"] = ($payment_method === PaymentMethodEnum::ADVANCE_PAYMENT)
                ? ($row["payment_amount"] ?? 0)
                : 0;
                $mailList["attendance"][$row["name"]] = $row["attendance"];
                $guestEventList[$row["name"]] = $row["name"];
                unset($row["date"]);
                unset($row["name"]);
                $guestEventAnswerList[] = $row;
            }
            GuestEventAnswer::upsert($guestEventAnswerList, "id");

            $mailList["event_list"] = implode("・", $guestEventList);

            //連名者
            foreach ($guests as $row) {
                $childGuestEventAnswers = $row["guest_event_answers"] ?? [];
                unset($row["guest_event_answers"]);

                $childGuest = new Guest();
                $childGuest->fill($row);
                $childGuest->parent_guest_id = $guestModel->id;
                $childGuest->member_id = $memberId;
                $childGuest->member_confirm_type = MemberConfirmTypeEnum::New;
                $childGuest->invitation_delivery =
                    InvitationDeliveryEnum::WEB_INVITATION;
                $childGuest->media_type = !empty($row["media_type"])
                    ? $row["media_type"]->value
                    : null;
                if($payment_method !== PaymentMethodEnum::ADVANCE_PAYMENT){
                    $childGuest->gift_amount = null;
                    $childGuest->is_system_fee = false;
                    $childGuest->system_fee = 0;
                    $childGuest->system_fee_rate = 0;
                    $childGuest->total_amount = 0;
                    $childGuest->settlement_amount = 0;
                    $childGuest->card_settlement_id = null;
                }
                $childGuest->save();

                $mailList["guests"][] =
                    $childGuest->last_name . " " . $childGuest->first_name;
                $mailList["payment_list"][$childGuest->id]["name"] =
                    $childGuest->last_name . " " . $childGuest->first_name;
                $mailList["payment_list"][$childGuest->id][
                    "settlement_amount"
                ] = $childGuest->settlement_amount;

                foreach (
                    $childGuestEventAnswers
                    as $key => $childGuestEventAnswer
                ) {
                    $childGuestEventAnswer[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $childGuestEventAnswer["guest_id"] = $childGuest->id;
                    $childGuestEventAnswer["member_id"] = $memberId;
                    $childGuestEventAnswer["event_list_id"] =
                        $eventList[$childGuestEventAnswer["name"]] ?? null;
                    $childGuestEventAnswer["payment_amount"] = ($payment_method === PaymentMethodEnum::ADVANCE_PAYMENT)
                        ? ($childGuestEventAnswer["payment_amount"] ?? 0)
                        : 0;
                    unset($childGuestEventAnswer["date"]);
                    unset($childGuestEventAnswer["name"]);
                    $childGuestEventAnswers[$key] = $childGuestEventAnswer;
                }
                GuestEventAnswer::upsert($childGuestEventAnswers, "id");
            }

            //会員にメール送信
            $member = Member::find($memberId);
            Mail::to($member->email)->send(
                new WebInvitationGuestCompleteMemberMail(
                    $member->first_name,
                    $member->last_name
                )
            );

            //ゲストにメール送信
            if (!empty($guestModel->email)) {
                Mail::to($guestModel->email)->send(
                    new WebInvitationGuestCompleteGuestMail($mailList)
                );
            }

            $paymentLogService->logApiResult(
                $memberId,
                $webInvitationId,
                $guestName,
                $post ?? [],
                $credentials,
                $result ?? [],
                $guestModel->toArray()
            );
            DB::commit();
            return $guestModel;
        } catch (\Exception $e) {
            DB::rollBack();
            $paymentLogService->logWebInvitationError(
                $memberId ?? null,
                $webInvitationId ?? null,
                $guestName,
                $post ?? [],
                $credentials,
                $result ?? [],
                !empty($guestModel) ? $guestModel->toArray() : [],
                $e->getMessage()
            );
            throw new InValidException($e->getMessage(), [
                "error" => [$e->getMessage()],
            ]);
        }
    }

    /**
     * Web自動回答のバリデーションルール
     *
     * @return array
     */
    private function getRules()
    {
        return [
            "is_save" => "required|boolean",
            "input.guest_list_id" => "nullable",
            "input.web_invitation_id" => "nullable",
            "input.m_web_invitation_id" => "nullable",
            "input.guest_type" => "nullable|in:1,2", // GuestTypeEnumに応じて適切な値を設定してください
            "input.last_name" => "required|string|max:255",
            "input.first_name" => "required|string|max:255",
            "input.last_name_kana" => "nullable|string|max:255",
            "input.first_name_kana" => "nullable|string|max:255",
            "input.last_name_romaji" => "nullable|string|max:255",
            "input.first_name_romaji" => "nullable|string|max:255",
            "input.gender" => "nullable|in:0,1,2", // GenderEnumに応じて適切な値を設定してください
            "input.allergies" => "nullable",
            "input.allergy" => "nullable|string|max:255",
            "input.birthdate" => "nullable|date",
            "input.postal_code" => "nullable|string|max:255",
            "input.prefecture" => "nullable|string|max:255",
            "input.city" => "nullable|string|max:255",
            "input.address" => "nullable|string|max:255",
            "input.building" => "nullable|string|max:255",
            "input.phone" => "nullable|string|max:255",
            "input.email" => ["nullable", "email", new RecentGuestSubmission],
            "input.message" => ["nullable", new MaxWordCountValidation(300)],
            "input.invitation_delivery" => "nullable|integer",
            "input.guest_title" => "nullable|string|max:255",
            "input.guest_honor" => "nullable|string|max:255",
            "input.relationship" => "nullable|string|max:255",
            "input.web_invite_reply_datetime" => "nullable|date",
            "input.member_confirm_type" => "nullable|boolean",
            "input.payment_method" => "nullable|in:1,2,3", // PaymentMethodEnumに応じて適切な値を設定してください
            "input.gift_amount" => "nullable|integer",
            "input.is_system_fee" => "nullable|boolean",
            "input.system_fee" => "nullable|integer",
            "input.system_fee_rate" => "nullable|numeric",
            "input.total_amount" => "nullable|integer",
            "input.settlement_amount" => "nullable|integer",
            "input.card_settlement_id" => "nullable|string|max:255",
            "guests.*.guest_list_id" => "nullable",
            "guests.*.web_invitation_id" => "nullable",
            "guests.*.m_web_invitation_id" => "nullable",
            "guests.*.guest_type" => "nullable|in:1,2", // GuestTypeEnumに応じて適切な値を設定してください
            "guests.*.last_name" => "required|string|max:255",
            "guests.*.first_name" => "required|string|max:255",
            "guests.*.last_name_kana" => "nullable|string|max:255",
            "guests.*.first_name_kana" => "nullable|string|max:255",
            "guests.*.last_name_romaji" => "nullable|string|max:255",
            "guests.*.first_name_romaji" => "nullable|string|max:255",
            "guests.*.gender" => "nullable|in:0,1,2", // GenderEnumに応じて適切な値を設定してください
            "guests.*.allergies" => "nullable",
            "guests.*.allergy" => "nullable|string|max:255",
            "guests.*.birthdate" => "nullable|date",
            "guests.*.postal_code" => "nullable|string|max:255",
            "guests.*.prefecture" => "nullable|string|max:255",
            "guests.*.city" => "nullable|string|max:255",
            "guests.*.address" => "nullable|string|max:255",
            "guests.*.building" => "nullable|string|max:255",
            "guests.*.phone" => "nullable|string|max:255",
            "guests.*.email" => "nullable|email",
            "guests.*.message" => ["nullable", new MaxWordCountValidation(300)],
            "guests.*.invitation_delivery" => "nullable|integer",
            "guests.*.guest_title" => "nullable|string|max:255",
            "guests.*.guest_honor" => "nullable|string|max:255",
            "guests.*.relationship" => "nullable|string|max:255",
            "guests.*.web_invite_reply_datetime" => "nullable|date",
            "guests.*.member_confirm_type" => "nullable|boolean",
            "guests.*.payment_method" => "nullable|in:1,2,3", // PaymentMethodEnumに応じて適切な値を設定してください
            "guests.*.gift_amount" => "nullable|integer",
            "guests.*.is_system_fee" => "nullable|boolean",
            "guests.*.system_fee" => "nullable|integer",
            "guests.*.system_fee_rate" => "nullable|numeric",
            "guests.*.total_amount" => "nullable|integer",
            "guests.*.settlement_amount" => "nullable|integer",
            "guests.*.card_settlement_id" => "nullable|string|max:255",
            "free_item_values.*.name" => "nullable|string|max:255",
            "free_item_values.*.content" => "nullable|string|max:255",
            "guest_event_answers.*.name" => "nullable|string|max:255",
            "guest_event_answers.*.date" => "nullable|date",
            "guest_event_answers.*.payment_amount" => "nullable|integer",
            "guest_event_answers.*.attendance" => "nullable|string|max:255",
            "guest_survey_answers.*.ui_type" => "nullable|in:1,2,3",
            "guest_survey_answers.*.answer_content" =>
                "nullable|string|max:255",
        ];
    }

    /**
     * フィールドの属性名取得
     *
     * @return array
     */
    private function getAttributes()
    {
        return [
            "is_save" => "保存フラグ",
            "input.guest_list_id" => "ゲストリスト",
            "input.guest_group_id" => "ゲストグループ",
            "input.parent_guest_id" => "連名筆頭者",
            "input.web_invitation_id" => "Web招待状",
            "input.m_web_invitation_id" => "Web招待状マスタ",
            "input.guest_type" => "ゲストタイプ",
            "input.last_name" => "姓",
            "input.first_name" => "名",
            "input.last_name_kana" => "せい",
            "input.first_name_kana" => "めい",
            "input.last_name_romaji" => "姓(ローマ字)",
            "input.first_name_romaji" => "名(ローマ字)",
            "input.gender" => "性別",
            "input.allergies" => "アレルギー品目",
            "input.allergy" => "その他アレルギー",
            "input.birthdate" => "生年月日",
            "input.image_url" => "プロフィール画像",
            "input.postal_code" => "郵便番号",
            "input.prefecture" => "都道府県",
            "input.city" => "市区町村",
            "input.address" => "丁目・番地",
            "input.building" => "建物名・部屋番号",
            "input.phone" => "電話番号",
            "input.email" => "メールアドレス",
            "input.message" => "メッセージ",
            "input.invitation_delivery" => "招待状お届け方法",
            "input.guest_title" => "ゲスト肩書",
            "input.guest_honor" => "ゲスト敬称",
            "input.relationship" => "間柄",
            "input.web_invite_reply_datetime" => "Web招待状返信日時",
            "input.member_confirm_type" => "会員確認済種別",
            "input.payment_method" => "会費・ご祝儀支払い方法",
            "input.gift_amount" => "お気持ち金額",
            "input.is_system_fee" => "システム利用料負担FLG",
            "input.system_fee" => "システム利用料",
            "input.system_fee_rate" => "システム利用料率",
            "input.total_amount" => "会費・ご祝儀・お気持ち金額合計金額",
            "input.settlement_amount" => "決算金額",
            "input.card_settlement_id" => "カード決算ID",
            "guests.*.guest_list_id" => "ゲストリスト",
            "guests.*.guest_group_id" => "ゲストグループ",
            "guests.*.parent_guest_id" => "連名筆頭者",
            "guests.*.web_invitation_id" => "Web招待状",
            "guests.*.m_web_invitation_id" => "Web招待状マスタ",
            "guests.*.guest_type" => "ゲストタイプ",
            "guests.*.last_name" => "姓",
            "guests.*.first_name" => "名",
            "guests.*.last_name_kana" => "せい",
            "guests.*.first_name_kana" => "めい",
            "guests.*.last_name_romaji" => "姓(ローマ字)",
            "guests.*.first_name_romaji" => "名(ローマ字)",
            "guests.*.gender" => "性別",
            "guests.*.allergies" => "アレルギー品目",
            "guests.*.allergy" => "その他アレルギー",
            "guests.*.birthdate" => "生年月日",
            "guests.*.image_url" => "プロフィール画像",
            "guests.*.postal_code" => "郵便番号",
            "guests.*.prefecture" => "都道府県",
            "guests.*.city" => "市区町村",
            "guests.*.address" => "丁目・番地",
            "guests.*.building" => "建物名・部屋番号",
            "guests.*.phone" => "電話番号",
            "guests.*.email" => "メールアドレス",
            "guests.*.message" => "メッセージ",
            "guests.*.invitation_delivery" => "招待状お届け方法",
            "guests.*.guest_title" => "ゲスト肩書",
            "guests.*.guest_honor" => "ゲスト敬称",
            "guests.*.relationship" => "間柄",
            "guests.*.web_invite_reply_datetime" => "Web招待状返信日時",
            "guests.*.member_confirm_type" => "会員確認済種別",
            "guests.*.payment_method" => "会費・ご祝儀支払い方法",
            "guests.*.gift_amount" => "お気持ち金額",
            "guests.*.is_system_fee" => "システム利用料負担FLG",
            "guests.*.system_fee" => "システム利用料",
            "guests.*.system_fee_rate" => "システム利用料率",
            "guests.*.total_amount" => "会費・ご祝儀・お気持ち金額合計金額",
            "guests.*.settlement_amount" => "決算金額",
            "guests.*.card_settlement_id" => "カード決算ID",
            "free_item_values.*.name" => "項目名称",
            "free_item_values.*.content" => "内容",
            "guest_event_answers.*.name" => "イベント名",
            "guest_event_answers.*.date" => "イベント日付",
            "guest_event_answers.*.payment_amount" => "支払い金額",
            "guest_event_answers.*.attendance" => "出欠",
            "guest_survey_answers.*.ui_type" => "UI種類",
            "guest_survey_answers.*.answer_content" => "回答内容",
        ];
    }
}
