<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\Guest;
use App\Models\GuestFreeItemValue;
use App\Models\GuestTagGuest;
use App\Models\GuestEventAnswer;
use App\Models\GuestSurveyAnswer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

final class DeleteGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id", "is_delete_all"]);

        return DB::transaction(function () use ($credentials) {
            $memberId = Auth::user()->id;
            $isDeleteAll = $credentials["is_delete_all"];

            $guests = Guest::with("children_guests")
                ->whereIn("id", $credentials["id"])
                ->get();

            //連名者まるごと削除
            if ($isDeleteAll) {
                $guestIds = [];
                foreach ($guests as $guest) {
                    if ($guest->member_id != $memberId) {
                        throw new AuthenticationException();
                    }
                    $guestIds = array_merge(
                        $guestIds,
                        $guest->children_guests->pluck("id")->toArray()
                    );
                    $guestIds[] = $guest->id;
                }
                GuestFreeItemValue::WhereIn("guest_id", $guestIds)->delete();
                GuestEventAnswer::WhereIn("guest_id", $guestIds)->delete();
                GuestSurveyAnswer::WhereIn("guest_id", $guestIds)->delete();
                GuestTagGuest::WhereIn("guest_id", $guestIds)->delete();
                Guest::WhereIn("id", $guestIds)->delete();
            } else {
                //筆頭者のみ削除,一番目の連名者を筆頭者になるようにする
                $guestIds = [];
                foreach ($guests as $guest) {
                    if ($guest->member_id != $memberId) {
                        throw new AuthenticationException();
                    }
                    $children_guests = $guest->children_guests
                        ->pluck("id")
                        ->toArray();
                    if (!empty($children_guests)) {
                        $newParentGuestId = array_shift($children_guests);
                        Guest::where("id", $newParentGuestId)->update([
                            "parent_guest_id" => null,
                        ]);
                        Guest::where("parent_guest_id", $guest->id)->update([
                            "parent_guest_id" => $newParentGuestId,
                        ]);
                    }
                    $guestIds[] = $guest->id;
                }
                GuestFreeItemValue::WhereIn("guest_id", $guestIds)->delete();
                GuestEventAnswer::WhereIn("guest_id", $guestIds)->delete();
                GuestSurveyAnswer::WhereIn("guest_id", $guestIds)->delete();
                GuestTagGuest::WhereIn("guest_id", $guestIds)->delete();
                Guest::WhereIn("id", $guestIds)->delete();
            }

            return true;
        });
    }
}
