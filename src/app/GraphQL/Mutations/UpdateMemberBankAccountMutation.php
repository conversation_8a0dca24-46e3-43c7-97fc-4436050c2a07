<?php

namespace App\GraphQL\Mutations;

use App\Models\MemberBankAccount;
use App\Models\MoneyTransfer;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use App\Services\MemberAuthorizationService;
use Illuminate\Support\Facades\Mail;
use App\Mail\NotifyAdminAboutMemberBankAccount;

final class UpdateMemberBankAccountMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args, GraphQLContext $context)
    {
        $credentials = Arr::only($args, ["input"]);
        return DB::transaction(function () use ($credentials) {
            $memberBankAccount = MemberBankAccount::find($credentials["input"]["id"]);

            if (!$memberBankAccount) {
                throw new InValidException(
                    "口座情報が見つかりません。",
                    [
                        "error" => ["口座情報が見つかりません。"],
                    ]
                );
            }
            MemberAuthorizationService::authorize($memberBankAccount->member_id);

            $moneyTransfer = MoneyTransfer::where([
                ['member_id', Auth::id()],
                ['is_error', true]
            ])->first();

            if($moneyTransfer){
                Mail::to(env("MAIL_FROM_CONTACT_ADDRESS"))->send(
                    new NotifyAdminAboutMemberBankAccount(
                        Auth::user()
                    )
                );
            }

            $memberBankAccount->update($credentials["input"]);
            return $memberBankAccount;
        });
    }
}
