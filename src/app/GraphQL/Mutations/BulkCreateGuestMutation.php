<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestEventAnswer;
use App\Models\GuestFreeItemValue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use App\Enums\MemberConfirmTypeEnum;
use App\Services\UuidGeneratorService;

final class BulkCreateGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $guests = Arr::only($args, ["input"]);

        return DB::transaction(function () use ($guests) {
            $memberId = Auth::user()->id;
            $guestListIds = array_column(
                $guests["input"] ?? [],
                "guest_list_id"
            );
            $guestList = GuestList::whereIn("id", $guestListIds)->pluck(
                "member_id",
                "id"
            );

            $eventList = GuestList::whereIn("id", $guestListIds)
                ->with([
                    "event_lists" => function ($query) {
                        $query->select("id", "event_name");
                    },
                ])
                ->get()
                ->pluck("event_lists")
                ->flatten()
                ->pluck("event_name", "id");

            foreach ($guests["input"] ?? [] as $key => $row) {
                if (
                    !isset($guestList[$row["guest_list_id"]]) ||
                    $guestList[$row["guest_list_id"]] != $memberId
                ) {
                    throw new AuthenticationException();
                }

                //ゲスト登録
                $row["member_id"] = $memberId;
                $row["member_confirm_type"] = MemberConfirmTypeEnum::Normal;
                $guest = Guest::create($row);

                //ゲストタグゲスト登録
                $guestTagIds = array_column(
                    $row["guest_tag_guests"],
                    "guest_tag_id"
                );
                $guest->guest_tags()->sync($guestTagIds);

                //ゲストイベント登録
                $guestId = $guest->id;
                $guestEventAnswers = Arr::pull($row, "guest_event_answers");
                if (!$guestEventAnswers) {
                    $guestEventAnswers = [];
                }

                $newGuestEventAnswers = array_map(function ($item) use (
                    $memberId,
                    $guestId,
                    $eventList
                ) {
                    $item["id"] = UuidGeneratorService::generateOrderedUuid();
                    $item["member_id"] = $memberId;
                    $item["guest_id"] = $guestId;
                    $row["event_list_id"] = $eventList[$item["name"]] ?? null;
                    unset($item["date"]);
                    unset($item["name"]);
                    return $item;
                }, $guestEventAnswers);
                GuestEventAnswer::insert($newGuestEventAnswers);

                // ゲストフリー項目値
                $guestFreeItemValues = Arr::pull(
                    $row,
                    "guest_free_item_values"
                );
                if (!$guestFreeItemValues) {
                    $guestFreeItemValues = [];
                }
                foreach ($guestFreeItemValues as $key => $row) {
                    $row["id"] = UuidGeneratorService::generateOrderedUuid();
                    $row["member_id"] = $memberId;
                    $row["guest_id"] = $guestId;
                    $guestFreeItemValues[$key] = $row;
                }
                GuestFreeItemValue::upsert($guestFreeItemValues, "id");
            }

            return true;
        });
    }
}
