<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Consts\SystemConst;
use App\Models\Member;
use App\Models\FamilyProfile;
use App\Models\MemberRegistQuestionnaire;
use App\Models\WeddingInfo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Notifications\TmpCreateMemberQuestionnaireNotification;
use App\GraphQL\Exceptions\InValidException;
use App\Models\System;
use App\Services\UuidGeneratorService;
use Carbon\CarbonImmutable;

final class TmpCreateMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "input",
            "member_regist_questionnaire",
            "wedding_info",
        ]);

        return DB::transaction(function () use ($credentials) {
            $member = $credentials["input"];
            $member_regist_questionnaires =
                $credentials["member_regist_questionnaire"] ?? [];
            $wedding_info = $credentials["wedding_info"];

            $memberModel = Member::findByEmail($member["email"]);

            if ($memberModel && $memberModel->is_regist == true) {
                throw new InValidException("登録済みの会員です。", [
                    "error" => ["登録済みの会員です。"],
                ]);
            }

            //仮登録
            $tempUuid = Str::uuid()->toString();
            if ($memberModel) {
                $member["tmp_uuid"] = $tempUuid;
                $member["tmp_uuid_expires_at"] = CarbonImmutable::now()->addHours(SystemConst::MEMBER_TMP_UUID_EXPIRES_AT);
                $memberModel->update($member);
            } else {
                $memberModel = new Member();
                $memberModel->tmp_uuid = $tempUuid;
                $memberModel->tmp_uuid_expires_at = CarbonImmutable::now()->addHours(SystemConst::MEMBER_TMP_UUID_EXPIRES_AT);
                $memberModel->fill($member);
                $memberModel->save();
            }

            //家族プロフィール(あなたのプロフィール)作成
            FamilyProfile::where("member_id", $memberModel->id)->delete();
            $familyProfile = new FamilyProfile();
            $familyProfile->member_id = $memberModel->id;
            $familyProfile->first_name = $memberModel->first_name;
            $familyProfile->last_name = $memberModel->last_name;
            $familyProfile->first_name_romaji = $memberModel->first_name_romaji;
            $familyProfile->last_name_romaji = $memberModel->last_name_romaji;
            $familyProfile->birth_date = $memberModel->birthdate;
            $familyProfile->order = 1;
            $familyProfile->save();

            //アンケート登録
            MemberRegistQuestionnaire::where(
                "member_id",
                $memberModel->id
            )->delete();
            $memberRegistQuestionnaireModelInput = [];
            foreach (
                $member_regist_questionnaires
                as $member_regist_questionnaire
            ) {
                $member_regist_questionnaire[
                    "id"
                ] = UuidGeneratorService::generateOrderedUuid();
                $member_regist_questionnaire["member_id"] = $memberModel->id;
                $memberRegistQuestionnaireModelInput[] = $member_regist_questionnaire;
            }
            MemberRegistQuestionnaire::upsert(
                $memberRegistQuestionnaireModelInput,
                "id"
            );

            //結婚式情報
            WeddingInfo::where("member_id", $memberModel->id)->delete();
            $weddingInfoModel = new WeddingInfo();
            $weddingInfoModel->member_id = $memberModel->id;
            $weddingInfoModel->fill($wedding_info);
            $weddingInfoModel->save();

            //メール送信
            $memberModel->notify(
                new TmpCreateMemberQuestionnaireNotification($tempUuid)
            );

            return true;
        });
    }
}
