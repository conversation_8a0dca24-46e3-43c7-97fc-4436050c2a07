<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Arr;
use App\Models\Image;
use Illuminate\Support\Facades\DB;
use App\Services\MemberAuthorizationService;

final readonly class UpdateImageHiddenMutation
{
    /** @param  array{}  $args */
    public function __invoke(null $_, array $args)
    {
        $credentials = Arr::only($args, [
            "uuid",
        ]);

        return DB::transaction(function () use ($credentials) {
            $image = Image::findOrFail($credentials["uuid"]);
            MemberAuthorizationService::authorize($image->owner_id);
            $image->hidden_at = now();
            $image->save();

            return true;
        });
    }
}
