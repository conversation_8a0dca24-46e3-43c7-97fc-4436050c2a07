<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\FamilyProfile;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\UuidGeneratorService;
use Illuminate\Support\Facades\Auth;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

// 家族プロフィール 登録・更新
final class UpsertFamilyProfileMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["is_save", "input"]);

        // 保存フラグがfalseの場合はここで処理を終了
        if (!$credentials["is_save"]) {
            return [];
        }

        return DB::transaction(function () use ($credentials) {
            $familyProfiles = $credentials["input"] ?? [];

            //認証
            $member = Auth::user();
            if (!$member) {
                throw new AuthenticationException();
            }

            //家族プロフィール upsert
            FamilyProfile::where("member_id", $member->id)->forceDelete();
            $memberData = [];
            $familyProfileList = [];
            foreach ($familyProfiles as $row) {
                $familyProfile = [
                    "id" => UuidGeneratorService::generateOrderedUuid(),
                    "member_id" => $member->id,
                    "first_name" => $row["first_name"] ?? null,
                    "last_name" => $row["last_name"] ?? null,
                    "last_name_kana" => $row["last_name_kana"] ?? null,
                    "first_name_kana" => $row["first_name_kana"] ?? null,
                    "first_name_romaji" => $row["first_name_romaji"] ?? null,
                    "last_name_romaji" => $row["last_name_romaji"] ?? null,
                    "birth_date" => $row["birth_date"] ?? null,
                    "order" => $row["order"] ?? null,
                    "type" => $row["type"] ?? null,
                    "created_at" => now(),
                    "updated_at" => now(),
                ];
                $familyProfileList[] = $familyProfile;

                if (empty($memberData)) {
                    $memberData = [
                        "first_name" => $row["first_name"] ?? null,
                        "last_name" => $row["last_name"] ?? null,
                        "first_name_romaji" =>
                            $row["first_name_romaji"] ?? null,
                        "last_name_romaji" => $row["last_name_romaji"] ?? null,
                        "birthdate" => $row["birth_date"] ?? null,
                        "order" => $row["order"] ?? null,
                    ];
                }
            }
            FamilyProfile::upsert($familyProfileList, "id");
            if (!empty($memberData)) {
                $member->update($memberData);
            }

            return FamilyProfile::where("member_id", $member->id)->get();
        });
    }
}
