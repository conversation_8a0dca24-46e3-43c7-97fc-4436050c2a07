<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestFreeItemValue;
use App\Models\GuestTagGuest;
use App\Models\GuestEventAnswer;
use App\Models\GuestSurveyAnswer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Illuminate\Support\Facades\Auth;

//ゲストリスト削除(紐づくゲスト情報も削除)
final class DeleteGuestListMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id"]);

        return DB::transaction(function () use ($credentials) {
            $guestList = GuestList::find($credentials["id"]);

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            if ($guestList->member_id != Auth::user()->id) {
                throw new AuthenticationException();
            }

            $guestIds = Guest::where(
                "guest_list_id",
                $credentials["id"]
            )->pluck("id");

            GuestFreeItemValue::WhereIn("guest_id", $guestIds)->delete();
            GuestEventAnswer::WhereIn("guest_id", $guestIds)->delete();
            GuestSurveyAnswer::WhereIn("guest_id", $guestIds)->delete();
            GuestTagGuest::WhereIn("guest_id", $guestIds)->delete();
            Guest::where("guest_list_id", $credentials["id"])->delete();
            $guestList->delete();

            return true;
        });
    }
}
