<?php

namespace App\GraphQL\Mutations;

use Illuminate\Support\Facades\Password;
use Illuminate\Support\Arr;
use App\Models\Member;

final class SendResetPasswordUrlMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["email"]);

        $member = Member::findByEmail($credentials["email"]);

        // セキュリティ上、メアドの存在チェック機能はなくす
        // またはSNS登録されているユーザーには再設定メール送信されないように処理を終了する
        if (!$member || (!empty($member->sns_login_id) && ! $member->is_use_password)) {
            return true;
        }

        $status = Password::broker("members")->sendResetLink([
            "email" => $credentials["email"],
        ]);
        return $status == Password::RESET_LINK_SENT;
    }
}
