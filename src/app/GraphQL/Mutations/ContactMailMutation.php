<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use Illuminate\Support\Facades\Mail;
use App\Mail\ContactAdminMail;
use App\Mail\ContactMemberMail;
use Illuminate\Support\Arr;
use App\Models\Member;

//お問い合わせメール
final class ContactMailMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "first_name",
            "last_name",
            "first_name_kana",
            "last_name_kana",
            "email",
            "phone",
            "content",
        ]);

        $member = Member::where("email", $credentials["email"])->first();

        //お問い合わせ内容を管理者に送信
        Mail::to(env("MAIL_FROM_CONTACT_ADDRESS"))->send(
            new ContactAdminMail(
                $credentials["first_name"],
                $credentials["last_name"],
                $credentials["first_name_kana"],
                $credentials["last_name_kana"],
                $credentials["email"],
                $credentials["phone"],
                $credentials["content"],
                $member->alternate_member_number ?? ""
            )
        );

        //お問い合わせメール完了メール
        Mail::to($credentials["email"])->send(
            new ContactMemberMail(
                $credentials["first_name"],
                $credentials["last_name"],
                $credentials["first_name_kana"],
                $credentials["last_name_kana"],
                $credentials["email"],
                $credentials["phone"],
                $credentials["content"]
            )
        );

        return true;
    }
}
