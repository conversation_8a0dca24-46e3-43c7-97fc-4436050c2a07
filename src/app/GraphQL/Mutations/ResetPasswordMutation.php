<?php

namespace App\GraphQL\Mutations;

use Illuminate\Support\Facades\Password;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Illuminate\Support\Facades\Mail;
use App\Mail\ResetPasswordCompleteMail;
use App\Models\Member;

//パスワード再設定
final class ResetPasswordMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["token", "password"]);

        // トークンでパスワードリセットテーブルを検索する
        $passwordReset = DB::table("password_resets")
            ->where("email_token", $credentials["token"])
            ->first();

        if (!$passwordReset) {
            throw new InValidException("トークンが無効です", [
                "error" => ["トークンが無効です"],
            ]);
        }

        $member = Member::where([["email", $passwordReset->email]])->first();

        if (!$member) {
            throw new InValidException("存在しない会員です", [
                "error" => ["存在しない会員です"],
            ]);
        }

        // パスワードリセットを行う
        $response = Password::reset(
            [
                "email" => $passwordReset->email,
                "token" => $credentials["token"],
                "password" => $credentials["password"],
            ],
            function ($user, $password) {
                $user->password = $password;
                $user->save();
            }
        );

        if ($response != Password::PASSWORD_RESET) {
            throw new InValidException($response, [
                "error" => ["トークンが無効です"],
            ]);
        }

        //パスワード再設定完了メール
        Mail::to($member->email)->send(new ResetPasswordCompleteMail());

        return true;
    }
}
