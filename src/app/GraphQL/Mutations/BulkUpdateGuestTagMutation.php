<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestTag;
use App\Models\Guest;
use App\Models\GuestList;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use App\Services\MemberAuthorizationService;

//タグ一括編集用 保存API
final class BulkUpdateGuestTagMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["guest_list_id", "guest_ids", "tags"]);

        return DB::transaction(function () use ($credentials) {
            $guest_list_id = $credentials["guest_list_id"] ?? "";
            $guest_ids = $credentials["guest_ids"] ?? [];
            $tags = $credentials["tags"] ?? [];

            $guestList = GuestList::find($guest_list_id);

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            MemberAuthorizationService::authorize($guestList->member_id);

            $guests = Guest::whereIn("id", $guest_ids)->get();

            if (!$guest_ids || !$guests) {
                throw new InValidException("ゲスト情報が見つかりません。", [
                    "error" => ["ゲスト情報が見つかりません。"],
                ]);
            }

            foreach ($guests as $guest) {
                MemberAuthorizationService::authorize($guest->member_id);
            }

            //変更があればゲストとタグの紐付けを更新する
            foreach ($tags as $guest_tag) {
                if ($guest_tag["dirty"]) {
                    $tagModel = GuestTag::find($guest_tag["tag_id"]);
                    if (empty($tagModel)) {
                        $tagModel = new GuestTag();
                        $tagModel->member_id = $guestList->member_id;
                        $tagModel->guest_list_id = $guestList->id;
                        $tagModel->tag = $guest_tag["tag"];
                        $tagModel->save();
                    }

                    if ($guest_tag["selected"]) {
                        $tagModel->guests()->syncWithoutDetaching($guest_ids);
                    } else {
                        $tagModel->guests()->detach($guest_ids);
                    }
                }
            }

            return true;
        });
    }
}
