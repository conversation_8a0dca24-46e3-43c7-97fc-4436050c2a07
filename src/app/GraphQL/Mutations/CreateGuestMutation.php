<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestFreeItemValue;
use App\Models\GuestEventAnswer;
use App\Models\GuestSurveyAnswer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Illuminate\Support\Facades\Auth;
use App\Enums\MemberConfirmTypeEnum;
use App\Services\UuidGeneratorService;

//ゲスト情報新規登録
final class CreateGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "input",
            "guests",
            "free_item_values",
            "guest_event_answers",
            "guest_survey_answers",
        ]);

        return DB::transaction(function () use ($credentials) {
            $guest = $credentials["input"] ?? [];
            $guests = $credentials["guests"] ?? [];
            $freeItemValues = $credentials["free_item_values"] ?? [];
            $guestEventAnswers = $credentials["guest_event_answers"] ?? [];
            $guestSurveyAnswers = $credentials["guest_survey_answers"] ?? [];

            $guestList = GuestList::find($guest["guest_list_id"] ?? 0);

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            if ($guestList->member_id != Auth::user()->id) {
                throw new AuthenticationException();
            }

            $member_id = Auth::user()->id;

            //代表ゲスト
            $guestModel = new Guest();
            $guestModel->fill($guest);
            $guestModel->member_id = $member_id;
            $guestModel->member_confirm_type = MemberConfirmTypeEnum::Normal;
            $guestModel->save();

            //代表者のタグを登録
            $guestTagIds = array_column(
                $guest["guest_tag_guests"] ?? [],
                "guest_tag_id"
            );
            $guestModel->guest_tags()->sync($guestTagIds);

            //連名者
            foreach ($guests as $key => $row) {
                $childGuestModel = new Guest();
                $childGuestModel->fill($row);
                $childGuestModel->member_id = $guestModel->member_id;
                $childGuestModel->parent_guest_id = $guestModel->id;
                $childGuestModel->member_confirm_type =
                    MemberConfirmTypeEnum::Normal;
                $childGuestModel->save();

                $guestTagIds = array_column(
                    $row["guest_tag_guests"] ?? [],
                    "guest_tag_id"
                );
                $childGuestModel->guest_tags()->sync($guestTagIds);
            }

            //筆頭ゲストのフリー項目値
            foreach ($freeItemValues as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $guestModel->member_id;
                $row["guest_id"] = $guestModel->id;
                $freeItemValues[$key] = $row;
            }
            GuestFreeItemValue::upsert($freeItemValues, "id");

            //ゲストイベント回答
            $eventList = $guestList->event_name_uuid_list;
            foreach ($guestEventAnswers as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $guestModel->member_id;
                $row["guest_id"] = $guestModel->id;
                $row["event_list_id"] = $eventList[$row["name"]] ?? null;
                unset($row["date"]);
                unset($row["name"]);
                $guestEventAnswers[$key] = $row;
            }
            GuestEventAnswer::upsert($guestEventAnswers, "id");

            //ゲストアンケート回答
            $eventList = $guestList->event_name_uuid_list;
            foreach ($guestSurveyAnswers as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $guestModel->member_id;
                $row["guest_id"] = $guestModel->id;
                $guestSurveyAnswers[$key] = $row;
            }
            GuestSurveyAnswer::upsert($guestSurveyAnswers, "id");

            return $guestModel;
        });
    }
}
