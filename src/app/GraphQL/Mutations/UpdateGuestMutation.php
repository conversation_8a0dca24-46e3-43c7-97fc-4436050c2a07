<?php declare(strict_types=1);

namespace App\GraphQL\Mutations;

use App\Models\GuestList;
use App\Models\Guest;
use App\Models\GuestFreeItemValue;
use App\Models\GuestEventAnswer;
use App\Models\GuestSurveyAnswer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use Illuminate\Support\Facades\Auth;
use App\Enums\MemberConfirmTypeEnum;
use App\Services\UuidGeneratorService;

// ゲスト更新
final class UpdateGuestMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "input",
            "guests",
            "free_item_values",
            "guest_event_answers",
            "guest_survey_answers",
        ]);

        return DB::transaction(function () use ($credentials) {
            $guest = $credentials["input"] ?? [];
            $guests = $credentials["guests"] ?? [];
            $freeItemValues = $credentials["free_item_values"] ?? [];
            $guestEventAnswers = $credentials["guest_event_answers"] ?? [];
            $guestSurveyAnswers = $credentials["guest_survey_answers"] ?? [];

            $guestList = GuestList::with("guests")->find(
                $guest["guest_list_id"] ?? 0
            );

            if (!$guestList) {
                throw new InValidException(
                    "ゲストリスト情報が見つかりません。",
                    [
                        "error" => ["ゲストリスト情報が見つかりません。"],
                    ]
                );
            }

            if ($guestList->member_id != Auth::user()->id) {
                throw new AuthenticationException();
            }

            $member_id = Auth::user()->id;

            //代表ゲスト
            $guestModel = Guest::with(["children_guests", "member"])->find(
                $guest["id"]
            );
            if (!$guest) {
                throw new InValidException("ゲスト情報が見つかりません。", [
                    "error" => ["ゲスト情報が見つかりません。"],
                ]);
            }

            $guestModel->fill($guest);
            $guestModel->member_id = $member_id;
            $guestModel->member_confirm_type = MemberConfirmTypeEnum::Normal;
            $guestModel->update();

            //代表者のタグを登録
            $guestTagIds = array_column(
                $guest["guest_tag_guests"] ?? [],
                "guest_tag_id"
            );
            $guestModel->guest_tags()->sync($guestTagIds);

            // デザイン上、UpdateGuest の際に連名者は更新しないので、空なら無視するように
            if (count($guests)) {
                //連名者
                $updateGuestIds = [];
                foreach ($guests as $key => $row) {
                    unset($row["guest_tag_guests"]);
                    if (isset($row["id"])) {
                        $updateGuestIds[] = $row["id"];
                    } else {
                        $row[
                            "id"
                        ] = UuidGeneratorService::generateOrderedUuid();
                    }
                    $row["member_id"] = $guestModel->member_id;
                    $row["member_confirm_type"] = MemberConfirmTypeEnum::Normal;
                    $guests[$key] = $row;
                }
                Guest::upsert($guests, "id");

                //リクエストに存在しない連名者は削除
                $guestIds = array_column(
                    $guestModel->children_guests->toArray(),
                    "id"
                );
                $diffGuesteIds = array_diff($guestIds, $updateGuestIds);
                Guest::whereIn("id", $diffGuesteIds)->delete();
            }

            //筆頭ゲストのフリー項目値
            GuestFreeItemValue::where("guest_id", $guestModel->id)->delete();
            foreach ($freeItemValues as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $guestModel->member_id;
                $row["guest_id"] = $guestModel->id;
                $freeItemValues[$key] = $row;
            }
            GuestFreeItemValue::upsert($freeItemValues, "id");

            //ゲストアンケート回答
            GuestSurveyAnswer::where("guest_id", $guestModel->id)->delete();
            foreach ($guestSurveyAnswers as $key => $row) {
                $row["id"] = UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $guestModel->member_id;
                $row["guest_id"] = $guestModel->id;
                $guestSurveyAnswers[$key] = $row;
            }
            GuestSurveyAnswer::upsert($guestSurveyAnswers, "id");

            //ゲストイベント回答
            $eventList = $guestList->event_name_uuid_list;
            foreach ($guestEventAnswers as $key => $row) {
                // UUIDを生成する
                $row["id"] =
                    $row["id"] ?? UuidGeneratorService::generateOrderedUuid();
                $row["member_id"] = $guestModel->member_id;
                $row["guest_id"] = $guestModel->id;
                if (isset($row["name"])) {
                    $row["event_list_id"] = $eventList[$row["name"]] ?? null;
                }
                unset($row["date"]);
                unset($row["name"]);

                // 新規登録または更新
                $guestEventAnswer = GuestEventAnswer::find($row["id"]);
                if ($guestEventAnswer) {
                    $guestEventAnswer->update($row);
                } else {
                    $guestEventAnswer = new GuestEventAnswer($row);
                    $guestEventAnswer->save();
                }
            }
            // GuestEventAnswer::upsert($guestEventAnswers, "id");
            return $guestModel;
        });
    }
}
