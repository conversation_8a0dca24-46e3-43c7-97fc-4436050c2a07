<?php

namespace App\GraphQL\Admin\Queries;

use Illuminate\Support\Arr;
use App\Models\MoneyTransfer;
use Carbon\Carbon;

final class AdminTotalMoneyTransferAmountQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "member_number",
            "member_name",
            "status",
            "error_status",
            "from",
            "to",
            "transfer_from",
            "transfer_to",
            "account",
        ]);
        $query = MoneyTransfer::query();

        if (!empty($credentials["member_number"])) {
            $query->withMemberId($credentials["member_number"]);
        }

        if (!empty($credentials["member_name"])) {
            $query->withMemberName($credentials["member_name"]);
        }

        if (!empty($credentials["status"])) {
            $query->status($credentials["status"]);
        }

        if (!empty($credentials["error_status"])) {
            $query->errorStatus($credentials["error_status"]);
        }

        if (!empty($credentials["account"])) {
            $query->account($credentials["account"]);
        }

        if (!empty($credentials["from"])) {
            $query->where("deadline_date", ">=", $credentials["from"]);
        }

        if (!empty($credentials["to"])) {
            $query->where("deadline_date", "<=", $credentials["to"]);
        }

        if (!empty($credentials["transfer_from"])) {
            $query->where("transfer_date", ">=", Carbon::parse($credentials["transfer_from"]));
        }

        if (!empty($credentials["transfer_to"])) {
            $query->where("transfer_date", "<=", Carbon::parse($credentials["transfer_to"]));
        }

        // 金額の合計を計算
        $totalAmount = $query->sum("transfer_amount");

        return $totalAmount;
    }
}
