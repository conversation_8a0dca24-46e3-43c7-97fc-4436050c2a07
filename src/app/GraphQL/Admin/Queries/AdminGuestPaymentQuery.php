<?php

namespace App\GraphQL\Admin\Queries;

use App\Models\WebInvitation;
use App\Consts\SystemConst;
use App\Enums\MoneyTransferGuestPaymentStatusEnum;
use Illuminate\Support\Arr;
use App\Enums\PaymentMethodEnum;
use App\GraphQL\Exceptions\InValidException;
use Carbon\Carbon;

final class AdminGuestPaymentQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "id",
        ]);

        $webInvitation = WebInvitation::query()
            ->with([
                'member',
                'member.member_bank_account',
                'guests' => function ($query) {
                    $query->withTrashed()
                        ->where([
                            ["payment_method", PaymentMethodEnum::ADVANCE_PAYMENT],
                            ["settlement_amount", ">", 0]
                        ]);
                },
                'money_transfer'
            ])
            ->find($credentials["id"]);

        if (!$webInvitation) {
            throw new InValidException("送金情報が見つかりません。", [
                "error" => ["送金情報が見つかりません。"],
            ]);
        }

        if (!empty($webInvitation->money_transfer)) {

            //ゲストシステム利用料
            $guest_system_fee = $webInvitation->money_transfer->guest_system_fee;

            //システム利用料
            $system_fee = is_null($guest_system_fee) ? null : $webInvitation->money_transfer->guest_system_fee + $webInvitation->money_transfer->system_fee;

            //会員システム利用料
            $member_system_fee = $webInvitation->money_transfer->system_fee;

            return [
                'id' => $webInvitation->id,
                'member_id' => $webInvitation->member->id,
                'member_number' => $webInvitation->member->number,
                'alternate_member_number' => $webInvitation->member->alternate_member_number,
                'member_name' => $webInvitation->member->last_name . " " . $webInvitation->member->first_name,
                'email' => $webInvitation->member->email,
                'phone' => optional($webInvitation->member->member_bank_account)->phone,
                'name' => $webInvitation->name,
                'date' => Carbon::parse($webInvitation->money_transfer->transfer_date)->format('Y-m-d'),
                'completion_datetime' => $webInvitation->money_transfer->completion_datetime,
                'deadline_date' => $webInvitation->money_transfer->deadline_date,
                'total_amount' => $webInvitation->money_transfer->prepayment_amount,
                'system_fee' => $system_fee,
                'guest_system_fee' => $guest_system_fee,
                'member_system_fee' => $member_system_fee,
                'member_settlement_amount' => $webInvitation->money_transfer->transfer_amount,
                'commission_fee' => $webInvitation->money_transfer->commission_fee,
                'bank_code' => $webInvitation->money_transfer->bank_code,
                'bank_name' => $webInvitation->money_transfer->bank_name,
                'branch_code' => $webInvitation->money_transfer->branch_code,
                'branch_name' => $webInvitation->money_transfer->branch_name,
                'account_type' => $webInvitation->money_transfer->account_type,
                'account_name' => $webInvitation->money_transfer->account_name,
                'account_number' => $webInvitation->money_transfer->account_number,
                'status' => $webInvitation->money_transfer->status,
            ];
        } else {

            //会費・ご祝儀
            $total_amount = $webInvitation->guests->sum('total_amount');

            //ゲストシステム利用料
            $guest_system_fee = $webInvitation->guests->sum('system_fee');

            //会員システム利用料
            $system_guest_total_fee = collect($webInvitation->guests)->sum(fn($guest) => $guest->system_fee ?? 0);
            $total_advance_payment = collect($webInvitation->guests)->sum(fn($guest) => floor($guest->total_amount * SystemConst::SYSTEM_FEE_RATE));
            $member_system_fee = floor($total_advance_payment - $system_guest_total_fee);

            //システム利用料
            $system_fee = $guest_system_fee + $member_system_fee;

            return [
                'id' => $webInvitation->id,
                'member_id' => $webInvitation->member->id,
                'member_number' => $webInvitation->member->number,
                'alternate_member_number' => $webInvitation->member->alternate_member_number,
                'member_name' => $webInvitation->member->last_name . " " . $webInvitation->member->first_name,
                'email' => $webInvitation->member->email,
                'phone' => optional($webInvitation->member->member_bank_account)->phone,
                'name' => $webInvitation->name,
                'date' => $webInvitation->transfer_date,
                'completion_datetime' => null,
                'deadline_date' => $webInvitation->scheduled_transfer_date,
                'total_amount' => $webInvitation->guests->sum('total_amount'),
                'system_fee' => $system_fee,
                'guest_system_fee' => $guest_system_fee,
                'member_system_fee' => $member_system_fee,
                'member_settlement_amount' => $total_amount - $member_system_fee - SystemConst::TRANSFER_FEE,
                'commission_fee' => SystemConst::TRANSFER_FEE,
                'bank_code' => optional($webInvitation->member->member_bank_account)->bank_code ?? '',
                'bank_name' => optional($webInvitation->member->member_bank_account)->bank_name ?? '',
                'branch_code' => optional($webInvitation->member->member_bank_account)->branch_code ?? '',
                'branch_name' => optional($webInvitation->member->member_bank_account)->branch_name ?? '',
                'account_type' => optional($webInvitation->member->member_bank_account)->account_type,
                'account_name' => optional($webInvitation->member->member_bank_account)->account_name,
                'account_number' => optional($webInvitation->member->member_bank_account)->account_number,
                'status' => MoneyTransferGuestPaymentStatusEnum::UNCONFIRMED,
            ];
        }
    }
}
