<?php

namespace App\GraphQL\Admin\Queries;

use Illuminate\Support\Arr;
use App\Models\WebInvitation;
use App\Consts\SystemConst;
use Illuminate\Support\Facades\DB;
use App\Enums\PaymentMethodEnum;
use App\Enums\MoneyTransferGuestPaymentStatusEnum;
use App\Enums\RegistrationEnum;
use Illuminate\Support\Str;

final class AdminTotalGuestPaymentAmountQuery
{
        /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "member_number",
            "member_name",
            "status",
            "error_status",
            "from_date",
            "to_date",
            "dead_line_from_date",
            "dead_line_to_date",
            "account",
            "first",
            "page"
        ]);

        $where = [];
        if(!empty($credentials['from_date'])) {
            $where[] = ['transfer_date', '>=', $credentials['from_date']];
        }
        if(!empty($credentials['to_date'])) {
            $where[] = ['transfer_date', '<=', $credentials['to_date']];
        }

        if(!empty($credentials['dead_line_from_date'])) {
            $where[] = ['scheduled_transfer_date', '>=', $credentials['dead_line_from_date']];
        }

        if(!empty($credentials['dead_line_to_date'])) {
            $where[] = ['scheduled_transfer_date', '<=', $credentials['dead_line_to_date']];
        }

        $query = WebInvitation::query()
            ->where($where)
            ->withWhereHas('member', function ($query) use ($credentials) {
                if(!empty($credentials['member_number'])){
                    $query->where(
                        DB::raw('LPAD(member_number, 8, "0")'),
                        "LIKE",
                        "%{$credentials['member_number']}%"
                    )->orWhere(
                        'alternate_member_number',
                        "LIKE",
                        "%{$credentials['member_number']}%"
                    );
                }
                if(!empty($credentials['member_name'])){
                    $searchName = Str::of($credentials['member_name'])
                        ->replaceMatches("/[\s\x{3000}]+/u", "")
                        ->toString();
                    $query->where(
                        DB::raw("concat(last_name, '', first_name)"),
                        "LIKE",
                        "%{$searchName}%"
                    );
                }
            })
            ->withWhereHas('guests', function($query){
                $query
                ->withTrashed()
                ->where([
                    ["payment_method", PaymentMethodEnum::ADVANCE_PAYMENT],
                    ["settlement_amount", ">", 0]
                ]);
            })
            ->where(function ($query) use ($credentials) {
                if (!empty($credentials['status'])) {
                    // 各オブジェクトから `value` を取得
                    $statusValues = array_map(fn($status) => $status->value, $credentials['status']);

                    // 未確定のステータスが含まれる場合
                    if (in_array(MoneyTransferGuestPaymentStatusEnum::UNCONFIRMED, $statusValues)) {
                        $query->whereDoesntHave('money_transfer'); // money_transfer が存在しないレコードを取得
                    }

                    // 未確定以外のステータスが含まれる場合
                    $filteredStatus = array_diff($statusValues, [MoneyTransferGuestPaymentStatusEnum::UNCONFIRMED]);

                    if (!empty($filteredStatus)) {
                        $query->orWhereHas('money_transfer', function ($subQuery) use ($filteredStatus) {
                            $subQuery->whereIn('status', $filteredStatus);
                        });
                    }
                }
            });

        // アカウントの状態による分岐
        if (!empty($credentials['account']) && $credentials['account'] instanceof RegistrationEnum) {
            $query->when($credentials['account']->value === RegistrationEnum::Unregistered, function ($query) {
                $query->where(function ($query) {
                    $query->whereDoesntHave('member.member_bank_account')
                        ->orWhereHas('member.member_bank_account', function ($query) {
                            $query->onlyTrashed();
                        });
                });
            })->when($credentials['account']->value === RegistrationEnum::Registered, function ($query) {
                $query->whereHas('member.member_bank_account', function ($query) {
                    $query->whereNull('deleted_at');
                });
            });
        }

        // Eagerロードは最後に行う
        $query->with(['member.member_bank_account' => function ($query) use ($credentials) {
            if (!empty($credentials['account']) &&
                $credentials['account']->value === RegistrationEnum::Unregistered) {
                $query->withTrashed();
            }
        }]);

        $webInvitations = $query->get();

        $total_amount = 0;
        foreach($webInvitations as $row){

            if(!empty($row->money_transfer)){

                //送金金額
                $total_amount += $row->money_transfer->transfer_amount;

            }else {

                //会員システム利用料
                $member_system_fee = $row->guests->sum(function ($guest) {
                    return !$guest->is_system_fee ? floor($guest->total_amount * SystemConst::SYSTEM_FEE_RATE) : 0;
                });

                //送金金額
                $total_amount += $row->guests->sum('settlement_amount') - $member_system_fee - SystemConst::TRANSFER_FEE;
            }
        }

        return $total_amount;
    }
}
