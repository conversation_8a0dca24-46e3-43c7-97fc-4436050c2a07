<?php

namespace App\GraphQL\Admin\Queries;

use Illuminate\Support\Arr;
use App\Models\Product;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
final class AdminProductWebInvitationsQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "first",
            "page",
            "id",
            "name",
            "sales_period_start",
            "sales_period_end",
        ]);
        $query = Product::query()
            ->with([
                "tags",
                "m_specification_products",
                "m_specification_products.product_images",
                "m_specification_products.product_images.image",
                "m_specification_products.m_web_invitations",
                "m_specification_products.m_web_invitations.web_invitation_design_images",
            ])
            ->withoutGlobalScopes(["period", "unpublished"]);

        if (isset($credentials["id"])) {
            $query->where("id", $credentials["id"]);
        }
        if (isset($credentials["name"])) {
            $query->where("name", "like", "%" . $credentials["name"] . "%");
        }
        if (isset($credentials["sales_period_start"])) {
            $query->where(
                "sales_period_end",
                ">=",
                $credentials["sales_period_start"]
            );
        }
        if (isset($credentials["sales_period_end"])) {
            $query->where(
                "sales_period_start",
                "<=",
                $credentials["sales_period_end"]
            );
        }

        $perPage = $credentials["first"] ?? 50;
        $currentPage = $credentials["page"] ?? 1;
        // 総件数を取得
        $total = $query->count();
        $products = $query
            ->skip(($currentPage - 1) * $perPage)
            ->take($perPage)
            ->get();

        $storage = Storage::disk("s3");
        $config = $storage->getConfig();

        if ($config["use_path_style_endpoint"] ?? false) {
            $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
            $storage = Storage::build(Arr::set($config, "endpoint", $endpoint));
        }

        $items = [];
        foreach ($products ?? [] as $product) {
            $imageUrl = [];

            if ($product && !empty($product->m_specification_products)) {
                $firstSpecProduct =
                    $product->m_specification_products[0] ?? null;

                if ($firstSpecProduct) {
                    $product_images = $firstSpecProduct->product_images;
                    foreach ($product_images as $product_image) {
                        $imageUrl[] = [
                            "presigned_url" => $this->getImageUrl(
                                $product_image->image
                            ),
                        ];
                    }
                }
            }
            $items[] = [
                "id" => $product->id,
                "name" => $product->name,
                "images" => $imageUrl,
                "display_order" => $product->display_order,
            ];
        }
        // return $query->paginate(
        //     $credentials["first"],
        //     ["*"],
        //     "page",
        //     $credentials["page"] ?? 1
        // );
        return new LengthAwarePaginator(
            $items, // 現在のページのデータ
            $total, // 総件数
            $perPage, // 1ページあたりの件数
            $currentPage, // 現在のページ番号
            ["path" => LengthAwarePaginator::resolveCurrentPath()]
        );
    }

    private function getImageUrl($image)
    {
        if (isset($image->name) && isset($image->extension_type)) {
            $filePath =
                env("AWS_DIR_ADMIN") .
                "{$image->name}_s.{$image->extension_type}";
            if (Storage::disk("s3")->exists($filePath)) {
                return Storage::disk('s3')->url($filePath);
            }
        }
        return "";
    }
}
