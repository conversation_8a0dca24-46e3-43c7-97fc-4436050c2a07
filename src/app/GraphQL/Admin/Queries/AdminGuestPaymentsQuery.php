<?php

namespace App\GraphQL\Admin\Queries;

use Illuminate\Support\Arr;
use App\Models\WebInvitation;
use Carbon\Carbon;
use App\Consts\SystemConst;
use Illuminate\Support\Facades\DB;
use App\Enums\PaymentMethodEnum;
use App\Enums\MoneyTransferGuestPaymentStatusEnum;
use App\Enums\RegistrationEnum;
use Illuminate\Support\Str;
use Illuminate\Pagination\LengthAwarePaginator;

final class AdminGuestPaymentsQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "member_number",
            "member_name",
            "status",
            "error_status",
            "from_date",
            "to_date",
            "dead_line_from_date",
            "dead_line_to_date",
            "account",
            "first",
            "page"
        ]);

        $perPage = $credentials["first"] ?? 50;
        $currentPage = $credentials["page"] ?? 1;

        $where = [];
        if(!empty($credentials['from_date'])) {
            $where[] = ['transfer_date', '>=', $credentials['from_date']];
        }
        if(!empty($credentials['to_date'])) {
            $where[] = ['transfer_date', '<=', $credentials['to_date']];
        }

        if(!empty($credentials['dead_line_from_date'])) {
            $where[] = ['scheduled_transfer_date', '>=', $credentials['dead_line_from_date']];
        }

        if(!empty($credentials['dead_line_to_date'])) {
            $where[] = ['scheduled_transfer_date', '<=', $credentials['dead_line_to_date']];
        }

        $query = WebInvitation::query();
        $query->where($where)
            ->withWhereHas('member', function ($query) use ($credentials) {
                if(!empty($credentials['member_number'])){
                    $query->where(
                        DB::raw('LPAD(member_number, 8, "0")'),
                        "LIKE",
                        "%{$credentials['member_number']}%"
                    )->orWhere(
                        'alternate_member_number',
                        "LIKE",
                        "%{$credentials['member_number']}%"
                    );
                }
                if(!empty($credentials['member_name'])){
                    $searchName = Str::of($credentials['member_name'])
                        ->replaceMatches("/[\s\x{3000}]+/u", "")
                        ->toString();
                    $query->where(
                        DB::raw("concat(last_name, '', first_name)"),
                        "LIKE",
                        "%{$searchName}%"
                    );
                }
            })
            ->withWhereHas('guests', function($query){
                $query
                ->withTrashed()
                ->where([
                    ["payment_method", PaymentMethodEnum::ADVANCE_PAYMENT],
                    ["settlement_amount", ">", 0]
                ]);
            })
            ->where(function ($query) use ($credentials) {
                if (!empty($credentials['status'])) {
                    // 各オブジェクトから `value` を取得
                    $statusValues = array_map(fn($status) => $status->value, $credentials['status']);

                    // 未確定のステータスが含まれる場合
                    if (in_array(MoneyTransferGuestPaymentStatusEnum::UNCONFIRMED, $statusValues)) {
                        $query->whereDoesntHave('money_transfer'); // money_transfer が存在しないレコードを取得
                    }

                    // 未確定以外のステータスが含まれる場合
                    $filteredStatus = array_diff($statusValues, [MoneyTransferGuestPaymentStatusEnum::UNCONFIRMED]);

                    if (!empty($filteredStatus)) {
                        $query->orWhereHas('money_transfer', function ($subQuery) use ($filteredStatus) {
                            $subQuery->whereIn('status', $filteredStatus);
                        });
                    }
                }
            });
        // アカウントの状態による分岐
        if (!empty($credentials['account']) && $credentials['account'] instanceof RegistrationEnum) {
            $query->when($credentials['account']->value === RegistrationEnum::Unregistered, function ($query) {
                $query->where(function ($query) {
                    $query->whereDoesntHave('member.member_bank_account')
                        ->orWhereHas('member.member_bank_account', function ($query) {
                            $query->onlyTrashed();
                        });
                });
            })->when($credentials['account']->value === RegistrationEnum::Registered, function ($query) {
                $query->whereHas('member.member_bank_account', function ($query) {
                    $query->whereNull('deleted_at');
                });
            });
        }

        // Eagerロードは最後に行う
        $query->with(['member.member_bank_account' => function ($query) use ($credentials) {
            if (!empty($credentials['account']) &&
                $credentials['account']->value === RegistrationEnum::Unregistered) {
                $query->withTrashed();
            }
        }]);


        // 総件数を取得
        $total = $query->count();
        $webInvitations = $query
            ->skip(($currentPage - 1) * $perPage)
            ->take($perPage)
            ->get();

        $items = [];
        foreach($webInvitations as $row){

            if(!empty($row->money_transfer)){

                //会費・ご祝儀
                $total_amount = $row->money_transfer->prepayment_amount;

                //ゲストシステム利用料
                $guest_system_fee = $row->money_transfer->guest_system_fee;

                //システム利用料
                $system_fee = is_null($guest_system_fee) ? null : $row->money_transfer->guest_system_fee + $row->money_transfer->system_fee;

                //会員システム利用料
                $member_system_fee = $row->money_transfer->system_fee;

                //手数料
                $commission_fee = $row->money_transfer->commission_fee;

                //送金日
                $date = Carbon::parse($row->money_transfer->transfer_date)->format('Y-m-d');

                //送金完了日
                $completion_datetime = $row->money_transfer->completion_datetime;

                //送金期限
                $deadline_date = $row->money_transfer->deadline_date;

                //送金金額
                $transfer_amount = $row->money_transfer->transfer_amount;

                //ステータス
                $status = $row->money_transfer->status;

                //銀行コード
                $bank_code = $row->money_transfer->bank_code;

                //銀行名
                $bank_name = $row->money_transfer->bank_name;

                //支店コード
                $branch_code = $row->money_transfer->branch_code;

                //支店名
                $branch_name = $row->money_transfer->branch_name;

                //口座種別
                $account_type = $row->money_transfer->account_type;

                //口座名義
                $account_name = $row->money_transfer->account_name;

                //口座番号
                $account_number = $row->money_transfer->account_number;

            }else {

                //会費・ご祝儀
                $total_amount = $row->guests->sum('total_amount');

                //ゲストシステム利用料
                $guest_system_fee = $row->guests->sum('system_fee');

                //会員システム利用料
                $system_guest_total_fee = collect($row->guests)->sum(fn($guest) => $guest->system_fee ?? 0);
                $total_advance_payment = collect($row->guests)->sum(fn($guest) => floor($guest->total_amount * SystemConst::SYSTEM_FEE_RATE));
                $member_system_fee = floor($total_advance_payment - $system_guest_total_fee);

                //システム利用料
                $system_fee = $guest_system_fee + $member_system_fee;

                //手数料
                $commission_fee = SystemConst::TRANSFER_FEE;

                //送金日
                $date = $row->transfer_date;

                //送金完了日
                $completion_datetime = null;

                //送金期限
                $deadline_date = $row->scheduled_transfer_date;

                //送金金額
                $transfer_amount = max($total_amount - $member_system_fee - SystemConst::TRANSFER_FEE, 0);

                //ステータス
                $status = MoneyTransferGuestPaymentStatusEnum::UNCONFIRMED;

                //銀行コード
                $bank_code = optional($row->member->member_bank_account)->bank_code;

                //銀行名
                $bank_name = optional($row->member->member_bank_account)->bank_name;

                //支店コード
                $branch_code = optional($row->member->member_bank_account)->branch_code;

                //支店名
                $branch_name = optional($row->member->member_bank_account)->branch_name;

                //口座種別
                $account_type = optional($row->member->member_bank_account)->account_type;

                //口座名義
                $account_name = optional($row->member->member_bank_account)->account_name;

                //口座番号
                $account_number = optional($row->member->member_bank_account)->account_number;
            }

            $prepaymentDueDate = Carbon::parse($row->scheduled_transfer_date)->subDays(SystemConst::ADVANCE_PAYMENT_CLOSING_DAY);

            $items[] = [
                'id' => $row->id,
                'member_id' => $row->member->id,
                'member_number' => $row->member->number,
                'alternate_member_number' => $row->member->alternate_member_number,
                'member_name' => $row->member->last_name." ".$row->member->first_name,
                'email' => $row->member->email,
                'phone' => $row->phone,
                'name' => $row->name,
                'date' => $date,
                'completion_datetime' => $completion_datetime,
                'deadline_date' => $deadline_date,
                'prepayment_due_date' => $prepaymentDueDate->toDateString(),
                'total_amount' => $total_amount,
                'system_fee' => $system_fee,
                'guest_system_fee' => $guest_system_fee,
                'member_system_fee' => $member_system_fee,
                'member_settlement_amount' => $transfer_amount,
                'commission_fee' => $commission_fee,
                'bank_code' => $bank_code,
                'bank_name' => $bank_name,
                'branch_code' => $branch_code,
                'branch_name' => $branch_name,
                'account_type' => $account_type,
                'account_name' => $account_name,
                'account_number' => $account_number,
                'status' => $status
            ];
        }

        return new LengthAwarePaginator(
            $items, // 現在のページのデータ
            $total, // 総件数
            $perPage, // 1ページあたりの件数
            $currentPage, // 現在のページ番号
            ["path" => LengthAwarePaginator::resolveCurrentPath()]
        );
    }
}
