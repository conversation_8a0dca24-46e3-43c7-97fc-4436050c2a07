<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\MoneyTransfer;
use App\Models\Member;
use App\Enums\MoneyTransferStatusEnum;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use App\Mail\AdvancePaymentCompleteMail;
use Illuminate\Support\Facades\Mail;

final class AdminUpdateMoneyTransferMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $money_transfer = Arr::only($args, ["id", "status", "admin_id"]);

        return DB::transaction(function () use ($money_transfer) {
            $moneyTransferModel = MoneyTransfer::find($money_transfer["id"]);

            if (!$moneyTransferModel) {
                throw new InValidException("送金情報が見つかりません。", [
                    "error" => ["送金情報が見つかりません。"],
                ]);
            }

            if (
                !empty($money_transfer["status"]) &&
                property_exists($money_transfer["status"], "value")
            ) {
                $member = Member::withoutGlobalScopes()
                    ->with("member_bank_account")
                    ->find($moneyTransferModel->member_id);

                if (!$member) {
                    throw new InValidException("会員情報が見つかりません。", [
                        "error" => ["会員情報が見つかりません。"],
                    ]);
                }

                $money_transfer["status"] = $money_transfer["status"]->value;

                //送金予約に変更時は銀行口座情報を上書きする
                if (
                    $moneyTransferModel->status ==
                        MoneyTransferStatusEnum::NOT_TRANSFERRED &&
                    ($money_transfer["status"] ==
                        MoneyTransferStatusEnum::RESERVATION ||
                        $money_transfer["status"] ==
                            MoneyTransferStatusEnum::COMPLETED)
                ) {
                    $money_transfer["bank_code"] =
                        $member->member_bank_account->bank_code ?? null;
                    $money_transfer["bank_name"] =
                        $member->member_bank_account->bank_name ?? null;
                    $money_transfer["branch_code"] =
                        $member->member_bank_account->branch_code ?? null;
                    $money_transfer["branch_name"] =
                        $member->member_bank_account->branch_name ?? null;
                    $money_transfer["account_type"] =
                        $member->member_bank_account->account_type ?? null;
                    $money_transfer["account_name"] =
                        $member->member_bank_account->account_name ?? null;
                    $money_transfer["account_number"] =
                        $member->member_bank_account->account_number ?? null;
                }

                //送金完了の場合にエラーフラグはオフにする
                if (
                    $money_transfer["status"] ==
                            MoneyTransferStatusEnum::COMPLETED
                ){
                    $money_transfer["is_error"] = false;
                    $money_transfer["completion_datetime"] = now();
                }

                //送金完了時は会員にメール送信
                if (
                    $money_transfer["status"] ==
                    MoneyTransferStatusEnum::COMPLETED
                ) {
                    Mail::to($member->email)->send(
                        new AdvancePaymentCompleteMail(
                            $member->last_name,
                            $member->first_name
                        )
                    );
                }
            }
            $moneyTransferModel->update($money_transfer);

            return $moneyTransferModel;
        });
    }
}
