<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Product;
use App\Models\MSpecificationProduct;
use App\Models\ProductImage;
use App\Models\MWebInvitation;
use App\Models\WebInvitationDesignImage;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\FileS3Service;
use App\Services\FileService;
use App\Enums\FileType;
use Carbon\Carbon;
use App\Services\UuidGeneratorService;
use Illuminate\Support\Facades\Storage;

final class AdminUpdateProductWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "product",
            "m_specification_products",
            "product_tags",
        ]);

        return DB::transaction(function () use ($credentials) {
            //商品を更新
            $productId = $credentials["product"]["id"] ?? 0;
            $product = Product::withoutGlobalScopes()->findOrFail($productId);
            $product->update($credentials["product"]);

            if (isset($credentials["product_tags"])) {
                // 商品タグを登録
                $product->tags()->detach();

                // detach()メソッドが呼び出された後に関連付けられたタグが削除されているか確認
                if ($product->tags->isEmpty()) {
                    if (!empty($credentials["product_tags"])) {
                        $tagIds = array_column(
                            $credentials["product_tags"],
                            "tag_id"
                        );
                        $product->tags()->attach($tagIds);
                    }
                }
            }

            $fileS3Service = new FileS3Service();
            $fileService = new FileService();
            $now = Carbon::now();

            foreach (
                $credentials["m_specification_products"] ?? []
                as $m_specification_product
            ) {
                $m_web_invitation = Arr::pull(
                    $m_specification_product,
                    "m_web_invitation"
                );

                if (isset($m_web_invitation["image_aspect_settings_json"])) {
                    // JSON 文字列を連想配列にデコード
                    $m_web_invitation[
                        "image_aspect_settings_json"
                    ] = json_decode(
                        $m_web_invitation["image_aspect_settings_json"],
                        true
                    );
                }

                if (isset($m_specification_product["id"])) {
                    //規格別商品・Web招待状更新
                    $mSpecificationProduct = MSpecificationProduct::find(
                        $m_specification_product["id"]
                    );
                    $mSpecificationProduct->update($m_specification_product);

                    $mWebInvitation = MWebInvitation::find(
                        $m_web_invitation["id"]
                    );
                    $mWebInvitation->update($m_web_invitation);
                } else {
                    //規格別商品・Web招待状登録
                    $m_specification_product["product_id"] = $productId;
                    $mSpecificationProduct = MSpecificationProduct::create(
                        $m_specification_product
                    );

                    $m_web_invitation["m_specification_product_id"] =
                        $mSpecificationProduct->id;
                    $mWebInvitation = MWebInvitation::create($m_web_invitation);
                }

                $mSpecificationProductId = $mSpecificationProduct->id;
                $mWebInvitationId = $mWebInvitation->id;

                // 規格別商品画像の作成と更新
                if (
                    array_key_exists("product_images", $m_specification_product)
                ) {
                    $product_images = Arr::pull(
                        $m_specification_product,
                        "product_images"
                    );

                    $productImageIds = ProductImage::where(
                        "m_specification_product_id",
                        $mSpecificationProductId
                    )
                        ->pluck("id")
                        ->toArray();

                    $productImageInput = [];
                    foreach ($product_images ?? [] as $product_image) {
                        // 一致するIDがあれば、そのIDを$productImageIdsから削除する
                        if (isset($product_image["id"])) {
                            $index = array_search(
                                $product_image["id"],
                                $productImageIds
                            );
                            if ($index !== false) {
                                unset($productImageIds[$index]);
                            }
                            continue;
                        }

                        $fileData = $product_image["file"];

                        //画像保存処理
                        $fileS3Service->setFileData($fileData);
                        $fileName = $fileS3Service->getFileName();

                        //画像テーブルに保存
                        $image = $fileS3Service->imageSave(
                            $fileName,
                            FileType::FILE_TYPE_PRODUCT_MASTER
                        );

                        //商品規格別商品
                        unset($product_image["file"]);

                        $product_image[
                            "m_specification_product_id"
                        ] = $mSpecificationProductId;
                        $product_image[
                            "id"
                        ] = UuidGeneratorService::generateOrderedUuid();
                        $product_image["uuid"] = $image->uuid;
                        $product_image["created_at"] = $now;
                        $product_image["updated_at"] = $now;
                        $productImageInput[] = $product_image;

                        //サイズと長辺の長さ取得
                        $sizes = FileS3Service::SIZE_LIST;

                        //リサイズとファイルをS3に保存
                        foreach ($sizes as $size => $length) {
                            //画像リサイズと圧縮
                            $fileCompression = $fileS3Service->imageCompression(
                                $fileData,
                                $length
                            );

                            // ファイル名_サイズで保存
                            $newFileName = "{$fileName}_{$size}";

                            //ファイルをS3に保存
                            $fileS3Service->fileMove(
                                $fileCompression,
                                $newFileName
                            );
                        }
                    }

                    //$product_imagesに存在しないIDはデータと画像共に削除
                    if (!empty($productImageIds)) {
                        $productImage = ProductImage::whereIn(
                            "id",
                            $productImageIds
                        );
                        $productImageUuids = $productImage
                            ->pluck("uuid")
                            ->toArray();
                        $fileS3Service->deleteImages($productImageUuids);
                        $productImage->delete();
                    }
                    ProductImage::insert($productImageInput);
                }

                // Web招待状デザイン画像の作成と更新
                if (
                    array_key_exists(
                        "web_invitation_design_images",
                        $m_web_invitation
                    )
                ) {
                    $web_invitation_design_images = Arr::pull(
                        $m_web_invitation,
                        "web_invitation_design_images"
                    );

                    //Web招待状デザイン画像のパス
                    $filepath = env('AWS_DIR_WI'). "/" . $productId;

                    //現在登録されているデザイン画像IDの配列
                    $WebInvitationDesignImageIds = WebInvitationDesignImage::where(
                        "m_web_invitation_id",
                        $mWebInvitationId
                    )
                        ->pluck("id")
                        ->toArray();

                    //現在登録されているデザイン画像 ID => ファイル名
                    $WebInvitationDesignImageFileNames = WebInvitationDesignImage::whereIn(
                        "id",
                        $WebInvitationDesignImageIds
                    )
                        ->pluck("id", "file_name")
                        ->toArray();

                    $new_web_invitation_design_images = [];
                    foreach (
                        $web_invitation_design_images ?? []
                        as $web_invitation_design_image
                    ) {
                        // 一致するIDがあれば、そのIDを$WebInvitationDesignImageIdsから削除する
                        if (isset($web_invitation_design_image["id"])) {
                            $index = array_search(
                                $web_invitation_design_image["id"],
                                $WebInvitationDesignImageIds
                            );
                            if ($index !== false) {
                                unset($WebInvitationDesignImageIds[$index]);
                            }
                            continue;
                        }

                        //新規登録ファイルと既存ファイルでファイル名バッティングするなら既存のファイルIDを$WebInvitationDesignImageIdsに代入
                        if (
                            isset(
                                $WebInvitationDesignImageFileNames[
                                    $web_invitation_design_image["file_name"]
                                ]
                            )
                        ) {
                            $WebInvitationDesignImageIds[] =
                                $WebInvitationDesignImageFileNames[
                                    $web_invitation_design_image["file_name"]
                                ];
                        }
                        $new_web_invitation_design_images[] = $web_invitation_design_image;
                    }

                    // Web招待状デザイン削除処理 $WebInvitationDesignImageIdsにあるIDはデータと画像共に削除
                    if (!empty($WebInvitationDesignImageIds)) {
                        $fileNames = WebInvitationDesignImage::whereIn(
                            "id",
                            $WebInvitationDesignImageIds
                        )
                            ->pluck("file_name", "id")
                            ->toArray();

                        $deleteWebInvitationDesignImages = [];
                        foreach ($fileNames as $id => $fileName) {
                            $deleteWebInvitationDesignImages[] =
                                $filepath . "/" . $fileName;
                        }

                        $fileService->deleteImages(
                            $deleteWebInvitationDesignImages
                        );

                        WebInvitationDesignImage::whereIn(
                            "id",
                            $WebInvitationDesignImageIds
                        )->delete();
                    }

                    //Web招待状デザイン登録処理
                    $webInvitationDesignImageInput = []; //加工して登録するための配列
                    foreach (
                        $new_web_invitation_design_images
                        as $web_invitation_design_image
                    ) {
                        //画像保存処理
                        $file = $web_invitation_design_image["file"];
                        Storage::disk("s3_public")->putFileAs(
                            $filepath,
                            $file,
                            $file->getClientOriginalName(),
                        );

                        unset($web_invitation_design_image["file"]);

                        $web_invitation_design_image[
                            "m_web_invitation_id"
                        ] = $mWebInvitationId;
                        $web_invitation_design_image[
                            "id"
                        ] = UuidGeneratorService::generateOrderedUuid();
                        $web_invitation_design_image["created_at"] = $now;
                        $web_invitation_design_image["updated_at"] = $now;
                        $webInvitationDesignImageInput[] = $web_invitation_design_image;
                    }

                    WebInvitationDesignImage::insert(
                        $webInvitationDesignImageInput
                    );
                }
            }

            return $product;
        });
    }
}
