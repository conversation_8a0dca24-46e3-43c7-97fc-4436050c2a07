<?php

declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\MoneyTransfer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Enums\MoneyTransferStatusEnum;
use App\GraphQL\Exceptions\InValidException;

final class AdminUpdateMultipleDateMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["ids", "date"]);
        $ids = $credentials["ids"];
        $date = $credentials["date"];

        return DB::transaction(function () use ($ids, $date) {
            $records = MoneyTransfer::whereIn("id", $ids)->get();

            // データが見つからない場合、例外エラー
            if ($records->isEmpty()) {
                throw new InValidException("変更対象が選択されていない、またはデータがありません。", [
                    "error" => ["変更対象が選択されていない、またはデータがありません。"],
                ]);
            }

            // ステータスをチェックし、送金予約の場合は例外エラー
            foreach ($records as $record) {
                if ($record->status == MoneyTransferStatusEnum::RESERVATION) {
                    throw new InValidException("送金予約ステータスは送金日を変更できません。", [
                        "error" => ["送金予約ステータスは送金日を変更できません。"],
                    ]);
                }
            }

            MoneyTransfer::whereIn("id", $ids)->update(["transfer_date" => $date]);

            return true;
        });
    }
}
