<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Product;
use App\Models\MSpecificationProduct;
use App\Models\ProductImage;
use App\Models\MWebInvitation;
use App\Models\WebInvitationDesignImage;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

final class AdminDeleteProductWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id"]);

        return DB::transaction(function () use ($credentials) {
            //規格別商品画像を取得
            $productId = $credentials["id"];
            $product = Product::withoutGlobalScopes()
                ->with([
                    "m_specification_products",
                    "m_specification_products.product_images",
                    "m_specification_products.m_web_invitations",
                    "m_specification_products.m_web_invitations.web_invitation_design_images",
                ])
                ->findOrFail($productId);

            // 規格別商品を削除
            foreach (
                $product->m_specification_products
                as $specificationProduct
            ) {
                // 規格別商品画像削除
                if ($specificationProduct->product_images) {
                    $specificationProduct->product_images->each->delete();
                }

                // Web招待状デザイン画像削除
                if (
                    $specificationProduct->m_web_invitations
                        ->web_invitation_design_images
                ) {
                    $specificationProduct->m_web_invitations->web_invitation_design_images->each->delete();
                }

                // Web招待状マスタ削除
                if ($specificationProduct->m_web_invitations) {
                    $specificationProduct->m_web_invitations->delete();
                }

                // 規格別商品に関連する商品画像を削除
                $specificationProduct->delete();
            }

            //商品タグ削除
            $product->tags()->detach();
            //商品マスタ削除
            $product->delete();

            return $product;
        });
    }
}
