<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Member;
use App\Models\WeddingInfo;
use App\Models\FamilyProfile;
use App\Models\GuestList;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Consts\SystemConst;

final class AdminCreateMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["member", "wedding_info"]);

        return DB::transaction(function () use ($credentials) {
            $member = $credentials["member"];
            $wedding_info = $credentials["wedding_info"];

            //会員登録
            $memberModel = new Member();
            $memberModel->fill($member);
            $memberModel->save();

            //結婚式情報
            $weddingInfoModel = new WeddingInfo();
            $weddingInfoModel->member_id = $memberModel->id;
            $weddingInfoModel->fill($wedding_info);
            $weddingInfoModel->save();

            //家族プロフィール
            $familyProfile = new FamilyProfile();
            $familyProfile->member_id = $memberModel->id;
            $familyProfile->first_name = $memberModel->first_name;
            $familyProfile->last_name = $memberModel->last_name;
            $familyProfile->first_name_romaji = $memberModel->first_name_romaji;
            $familyProfile->last_name_romaji = $memberModel->last_name_romaji;
            $familyProfile->birth_date = $memberModel->birthdate;
            $familyProfile->order = 1;
            $familyProfile->save();

            //デフォルトゲストリスト登録
            $guestList = new GuestList();
            $guestList->member_id = $memberModel->id;
            $guestList->name = SystemConst::DEFAULT_GUEST_LIST_NAME;
            $guestList->is_default = true;
            $guestList->save();

            return $memberModel;
        });
    }
}
