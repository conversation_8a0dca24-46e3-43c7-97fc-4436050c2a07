<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Member;
use App\Models\WeddingInfo;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;

final class AdminUpdateMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["member", "wedding_info"]);

        return DB::transaction(function () use ($credentials) {
            $member = $credentials["member"];
            $wedding_info = $credentials["wedding_info"];

            $memberModel = Member::find($member["id"]);

            if (!$member) {
                throw new InValidException("会員情報が見つかりません。", [
                    "error" => ["会員情報が見つかりません。"],
                ]);
            }
            $memberModel->update($member);

            //結婚式情報
            $weddingInfoModel = WeddingInfo::find($wedding_info["id"]);

            if (!$weddingInfoModel) {
                throw new InValidException("結婚式情報が見つかりません。", [
                    "error" => ["結婚式情報が見つかりません。"],
                ]);
            }
            $weddingInfoModel->update($wedding_info);

            return $memberModel;
        });
    }
}
