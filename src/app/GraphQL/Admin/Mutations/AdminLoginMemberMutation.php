<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Member;
use Illuminate\Support\Arr;
use Illuminate\Auth\AuthenticationException;

final class AdminLoginMemberMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id"]);
        $member = Member::find($credentials["id"]);
        if (!$member) {
            throw new AuthenticationException("該当するユーザが存在しません", [
                "member",
            ]);
        }
        $token = $member->createAuthToken();
        return $token;
    }
}
