<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\VariationTemplate;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

final class AdminDeleteVariationTemplateMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id"]);

        return DB::transaction(function () use ($credentials) {
            $variationTemplate = VariationTemplate::findOrFail(
                $credentials["id"]
            );
            $variationTemplate->master_blocks()->detach();
            $variationTemplate->delete();

            return $variationTemplate;
        });
    }
}
