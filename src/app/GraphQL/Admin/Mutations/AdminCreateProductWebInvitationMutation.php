<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Product;
use App\Models\MSpecificationProduct;
use App\Models\ProductImage;
use App\Models\MWebInvitation;
use App\Models\WebInvitationDesignImage;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\FileS3Service;
use App\Services\FileService;
use App\Enums\FileType;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use App\Services\UuidGeneratorService;
use Illuminate\Support\Facades\Storage;

final class AdminCreateProductWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, [
            "product",
            "m_specification_products",
            "product_tags",
        ]);

        return DB::transaction(function () use ($credentials) {
            //商品を登録
            $product = Product::create($credentials["product"]);

            // 商品タグを登録
            if (!empty($credentials["product_tags"])) {
                $tagIds = array_column($credentials["product_tags"], "tag_id");
                $product->tags()->sync($tagIds);
            }

            $fileS3Service = new FileS3Service();
            $fileService = new FileService();
            $now = Carbon::now();

            foreach (
                $credentials["m_specification_products"]
                as $m_specification_product
            ) {
                $product_images = Arr::pull(
                    $m_specification_product,
                    "product_images"
                );
                $m_web_invitation = Arr::pull(
                    $m_specification_product,
                    "m_web_invitation"
                );
                $web_invitation_design_images = Arr::pull(
                    $m_web_invitation,
                    "web_invitation_design_images"
                );

                $m_specification_product["product_id"] = $product->id;
                // 規格別商品を登録
                $mSpecificationProduct = MSpecificationProduct::create(
                    $m_specification_product
                );

                // 規格別商品画像を登録
                $productImageInput = [];
                foreach ($product_images ?? [] as $product_image) {
                    $fileData = $product_image["file"];

                    //画像保存処理
                    $fileS3Service->setFileData($fileData);
                    $fileName = $fileS3Service->getFileName();

                    //画像テーブルに保存
                    $image = $fileS3Service->imageSave(
                        $fileName,
                        FileType::FILE_TYPE_PRODUCT_MASTER
                    );

                    //規格商品画像情報
                    unset($product_image["file"]);
                    $product_image[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $product_image["uuid"] = $image->uuid;
                    $product_image["m_specification_product_id"] =
                        $mSpecificationProduct->id;
                    $product_image["created_at"] = $now;
                    $product_image["updated_at"] = $now;
                    $productImageInput[] = $product_image;

                    //サイズと長辺の長さ取得
                    $sizes = FileS3Service::SIZE_LIST;

                    //リサイズとファイルをS3に保存
                    foreach ($sizes as $size => $length) {
                        //画像リサイズと圧縮
                        $fileCompression = $fileS3Service->imageCompression(
                            $fileData,
                            $length
                        );

                        // ファイル名_サイズで保存
                        $newFileName = "{$fileName}_{$size}";

                        //ファイルをS3に保存
                        $fileS3Service->fileMove(
                            $fileCompression,
                            $newFileName
                        );
                    }
                }
                ProductImage::insert($productImageInput);

                // Web招待状マスタを登録
                $m_web_invitation["m_specification_product_id"] =
                    $mSpecificationProduct->id;

                if (!empty($m_web_invitation["image_aspect_settings_json"])) {
                    $m_web_invitation[
                        "image_aspect_settings_json"
                    ] = json_decode(
                        $m_web_invitation["image_aspect_settings_json"],
                        true
                    );
                }
                $mWebInvitation = MWebInvitation::create($m_web_invitation);

                $mWebInvitationId = $mWebInvitation->id;
                $filepath = env('AWS_DIR_WI') . "/" . $product->id;

                // Web招待状デザイン画像を登録
                $webInvitationDesignImageInput = [];
                foreach (
                    $web_invitation_design_images ?? []
                    as $web_invitation_design_image
                ) {
                    //画像保存処理
                    $file = $web_invitation_design_image["file"];
                    Storage::disk("s3_public")->putFileAs(
                        $filepath,
                        $file,
                        $file->getClientOriginalName(),
                    );

                    unset($web_invitation_design_image["file"]);

                    $web_invitation_design_image[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $web_invitation_design_image[
                        "m_web_invitation_id"
                    ] = $mWebInvitationId;
                    $web_invitation_design_image["created_at"] = $now;
                    $web_invitation_design_image["updated_at"] = $now;
                    $webInvitationDesignImageInput[] = $web_invitation_design_image;
                }
                WebInvitationDesignImage::insert(
                    $webInvitationDesignImageInput
                );
            }

            return $product;
        });
    }
}
