<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\MoneyTransfer;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Services\CsvService;
use App\Enums\MoneyTransferStatusEnum;
use Carbon\Carbon;
use App\Consts\CsvConst;
use Exception;
use Illuminate\Support\Facades\Validator;
use App\Mail\AdvancePaymentCompleteMail;
use Illuminate\Support\Facades\Mail;
use App\GraphQL\Exceptions\InValidException;

final class AdminCSVMoneyTransferImportMutation
{
    protected $csvService;

    public function __construct(CsvService $csvService)
    {
        $this->csvService = $csvService;
    }

    /** 「金額」,「口座番号」、「送金ステータス=2」のデータとCSVのデータをつき合わせて該当するデータを更新
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["file"]);

        return DB::transaction(function () use ($credentials) {
            [$header, $csvData] = $this->csvService->convertToArray(
                $credentials["file"]->path()
            );

            $csvHeader = $this->validateCsvHeader($header);

            //口座番号[金額]取得
            $transferDataList = [];

            $columnIndexes = match ($csvHeader) {
                CsvConst::CSV_TRANSFER_IMPORT_NAME => ['date' => 0, 'amount' => 1, 'id' => 3],
                CsvConst::CSV_GENERAL_TRANSFER_IMPORT_NAME => ['date' => 1, 'amount' => 7, 'id' => 5],
                default => throw new InValidException("csvの形式が間違っています", [
                "error" => ["csvの形式が間違っています"],
                ])
            };

            if ($columnIndexes) {
                $transferDataList = [];

                array_map(function ($row) use (&$transferDataList, $columnIndexes) {
                    if (preg_match("/\d{7}/", $row[$columnIndexes['id']], $matches)) {
                        $transferDataList[$matches[0]][] = [
                            'date' => Carbon::parse($row[$columnIndexes['date']]),
                            'amount' => $row[$columnIndexes['amount']]
                        ];
                    }
                }, $csvData);
            }


            //未送金のデータを取得
            $moneyTransfers = MoneyTransfer::select(
                "id",
                "transfer_amount",
                "account_number"
            )
                ->where("status", MoneyTransferStatusEnum::RESERVATION)
                ->get();

            //送金完了したデータのみ抽出して更新
            $successCount = 0;
            $errorCount = 0;
            $updateIds = [];
            foreach ($moneyTransfers as $row) {
                $transferDataForAccount = $transferDataList[$row->account_number] ?? [];

                // 同一日付のデータを整理
                $dateGroupedData = [];
                foreach ($transferDataForAccount as $transferData) {
                    $dateGroupedData[$transferData['date']->format('Ymd')][] = $transferData['amount'];
                }

                foreach ($transferDataForAccount as $transferData) {
                    $amountsOnSameDate = $dateGroupedData[$transferData['date']->format('Ymd')] ?? [];

                    // 同じ日付のデータに符号がプラスとマイナスの両方あるか確認
                    $hasPositive = array_filter($amountsOnSameDate, fn($amt) => $amt > 0);
                    $hasNegative = array_filter($amountsOnSameDate, fn($amt) => $amt < 0);

                    // 送金エラー
                    if ($hasPositive && $hasNegative) {
                        $errorCount += MoneyTransfer::where("id", $row->id)->update([
                            "status" => MoneyTransferStatusEnum::NOT_TRANSFERRED,
                            "is_error" => true,
                        ]);
                        continue;
                    }

                    if (abs((int)$transferData['amount']) == $row->floor_transfer_amount) {
                        // 送金正常完了
                        $successCount += MoneyTransfer::where("id", $row->id)->update([
                            "status" => MoneyTransferStatusEnum::COMPLETED,
                            "completion_datetime" => now(),
                            "is_error" => false,
                        ]);

                        $updateIds[] = $row->id;
                    }
                }
            }

            // 送金テーブルから会員IDの配列を取得
            $members = MoneyTransfer::whereIn("id", $updateIds)
                ->with([
                    "member" => function ($query) {
                        $query->select(
                            "id",
                            "email",
                            "last_name",
                            "first_name"
                        );
                    },
                ])
                ->get()
                ->map(function ($moneyTransfer) {
                    return [
                        "email" => $moneyTransfer->member->email,
                        "last_name" => $moneyTransfer->member->last_name,
                        "first_name" => $moneyTransfer->member->first_name,
                    ];
                })
                ->unique("email")
                ->values();

            //メール送信
            foreach ($members as $member) {
                Mail::to($member["email"])->send(
                    new AdvancePaymentCompleteMail(
                        $member["last_name"],
                        $member["first_name"]
                    )
                );
            }

            return [
                'success_count' => $successCount,
                'error_count' => $errorCount
            ];
        });
    }

    /**
     * CSVヘッダーのバリデーションを行うメソッド
     */
    private function validateCsvHeader(array $header)
    {
        Validator::extend("same_array_values", function (
            $attribute,
            $value,
            $parameters,
            $validator
        ) {
            $otherArray = explode(",", $parameters[0]);
            return count(array_unique(array_merge($value, $otherArray))) ===
                max(count($value), count($otherArray));
        });

        $validHeaders = [
            CsvConst::CSV_TRANSFER_IMPORT_NAME => CsvConst::CSV_TRANSFER_IMPORT,
            CsvConst::CSV_GENERAL_TRANSFER_IMPORT_NAME => CsvConst::CSV_GENERAL_TRANSFER_IMPORT,
        ];

        $isValid = false;

        foreach ($validHeaders as $key => $validHeader) {
            $validator = app("validator")->make(
                ["header" => $header],
                [
                    "header" => [
                        "required",
                        "array",
                        "size:" . count($validHeader),
                        "same_array_values:" . implode(",", $validHeader),
                    ],
                ]
            );

            if (!$validator->fails()) {
                return $key;
            }
        }

        if (!$isValid) {
            throw new InValidException("csvの形式が間違っています", [
                "error" => ["csvの形式が間違っています"],
            ]);
        }
    }
}
