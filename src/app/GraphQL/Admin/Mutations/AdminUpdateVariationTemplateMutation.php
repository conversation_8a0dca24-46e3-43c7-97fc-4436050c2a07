<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\VariationTemplate;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

final class AdminUpdateVariationTemplateMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["input", "master_block_ids"]);

        return DB::transaction(function () use ($credentials) {
            $variationTemplate = VariationTemplate::findOrFail(
                $credentials["input"]["id"]
            );

            $variationTemplate->update($credentials["input"]);

            $variationTemplate
                ->master_blocks()
                ->sync($credentials["master_block_ids"]);

            return $variationTemplate;
        });
    }
}
