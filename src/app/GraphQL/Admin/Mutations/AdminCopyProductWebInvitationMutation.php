<?php declare(strict_types=1);

namespace App\GraphQL\Admin\Mutations;

use App\Models\Product;
use App\Models\ProductImage;
use App\Models\MWebInvitation;
use App\Models\WebInvitationDesignImage;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\GraphQL\Exceptions\InValidException;
use Carbon\Carbon;
use App\Services\FileS3Service;
use App\Services\UuidGeneratorService;
use Illuminate\Support\Facades\Storage;
final class AdminCopyProductWebInvitationMutation
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["id"]);

        return DB::transaction(function () use ($credentials) {
            //商品を取得
            $product = Product::withoutGlobalScopes()
                ->with([
                    "tags",
                    "m_specification_products",
                    "m_specification_products.product_images",
                    "m_specification_products.m_web_invitations",
                    "m_specification_products.m_web_invitations.web_invitation_design_images",
                ])
                ->find($credentials["id"]);

            if (!$product) {
                throw new InValidException("商品情報が見つかりません。", [
                    "error" => ["商品情報が見つかりません。"],
                ]);
            }

            $now = Carbon::now();

            //商品を登録
            $copyProduct = $product->replicate();
            $copyProduct->name = $copyProduct->name . "のコピー";
            $copyProduct->created_at = $now;
            $copyProduct->updated_at = $now;
            $copyProduct->save();

            //商品タグを登録
            $tagIds = $product->tags->pluck("id");
            if (!empty($tagIds)) {
                $copyProduct->tags()->sync($tagIds);
            }

            $fileS3Service = new FileS3Service();

            foreach (
                $product->m_specification_products ?? []
                as $m_specification_product
            ) {
                //規格別商品画像
                $copyMSpecificationProduct = $m_specification_product->replicate();
                $copyMSpecificationProduct->product_id = $copyProduct->id;
                $copyMSpecificationProduct->created_at = $now;
                $copyMSpecificationProduct->updated_at = $now;
                $copyMSpecificationProduct->save();

                //商品画像
                $productImages = [];
                foreach (
                    $m_specification_product->product_images ?? []
                    as $product_image
                ) {
                    //画像コピー
                    $copyProductImage = $product_image->replicate()->toArray();
                    $image = $fileS3Service->copyFile(
                        $copyProductImage["uuid"]
                    );

                    $copyProductImage[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $copyProductImage["uuid"] = $image->uuid;
                    $copyProductImage["m_specification_product_id"] =
                        $copyMSpecificationProduct->id;
                    $copyProductImage["created_at"] = $now;
                    $copyProductImage["updated_at"] = $now;
                    $productImages[] = $copyProductImage;
                }
                ProductImage::insert($productImages);

                // Web招待状マスタを登録
                $mWebInvitation = $m_specification_product->m_web_invitations
                    ->replicate()
                    ->toArray();
                $mWebInvitation["m_specification_product_id"] =
                    $copyMSpecificationProduct->id;
                $copyMWebInvitation = MWebInvitation::create($mWebInvitation);

                //web招待状 画像移動
                $oldPath = env('AWS_DIR_WI') . $product->id;
                $newPath = env('AWS_DIR_WI') . $copyProduct->id;

                // フォルダ内のファイル一覧を取得
                $files = Storage::disk('s3_public')->allFiles($oldPath);

                foreach ($files as $file) {
                    $newFilePath = str_replace($oldPath, $newPath, $file);

                    Storage::disk('s3_public')->copy($file, $newFilePath);
                }

                //Web招待状デザイン画像を登録
                $webInvitationDesignImages = [];
                foreach (
                    $m_specification_product->m_web_invitations
                        ->web_invitation_design_images ?? []
                    as $web_invitation_design_image
                ) {
                    $copyWebInvitationDesignImage = $web_invitation_design_image
                        ->replicate()
                        ->toArray();
                    $copyWebInvitationDesignImage[
                        "id"
                    ] = UuidGeneratorService::generateOrderedUuid();
                    $copyWebInvitationDesignImage["m_web_invitation_id"] =
                        $copyMWebInvitation->id;
                    $copyWebInvitationDesignImage["created_at"] = $now;
                    $copyWebInvitationDesignImage["updated_at"] = $now;
                    $webInvitationDesignImages[] = $copyWebInvitationDesignImage;
                }
                WebInvitationDesignImage::insert($webInvitationDesignImages);
            }

            return $copyProduct;
        });
    }
}
