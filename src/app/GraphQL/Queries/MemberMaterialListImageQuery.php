<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\Image;
use App\Enums\OwnerType;
use App\Enums\FileType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Consts\SystemConst;
use App\Enums\ImageTypeEnum;
use Illuminate\Support\Facades\Config;

//会員アップロード素材一覧取得
final class MemberMaterialListImageQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $user = Auth::user();

        $images = Image::where([
            ["owner_type", OwnerType::OWNER_TYPE_MEMBER],
            ["owner_id", $user->id],
            ["file_type", FileType::FILE_TYPE_MEMBER_MATERIAL],
            ["hidden_at", null],
        ])
            ->orderBy("created_at", "desc")
            ->get();

        //S3の設定を取得
        $storage = Storage::disk("s3");
        $config = $storage->getConfig();
        if ($config["use_path_style_endpoint"]) {
            $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
            $storage = Storage::build(Arr::set($config, "endpoint", $endpoint));
        }

        $mediaConvertNameDecorator = Config::get(
            "services.media_convert.name_decorator"
        );

        //一時署名付きURL作成
        $fileUrlList = $images
            ->map(function ($image) use (
                $user,
                $storage,
                $config,
                $mediaConvertNameDecorator
            ) {
                $status = true;
                $presignedUrl = null;
                $presignedUrlMain = null;

                if ($image->type == ImageTypeEnum::IMAGE) {
                    $filePath =
                        env("AWS_DIR_MEMBER") .
                        "{$user->id}/{$image->name}_s.{$image->extension_type}";

                    if (!Storage::disk("s3")->exists($filePath)) {
                        $filePath =
                            env("AWS_DIR_MEMBER") .
                            "{$user->id}/{$image->name}_m.{$image->extension_type}";
                        if (!Storage::disk("s3")->exists($filePath)) {
                            $status = false;
                            $filePath = null;
                        }
                    }
                    if (!empty($filePath)) {
                        $presignedUrl = Storage::disk('s3')->url($filePath);
                    }
                } else {
                    $filePathImage =
                        env("AWS_DIR_MEMBER") .
                        "{$image->owner_id}/{$image->name}_s.jpg";
                    $filePathVideo =
                        env("AWS_DIR_MEMBER") .
                        "{$image->owner_id}/{$image->name}{$mediaConvertNameDecorator}.{$image->extension_type}";

                    if (!Storage::disk("s3")->exists($filePathImage)) {
                        $status = false;
                    } else {
                        $presignedUrl = Storage::disk('s3')->url($filePathImage);
                    }

                    if (!Storage::disk("s3")->exists($filePathVideo)) {
                        $status = false;
                    } else {
                        $presignedUrlMain = Storage::disk('s3')->url($filePathVideo);
                    }
                }

                return [
                    "presigned_url" => $presignedUrl,
                    "uuid" => $image->uuid,
                    "status" => $status,
                    "presigned_url_main" => $presignedUrlMain,
                    "type" => $image->type,
                ];
            })
            ->toArray();
        return $fileUrlList;
    }
}
