<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\Image;
use App\Enums\OwnerType;
use App\Enums\FileType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\GraphQL\Exceptions\InValidException;
use App\Models\Member;
use Faker\Core\File;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

final class ImageQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["uuids"]);

        //ファイル情報取得
        $images = Image::whereIn("uuid", $credentials["uuids"])->get();

        //S3の設定を取得
        $storage = Storage::disk("s3");
        $config = $storage->getConfig();
        if ($config["use_path_style_endpoint"]) {
            $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
            $storage = Storage::build(Arr::set($config, "endpoint", $endpoint));
        }

        //認証ユーザー
        $user = Auth::user();

        $tmpImages = [];
        foreach ($images as $image) {
            //ファイル種別と会員による認証チェック
            $status = true;
            switch ($image->file_type) {
                case FileType::FILE_TYPE_MEMBER_MATERIAL:
                case FileType::FILE_TYPE_MEMBER_ETC:
                    if ($image->owner_type == OwnerType::OWNER_TYPE_MEMBER) {
                        if (
                            !$user ||
                            ($user instanceof Member &&
                                $user->id !== $image->owner_id)
                        ) {
                            $status = false;
                        }
                    }
                    break;
            }

            //パスの取得
            $filePath = "";
            if (
                $image->file_type >= 1 &&
                $image->file_type < 30 &&
                $image->file_type != FileType::FILE_TYPE_GUEST_UPLOAD_FILE
            ) {
                $filePath =
                    env("AWS_DIR_MEMBER") .
                    "{$image->owner_id}/{$image->name}_m.{$image->extension_type}";
            } elseif ($image->file_type >= 30) {
                $filePath =
                    env("AWS_DIR_ADMIN") .
                    "{$image->name}_m.{$image->extension_type}";
            } else {
                $filePath =
                    env("AWS_DIR_MEMBER") .
                    "{$image->owner_id}/" .
                    env("AWS_DIR_GUEST") .
                    "{$image->name}_m.{$image->extension_type}";
            }

            $presignedUrl = null;
            if (!Storage::disk("s3")->exists($filePath)) {
                $status = false;
            } else {
                //一時署名付きURL作成
                $presignedUrl = $storage->temporaryUrl(
                    $filePath,
                    now()->addSeconds($config["url_member_expiration"])
                );
            }

            $tmpImages[$image->uuid] = [
                "uuid" => $image->uuid,
                "presigned_url" => $presignedUrl,
                "status" => $status,
            ];
        }

        // リクエストされたuuidsの順序に基づいて結果を並べ替える
        $ret = [];
        foreach ($credentials["uuids"] as $uuid) {
            if (array_key_exists($uuid, $tmpImages)) {
                $ret[] = $tmpImages[$uuid];
            } else {
                // uuidが見つからない場合は、エラーまたはデフォルトのデータを追加する
                $ret[] = [
                    "uuid" => $uuid,
                    "presigned_url" => null,
                    "status" => false,
                ];
            }
        }

        return $ret;
    }
}
