<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\Guest;
use App\Models\EventList;
use App\Models\WebInvitation;
use App\Enums\MemberConfirmTypeEnum;
use App\Consts\SystemConst;

final class GuestListEventAnswerQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, [
            "guest_list_id",
            "guest_event_attendances",
            "guest_name",
            "web_invitation_id",
            "guest_type",
            "guest_group_ids",
            "guest_tag_ids",
            "orderBy",
            "is_update_member_confirm_type",
        ]);

        return DB::transaction(function () use ($params) {
            //特定のゲスト情報を取得
            $query = Guest::with([
                "guest_event_answers",
                "guest_event_answers.event_list",
                "guest_group",
                "guest_tags",
            ])
                ->where("guests.guest_list_id", $params["guest_list_id"])
                ->select([
                    "guests.id as id",
                    "guests.member_id as member_id",
                    "guests.guest_list_id as guest_list_id",
                    "guests.guest_group_id as guest_group_id",
                    "guests.parent_guest_id as parent_guest_id",
                    "guests.web_invitation_id as web_invitation_id",
                    "guests.m_web_invitation_id as m_web_invitation_id",
                    "guests.guest_type as guest_type",
                    "guests.last_name as last_name",
                    "guests.first_name as first_name",
                    "guests.last_name_kana as last_name_kana",
                    "guests.first_name_kana as first_name_kana",
                    "guests.last_name_romaji as last_name_romaji",
                    "guests.first_name_romaji as first_name_romaji",
                    "guests.gender as gender",
                    "guests.allergies as allergies",
                    "guests.allergy as allergy",
                    "guests.birthdate as birthdate",
                    "guests.image_url as image_url",
                    "guests.postal_code as postal_code",
                    "guests.prefecture as prefecture",
                    "guests.city as city",
                    "guests.address as address",
                    "guests.building as building",
                    "guests.phone as phone",
                    "guests.email as email",
                    "guests.message as message",
                    "guests.media_uuid as media_uuid",
                    "guests.media_type as media_type",
                    "guests.invitation_delivery as invitation_delivery",
                    "guests.guest_title as guest_title",
                    "guests.guest_honor as guest_honor",
                    "guests.relationship as relationship",
                    "guests.web_invite_reply_datetime as web_invite_reply_datetime",
                    "guests.member_confirm_type as member_confirm_type",
                    "guests.payment_method as payment_method",
                    "guests.is_system_fee as is_system_fee",
                    "guests.system_fee as system_fee",
                    "guests.system_fee_rate as system_fee_rate",
                    "guests.total_amount as total_amount",
                    "guests.settlement_amount as settlement_amount",
                    "guests.card_settlement_id as card_settlement_id",
                    "guests.created_at as created_at",
                    "guests.updated_at as updated_at",
                    "guests.deleted_at as deleted_at",
                ]);

            //ゲスト名
            if (!empty($params["guest_name"])) {
                $query->where(function ($query) use ($params) {
                    $guestName = $params["guest_name"];
                    $query
                        ->where("guests.last_name", "LIKE", "%$guestName%")
                        ->orWhere("guests.first_name", "LIKE", "%$guestName%")
                        ->orWhere(
                            "guests.last_name_kana",
                            "LIKE",
                            "%$guestName%"
                        )
                        ->orWhere(
                            "guests.first_name_kana",
                            "LIKE",
                            "%$guestName%"
                        )
                        ->orWhere(
                            "guests.last_name_romaji",
                            "LIKE",
                            "%$guestName%"
                        )
                        ->orWhere(
                            "guests.first_name_romaji",
                            "LIKE",
                            "%$guestName%"
                        );
                });
            }

            //回答した招待状
            if (!empty($params["web_invitation_id"])) {
                $query->where(
                    "guests.web_invitation_id",
                    $params["web_invitation_id"]
                );
            }

            //ゲストタイプ(新郎/新婦)
            if (!empty($params["guest_type"])) {
                $query->where("guests.guest_type", $params["guest_type"]);
            }

            //グループ
            if (!empty($params["guest_group_ids"])) {
                $query->whereIn(
                    "guests.guest_group_id",
                    $params["guest_group_ids"]
                );
            }

            //タグ
            if (isset($params["guest_tag_ids"])) {
                $query->whereHas("guest_tags", function ($query) use ($params) {
                    $query->whereIn("guest_tags.id", $params["guest_tag_ids"]);
                });
            }

            // ソート指示の処理
            foreach ($params["orderBy"] ?? [] as $sortInstruction) {
                $order = $sortInstruction["order"]->value;
                if (isset($sortInstruction["guest_group"])) {
                    $column = $sortInstruction["guest_group"]["column"];
                    $query
                        ->leftjoin("guest_groups", function ($join) {
                            $join->on(
                                "guests.guest_group_id",
                                "=",
                                "guest_groups.id"
                            );
                        })
                        ->orderBy("guest_groups.$column", $order);
                } elseif (isset($sortInstruction["column"])) {
                    if ($sortInstruction["column"] == "name") {
                        $query->orderByRaw(
                            "CONVERT(CONCAT(guests.last_name, guests.first_name) USING utf8mb4) COLLATE utf8mb4_unicode_ci"
                        );
                    } else {
                        $query->orderBy(
                            "guests." . $sortInstruction["column"],
                            $order
                        );
                    }
                }
            }

            //ゲスト情報を取得
            $guestListModels = $query
                ->orderBy("guests.parent_guest_id")
                ->get()
                ->toArray();

            //筆頭者ゲスト情報を取得
            $guestLists = [];

            //連名者ゲスト情報を取得
            $childGuestList = [];
            foreach ($guestListModels as $guest) {
                if (empty($guest["parent_guest_id"])) {
                    $guestLists[] = $guest;
                } else {
                    array_unshift($childGuestList, $guest);
                }
            }

            //連名者を筆頭者の直後に追加
            foreach ($childGuestList as $childGuest) {
                $index = array_search(
                    $childGuest["parent_guest_id"],
                    array_column($guestLists, "id")
                );
                if ($index !== false) {
                    array_splice($guestLists, $index + 1, 0, [$childGuest]);
                }
            }

            //イベントリスト取得
            $eventList = EventList::where(
                "guest_list_id",
                $params["guest_list_id"]
            )->get();

            //フラグによりDBと配列の会員出席種別の更新(新着->未読)
            if ($params["is_update_member_confirm_type"]) {
                // 新着に一致する要素をフィルタリング
                $filteredGuests = array_filter($guestLists, function ($guest) {
                    return $guest["member_confirm_type"] ==
                        MemberConfirmTypeEnum::New;
                });

                // フィルタリングされた要素からidを抽出
                $guestIds = array_map(function ($guest) {
                    return $guest["id"];
                }, $filteredGuests);

                // 新着に一致した要素のデータを未読に更新
                Guest::whereIn("id", $guestIds)->update([
                    "member_confirm_type" => MemberConfirmTypeEnum::Unread,
                ]);

                // 新着に一致した要素の配列更新
                array_walk($guestLists, function (&$guest) {
                    if (
                        $guest["member_confirm_type"] ==
                        MemberConfirmTypeEnum::New
                    ) {
                        $guest["member_confirm_type"] =
                            MemberConfirmTypeEnum::Unread;
                    }
                });
            }

            $paramEventAttendances = array_reduce(
                $params["guest_event_attendances"] ?? [],
                function ($carry, $item) {
                    $carry[$item["name"]] = $item["attendance"];
                    return $carry;
                },
                []
            );

            //レスポンスの値を型にはめるための加工
            $ret = [];
            foreach ($guestLists as $guest) {
                $guest_group = $guest["guest_group"] ?? [];
                $guest_event_answers = $guest["guest_event_answers"] ?? [];
                $guest_tags = $guest["guest_tags"] ?? [];
                unset(
                    $guest["guest_group"],
                    $guest["guest_event_answers"],
                    $guest["guest_tags"]
                );

                if (!$guest["media_type"]) {
                    $guest["media_type"] = null;
                }

                if ($guest["created_at"]) {
                    $guest["created_at"] = date(
                        "Y-m-d H:i:s",
                        strtotime($guest["created_at"])
                    );
                }
                if ($guest["updated_at"]) {
                    $guest["updated_at"] = date(
                        "Y-m-d H:i:s",
                        strtotime($guest["updated_at"])
                    );
                }
                if ($guest["deleted_at"]) {
                    $guest["deleted_at"] = date(
                        "Y-m-d H:i:s",
                        strtotime($guest["deleted_at"])
                    );
                }

                //ゲスト情報を配列に入れる
                $ret[$guest["id"]] = $guest;

                //ゲストグループを抽出
                $ret[$guest["id"]]["guest_group_name"] =
                    $guest_group["name"] ?? null;

                //ゲストタグを抽出
                $ret[$guest["id"]]["guest_event_tags"] = [];
                $ret[$guest["id"]]["guest_event_tags"] = array_map(function (
                    $guest_tag
                ) {
                    return [
                        "id" => $guest_tag["id"],
                        "tag" => $guest_tag["tag"],
                    ];
                }, $guest_tags);

                // ゲストイベント配列の加工
                $eventAnswers = [];
                foreach ($guest_event_answers as $row) {
                    if (!isset($row["event_list"])) {
                        continue;
                    }
                    $eventAnswers[$row["event_list"]["event_name"]] = [
                        "id" => $row["id"],
                        "attendance" => $row["attendance"],
                        "date" => $row["event_list"]["event_date"],
                    ];
                }

                // イベント(パーティー毎)に出席情報を配列に入れる。筆頭者がいるゲストは筆頭者に合わせる
                $ret[$guest["id"]]["guest_event_attendances"] = [];

                foreach ($eventList as $key => $event) {
                    $id = $eventAnswers[$event->event_name]["id"] ?? null;
                    $attendance =
                        $eventAnswers[$event->event_name]["attendance"] ?? null;

                    if (
                        isset($paramEventAttendances[$event->event_name]) &&
                        $paramEventAttendances[$event->event_name] !=
                            SystemConst::GUEST_EVENT_ATTNDANCE_ALL &&
                        $paramEventAttendances[$event->event_name] !=
                            $attendance
                    ) {
                        unset($ret[$guest["id"]]);
                        break;
                    }

                    $ret[$guest["id"]]["guest_event_attendances"][$key] = [
                        "id" => $id,
                        "name" => $event->event_name,
                        "attendance" => $attendance,
                    ];
                }
            }
            return $ret;
        });
    }
}
