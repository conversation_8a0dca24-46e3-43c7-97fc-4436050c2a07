<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\Image;
use App\Enums\FileType;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use App\Enums\ImageTypeEnum;
use Illuminate\Support\Facades\Config;

final class MaterialListImageQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["file_type", "orderBy"]);

        // 会員関連は認証エラーとする
        if (
            !empty($params["file_type"]) &&
            $params["file_type"]->value < FileType::FILE_TYPE_MATERIAL
        ) {
            throw new AuthenticationException();
        }

        $file_type = $params["file_type"] ?? FileType::FILE_TYPE_MATERIAL;

        $images = Image::where("file_type", $file_type)
            ->orderBy("created_at", "desc")
            ->get();

        //S3の設定を取得
        $storage = Storage::disk("s3");
        $config = $storage->getConfig();
        if ($config["use_path_style_endpoint"]) {
            $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
            $storage = Storage::build(Arr::set($config, "endpoint", $endpoint));
        }

        $mediaConvertNameDecorator = Config::get(
            "services.media_convert.name_decorator"
        );

        //URL作成
        $fileUrlList = $images
            ->map(function ($image) use (
                $storage,
                $config,
                $mediaConvertNameDecorator
            ) {
                $status = true;
                $fileUrl = null;
                $presignedUrlMain = null;

                if ($image->type == ImageTypeEnum::IMAGE) {
                    $filePath =
                        env("AWS_DIR_ADMIN") .
                        "{$image->name}_s.{$image->extension_type}";

                    if (!Storage::disk("s3")->exists($filePath)) {
                        $defaultFilePath = str_replace(
                            "_s",
                            "_m",
                            $filePath
                        );
                        if (!Storage::disk("s3")->exists($defaultFilePath)) {
                            $status = false;
                        }else {
                            $fileUrl = Storage::disk('s3')->url($defaultFilePath);
                        }
                    } else {
                        $fileUrl = Storage::disk('s3')->url($filePath);
                    }
                } else {
                    $filePath =
                        env("AWS_DIR_ADMIN") . "{$image->name}_s.jpg";
                    $filePathVideo =
                        env("AWS_DIR_ADMIN") .
                        "{$image->name}{$mediaConvertNameDecorator}.{$image->extension_type}";

                    if (!Storage::disk("s3")->exists($filePath)) {
                        $status = false;
                    } else {
                        $fileUrl = Storage::disk('s3')->url($filePath);
                    }

                    if (!Storage::disk("s3")->exists($filePathVideo)) {
                        $status = false;
                    } else {
                        $presignedUrlMain = Storage::disk('s3')->url($filePathVideo);
                    }
                }

                return [
                    "presigned_url" => $fileUrl,
                    "presigned_url_main" => $presignedUrlMain,
                    "uuid" => $image->uuid,
                    "status" => $status,
                    "type" => $image->type,
                ];
            })
            ->toArray();

        return $fileUrlList;
    }
}
