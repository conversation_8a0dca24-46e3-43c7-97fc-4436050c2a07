<?php

namespace App\GraphQL\Queries;

use App\Models\CountdownImage;
use Illuminate\Support\Facades\Auth;

class CountdownImageQuery
{
    /**
     * 認証ユーザーのカウントダウン画像設定一覧を取得
     *
     * @param null $_
     * @param array $args
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function __invoke($_, array $args)
    {
        $user = Auth::user();
        
        return CountdownImage::getByMember($user->id);
    }

    /**
     * 特定のInstagram種別の設定を取得
     *
     * @param null $_
     * @param array $args
     * @return CountdownImage|null
     */
    public function findByType($_, array $args)
    {
        $user = Auth::user();
        $instagramType = $args['instagram_type'];
        
        return CountdownImage::findByMemberAndType($user->id, $instagramType);
    }
}
