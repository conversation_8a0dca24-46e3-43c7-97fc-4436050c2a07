<?php

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use App\Models\GuestList;
use App\Models\Guest;
use App\GraphQL\Exceptions\InValidException;
use App\Services\MemberAuthorizationService;

// タグ一括編集用 タグ一覧取得
final class UpdateGuestTagsQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["guest_list_id", "guest_ids"]);

        $guestList = GuestList::with('guest_tags')
            ->find($params["guest_list_id"] ?? null);

        if (!$guestList) {
            throw new InValidException("ゲストリスト情報が見つかりません。", [
                "error" => ["ゲストリスト情報が見つかりません。"],
            ]);
        }

        MemberAuthorizationService::authorize($guestList->member_id);

        $guests = Guest::whereIn("id", $params["guest_ids"])
            ->with("guest_tags")
            ->get();
        $tags = $guestList->guest_tags;

        $guestTags = [];
        foreach ($tags as $tag) {
            $isSelected = true;
            foreach ($guests as $guest) {
                MemberAuthorizationService::authorize($guest->member_id);
                if (!$guest->guest_tags->contains($tag)) {
                    $isSelected = false;
                    break;
                }
            }

            $guestTags[] = [
                "tag_id" => $tag->id,
                "tag" => $tag->tag,
                "selected" => $isSelected,
                "dirty" => false,
            ];
        }

        return [
            "guest_list_id" => $params["guest_list_id"],
            "guest_ids" => $params["guest_ids"],
            "tags" => $guestTags,
        ];
    }
}
