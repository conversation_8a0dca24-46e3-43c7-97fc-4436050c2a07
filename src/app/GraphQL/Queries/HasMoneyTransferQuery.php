<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use App\Models\MoneyTransfer;
use Illuminate\Support\Facades\Auth;

// 会員送金ステータス情報存在チェック
final class HasMoneyTransferQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["status"]);

        $hasMoneyTransfer = MoneyTransfer::where([
            ['member_id', Auth::id()],
            ['status', $params['status']->value],
        ])->exists();

        return $hasMoneyTransfer;
    }
}
