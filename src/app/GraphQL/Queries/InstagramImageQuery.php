<?php

namespace App\GraphQL\Queries;

use App\Services\InstagramImage;
use App\Enums\InstagramTypeEnum;
use Illuminate\Support\Facades\Log;

class InstagramImageQuery
{
    protected $instagramImageService;

    public function __construct(InstagramImage $instagramImageService)
    {
        $this->instagramImageService = $instagramImageService;
    }

/**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $imageTypes = InstagramTypeEnum::getValues();
        $images = [];
        foreach ($imageTypes as $imageType) {
            $images[] = [
                'type' => $imageType,
                'image' => $this->instagramImageService->loadImage($imageType),
                'today_image' => $this->instagramImageService->loadTodayImage($imageType),
                'thumbnail_image' => $this->instagramImageService->loadThumbnailImage($imageType),
                'thumbnail_today_image' => $this->instagramImageService->loadThumbnailTodayImage($imageType),
            ];
        }

        return $images;
    }
}
