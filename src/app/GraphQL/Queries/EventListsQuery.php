<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use App\Models\WebInvitation;

// イベント パーティ情報を取得
final class EventListsQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["event_list"]);

        $event_list = [];
        if (
            isset($params["event_list"][0]) &&
            !empty($params["event_list"][0])
        ) {
            $editor_settings = $params["event_list"][0] ?? [];
            $webInvitation = new WebInvitation();
            $webInvitation->editor_settings = $editor_settings;
            $event_list = $webInvitation->event_list;
        }

        return $event_list;
    }
}
