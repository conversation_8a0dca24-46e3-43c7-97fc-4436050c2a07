<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use App\Models\WebInvitation;
use Illuminate\Support\Facades\Auth;
use App\Enums\MoneyTransferStatusEnum;

final class HasUnpaidInvitationsQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $memberId = Auth::id();

        return WebInvitation::where('member_id', $memberId)
            ->where(function ($query) {

                // ① 送金レコードがあり、ステータスが「送金完了」以外
                $query->whereHas('money_transfer', function ($q) {
                    $q->where('status', '!=', MoneyTransferStatusEnum::COMPLETED);
                })
                // OR条件
                ->orWhere(function ($q2) {
                    // ② 送金レコードがなく、ゲストテーブル側に settlement_amount > 0 がある
                    $q2->doesntHave('money_transfer')
                        ->whereHas('guests', function ($q3) {
                            $q3->whereNotNull('settlement_amount')
                                ->where('settlement_amount', '>', 0);
                        });
                });
            })
            ->exists();
    }
}
