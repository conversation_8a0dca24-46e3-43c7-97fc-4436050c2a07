<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Models\Guest;
use App\Enums\MemberConfirmTypeEnum;
use App\Services\MemberAuthorizationService;

final class GuestQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["id", "is_update_member_confirm_type"]);

        return DB::transaction(function () use ($params) {
            $guest = Guest::findOrFail($params["id"]);
            MemberAuthorizationService::authorize($guest->member_id);

            //フラグにより会員出席種別の更新(未読->更新)
            if (
                $params["is_update_member_confirm_type"] &&
                $guest->member_confirm_type == MemberConfirmTypeEnum::Unread
            ) {
                $guest->update([
                    "member_confirm_type" => MemberConfirmTypeEnum::Normal,
                ]);
                $guest->refresh();
            }

            return $guest;
        });
    }
}
