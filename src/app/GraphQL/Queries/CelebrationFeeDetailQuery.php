<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\WebInvitation;
use App\Consts\SystemConst;
use Illuminate\Support\Facades\Auth;
use App\Enums\PaymentMethodEnum;

final class CelebrationFeeDetailQuery
{
    const OKIMOTI_NAME = "お気持ち";
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        //web招待状データ取得
        $webInvitations = $this->getWebInvitations();

        $data = [];

        //web招待別データを作成
        foreach ($webInvitations as $webInvitation) {
            //web招待状初期化
            $data[$webInvitation->id] = $this->initializeData($webInvitation);

            //イベントリスト初期化
            $eventNames = $this->initializeEventNames($webInvitation);

            //事前支払い合計金額
            $payment_amount_total = 0;

            // 事前支払い額 * システム利用料率合計金額
            $total_advance_payment = 0;

            //web招待状別ゲスト回答カウント数
            $guest_event_answers_count = 0;

            //イベント別ゲスト情報
            foreach ($webInvitation->guests ?? [] as $guest) {
                //回答のカウント数を集計
                $guest_event_answers_count += $this->countGuestEventAnswers(
                    $guest
                );

                //ゲスト情報を作成
                $this->processGuestEventAnswers(
                    $guest,
                    $eventNames,
                    $data,
                    $webInvitation,
                    $payment_amount_total,
                    $total_advance_payment
                );
            }

            // web招待状合計金額作成
            $this->finalizeData(
                $data,
                $webInvitation,
                $guest_event_answers_count,
                $payment_amount_total,
                $total_advance_payment,
                $eventNames
            );
        }

        return $data;
    }

    /**
     * 会員別公開招待状取得
     *
     * @return object
     */
    private function getWebInvitations()
    {
        return WebInvitation::with([
            "guests" => function ($query) {
                $query->withTrashed();
            },
            "guests.guest_event_answers" => function ($query) {
                $query->withTrashed();
            },
            "guests.parent_guest",
        ])
            ->whereNotNull("public_url")
            ->where("public_url", "<>", "")
            ->where("member_id", Auth::user()->id)
            ->get()
            ->map(function ($invitation) {
                $invitation->guests = $invitation->guests->sortByDesc(function (
                    $guest
                ) {
                    return $guest->parent_guest_id === null;
                });
                return $invitation;
            });
    }

    /**
     * 初期化データ web招待状情報
     *
     * @param \App\Models\WebInvitation $webInvitation
     * @return array
     */
    private function initializeData($webInvitation)
    {
        return [
            "id" => $webInvitation->id, //Web招待状ID
            "name" => $webInvitation->name, //web招待状名
            "event_date" => $webInvitation->scheduled_date, //パーティー開催日
            "scheduled_transfer_date" =>
                $webInvitation->scheduled_transfer_date, //振込予定日
            "guest_event_answers_count" => 0, //回答数
            "gift_total_amount" => 0, //お気持ち合計金額
            "system_total_fee" => 0, //システム利用料負担金額
            "events" => [], //イベント毎情報
        ];
    }

    /**
     * 初期化データ イベントリスト情報を生成する
     *
     * @param \App\Models\WebInvitation $webInvitation
     * @return array
     */
    private function initializeEventNames($webInvitation)
    {
        $eventNames = [];

        foreach ($webInvitation->event_list ?? [] as $eventName) {
            $eventNames[$eventName] = $this->initializeEventData($eventName);
        }

        // お気持ちイベントの初期化
        $eventNames[self::OKIMOTI_NAME] = $this->initializeEventData(
            self::OKIMOTI_NAME
        );
        return $eventNames;
    }

    /**
     * イベントの初期化データを生成する
     *
     * @param string $eventName
     * @return array
     */
    private function initializeEventData($eventName)
    {
        return [
            "event_name" => $eventName, // イベント名
            "prepaid_amount_total" => 0, // 事前支払いの金額合計
            "prepaid_count_total" => 0, // 事前支払いの人数合計
            "pay_at_venue_count_total" => 0, // 当日持参の人数合計
            "paid_count_total" => 0, // 支払い済みの人数合計
            "gift_total_amount" => 0, // お気持ち合計金額
            "system_total_fee" => 0, // システム利用料負担金額
            "amount_total" => 0, //イベント別合計金額
            "guests" => [], // ゲスト情報
        ];
    }

    /**
     * 回答数カウント
     *
     * @param \App\Models\Guest $guest
     * @return int
     */
    private function countGuestEventAnswers($guest)
    {
        return !empty($guest->guest_event_answers) ? 1 : 0;
    }

    /**
     * ゲスト情報を作成処理を開始
     *
     * @param \App\Models\Guest $guest
     * @param array $eventNames
     * @param array $data
     * @param \App\Models\WebInvitation $webInvitation
     * @param int $payment_amount_total
     * @param int $total_advance_payment
     * @return void
     */
    private function processGuestEventAnswers(
        $guest,
        &$eventNames,
        &$data,
        $webInvitation,
        &$payment_amount_total,
        &$total_advance_payment
    ) {
        //お気持ち料金
        $gift_amount = $this->updateGiftAmount($guest, $data, $webInvitation);

        //システム料
        $system_fee = $this->updateSystemFee($guest, $data, $webInvitation);

        $guest_total_advance_payment = 0;
        foreach ($guest->guest_event_answers ?? [] as $guest_event_answer) {
            $eventName = $guest_event_answer->name;
            if (isset($eventNames[$eventName])) {
                //支払い種別
                $this->updatePaymentMethod(
                    $guest,
                    $guest_event_answer,
                    $eventNames,
                    $payment_amount_total,
                    $guest_total_advance_payment
                );

                //ゲスト情報
                $this->updateGuestInfo(
                    $guest,
                    $eventNames,
                    $guest_event_answer,
                    $gift_amount,
                    $system_fee
                );
            }
        }
        //事前支払い額 * システム利用料率合計金額
        $total_advance_payment += floor(($gift_amount + $guest_total_advance_payment) * SystemConst::SYSTEM_FEE_RATE);
    }

    /**
     * 支払い種別カウント
     *
     * @param \App\Models\Guest $guest
     * @param \App\Models\GuestEventAnswer $guest_event_answer
     * @param array $eventNames
     * @param int $payment_amount_total
     * @param int $guest_total_advance_payment
     * @return void
     */
    private function updatePaymentMethod(
        $guest,
        $guest_event_answer,
        &$eventNames,
        &$payment_amount_total,
        &$guest_total_advance_payment
    ) {
        //事前支払いの人数合計
        if (
            $guest->payment_method == PaymentMethodEnum::ADVANCE_PAYMENT &&
            $guest_event_answer->payment_amount > 0
        ) {
            $eventNames[$guest_event_answer->name]["prepaid_count_total"]++;
            //web招待状事前支払い金額合計
            $payment_amount_total += $guest_event_answer->payment_amount;
            $guest_total_advance_payment += $guest_event_answer->payment_amount;

            //イベント別事前支払い金額
            $eventNames[$guest_event_answer->name]["prepaid_amount_total"] +=
                $guest_event_answer->payment_amount;

            //イベント別合計金額
            $eventNames[$guest_event_answer->name]["amount_total"] +=
                $guest_event_answer->payment_amount;
        }

        //当日持参の人数合計
        if ($guest->payment_method == PaymentMethodEnum::BRING_ON_THE_DAY) {
            $eventNames[$guest_event_answer->name][
                "pay_at_venue_count_total"
            ]++;
        }

        //支払い済みの人数合計
        if ($guest->payment_method == PaymentMethodEnum::PAID) {
            $eventNames[$guest_event_answer->name]["paid_count_total"]++;
        }
    }

    /**
     * web招待状お気持ち合計金額足していく
     *
     * @param \App\Models\Guest $guest
     * @param array $data
     * @param \App\Models\WebInvitation $webInvitation
     * @return int
     */
    private function updateGiftAmount($guest, &$data, $webInvitation)
    {
        $gift_amount = 0;
        if ($guest->gift_amount) {
            $gift_amount = $guest->gift_amount ?? 0;
            $data[$webInvitation->id]["gift_total_amount"] += $gift_amount;
        }
        return $gift_amount;
    }

    /**
     * web招待状システム利用料負担金額足していく
     *
     * @param \App\Models\Guest $guest
     * @param array $data
     * @param \App\Models\WebInvitation $webInvitation
     * @return int
     */
    private function updateSystemFee($guest, &$data, $webInvitation)
    {
        $system_fee = 0;
        if ($guest->is_system_fee) {
            $system_fee = $guest->system_fee ?? 0;
            $data[$webInvitation->id]["system_total_fee"] += $system_fee;
        }
        return $system_fee;
    }

    /**
     * ゲスト情報を設定
     *
     * @param \App\Models\Guest $guest
     * @param array $eventNames
     * @param \App\Models\GuestEventAnswer $guest_event_answer
     * @param int $gift_amount
     * @param int $system_fee
     * @return void
     */
    private function updateGuestInfo(
        $guest,
        &$eventNames,
        $guest_event_answer,
        $gift_amount,
        $system_fee
    ) {
        $guestInfoArray = $this->getGuestInfoArray(
            $guest,
            $guest_event_answer,
            $gift_amount,
            $system_fee
        );
        if (empty($guest->parent_guest_id)) {
            //筆頭者
            if (
                !empty($guest_event_answer->payment_amount) ||
                (
                    $guest->payment_method == PaymentMethodEnum::BRING_ON_THE_DAY ||
                    $guest->payment_method == PaymentMethodEnum::PAID
                )
            ) {
                $eventNames[$guest_event_answer->name]["guests"][
                    $guest->id
                ] = $guestInfoArray;
            }
            //お気持ちイベント
            if (
                $guestInfoArray["is_gift_amount"] &&
                !empty($guestInfoArray["gift_amount"]) &&
                empty($eventNames[self::OKIMOTI_NAME]["guests"][$guest->id])
            ) {
                $guestInfoArray = $this->getGuestOkimotiInfoArray(
                    $guest,
                    $eventNames,
                    $gift_amount,
                    $system_fee
                );
                $eventNames[self::OKIMOTI_NAME]["guests"][
                    $guest->id
                ] = $guestInfoArray;
            }
        } else {
            //連名者のお気持ちフラグは筆頭者に合わせる
            $guestInfoArray["is_gift_amount"] = !empty(
                $guest->parent_guest->gift_amount
            )
                ? true
                : false;

            if (
                !isset(
                    $eventNames[$guest_event_answer->name]["guests"][
                        $guest->parent_guest_id
                    ]
                )
            ) {
                //筆頭者が金額NULLでいない場合連名者を先頭にする
                if (!empty($guest_event_answer->payment_amount) ||
                    (
                        $guest->payment_method == PaymentMethodEnum::BRING_ON_THE_DAY ||
                        $guest->payment_method == PaymentMethodEnum::PAID
                    )
                ) {
                    $eventNames[$guest_event_answer->name]["guests"][
                        $guest->parent_guest_id
                    ] = $guestInfoArray;
                }
                //お気持ちイベント
                if (
                    $guestInfoArray["is_gift_amount"] &&
                    !empty($guest->parent_guest->gift_amount) &&
                    empty(
                        $eventNames[self::OKIMOTI_NAME]["guests"][
                            $guest->parent_guest->id
                        ]
                    )
                ) {
                    $guestInfoArray = $this->getGuestOkimotiInfoArray(
                        $guest->parent_guest,
                        $eventNames,
                        $gift_amount,
                        $system_fee
                    );
                    $eventNames[self::OKIMOTI_NAME]["guests"][
                        $guest->id
                    ] = $guestInfoArray;
                }
            } else {
                //連名者
                if (!empty($guest_event_answer->payment_amount)) {
                    $eventNames[$guest_event_answer->name]["guests"][
                        $guest->parent_guest_id
                    ]["child_guests"][$guest->id] = $guestInfoArray;
                }
            }
        }
    }

    /**
     *  ゲスト情報を取得
     *
     * @param \App\Models\Guest $guest
     * @param \App\Models\GuestEventAnswer $guest_event_answer
     * @param int $gift_amount
     * @param int $system_fee
     * @return array
     */
    private function getGuestInfoArray(
        $guest,
        $guest_event_answer,
        $gift_amount,
        $system_fee
    ) {
        return [
            "id" => $guest->id,
            "last_name" => $guest->last_name,
            "first_name" => $guest->first_name,
            "guest_honor" => $guest->guest_honor,
            "payment_method" => $guest->payment_method,
            "amount" => $guest_event_answer->payment_amount ?? 0,
            "attendance" => $guest_event_answer->attendance,
            "is_attendance" => true,
            "date" => $guest_event_answer->created_at->format("Y-m-d"),
            "is_gift_amount" => !empty($gift_amount) ? true : false,
            "gift_amount" => $gift_amount ?? 0,
            "is_system_fee" => $guest->is_system_fee,
            "system_fee" => $system_fee ?? 0,
            "child_guests" => [],
        ];
    }

    /**
     *  お気に入りゲスト情報を取得
     *
     * @param \App\Models\Guest $guest
     * @param array $eventNames
     * @param int $gift_amount
     * @param int $system_fee
     * @return array
     */
    private function getGuestOkimotiInfoArray(
        $guest,
        &$eventNames,
        $gift_amount,
        $system_fee
    ) {
        //事前支払いの人数合計
        if ($guest->payment_method == PaymentMethodEnum::ADVANCE_PAYMENT) {
            $eventNames[self::OKIMOTI_NAME]["prepaid_count_total"]++;

            //イベント別事前支払い金額
            $eventNames[self::OKIMOTI_NAME]["prepaid_amount_total"] +=
                $guest->gift_amount;

            //イベント別合計金額
            $eventNames[self::OKIMOTI_NAME]["amount_total"] +=
                $guest->gift_amount;
        }

        //当日持参の人数合計
        if ($guest->payment_method == PaymentMethodEnum::BRING_ON_THE_DAY) {
            $eventNames[self::OKIMOTI_NAME]["pay_at_venue_count_total"]++;
        }

        //支払い済みの人数合計
        if ($guest->payment_method == PaymentMethodEnum::PAID) {
            $eventNames[self::OKIMOTI_NAME]["paid_count_total"]++;
        }
        return [
            "id" => $guest->id,
            "last_name" => $guest->last_name,
            "first_name" => $guest->first_name,
            "guest_honor" => $guest->guest_honor,
            "payment_method" => $guest->payment_method,
            "amount" => $guest->gift_amount,
            "attendance" => "",
            "is_attendance" => false,
            "date" => null,
            "is_gift_amount" => false, //お気持ちタブではフラグをfalseにする
            "gift_amount" => $gift_amount,
            "is_system_fee" => $guest->is_system_fee,
            "system_fee" => $system_fee ?? 0,
            "child_guests" => [],
        ];
    }

    /**
     * web招待別データを設定
     *
     * @param array $data
     * @param \App\Models\WebInvitation $webInvitation
     * @param int $guest_event_answers_count
     * @param int $payment_amount_total
     * @param int $$total_advance_payment
     * @param array $eventNames
     */
    private function finalizeData(
        &$data,
        $webInvitation,
        $guest_event_answers_count,
        $payment_amount_total,
        $total_advance_payment,
        $eventNames
    ) {
        if (empty($eventNames[self::OKIMOTI_NAME]["guests"])) {
            unset($eventNames[self::OKIMOTI_NAME]);
        }

        $payment_amount_total =
            $payment_amount_total +
            $data[$webInvitation->id]["gift_total_amount"]; //決済金額 + お気持ち
        $data[$webInvitation->id][
            "guest_event_answers_count"
        ] = $guest_event_answers_count; //回答数
        $data[$webInvitation->id]["payment_amount"] = $payment_amount_total;
        $system_amount = floor(
            $total_advance_payment -
                $data[$webInvitation->id]["system_total_fee"]
        ); // 決済金額 * システム手数料率 - ゲストシステム負担額
        $data[$webInvitation->id]["system_amount"] = $system_amount; //システム利用料
        $data[$webInvitation->id]["transfer_amount"] =
            SystemConst::TRANSFER_FEE; //振込事務手数料
        $data[$webInvitation->id]["total_amount"] =
            $payment_amount_total - $system_amount - SystemConst::TRANSFER_FEE; //振込予定額
        $data[$webInvitation->id]["events"] = $eventNames;
    }
}
