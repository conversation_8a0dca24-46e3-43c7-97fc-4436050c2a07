<?php

namespace App\GraphQL\Queries;

use Illuminate\Support\Facades\Auth;
use App\Models\WebInvitation;
use Illuminate\Support\Arr;
use App\Services\CloudFrontSignedCookieService;

final class UrlWebInvitationQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["public_url"]);

        $query = WebInvitation::with("member");

        if (!empty($params["public_url"])) {
            $query->where("public_url", $params["public_url"]);
        }

        $result = $query->first();

        // レコードが無い
        if (!$result) {
            return null;
        }

        // 会員が存在しない（退会など）
        if (empty($result->member)) {
            return null;
        }

        // web招待状非公開
        if ($result->is_public == false) {
            return null;
        }

        $cloudFrontSignedCookieService = new CloudFrontSignedCookieService($result->member_id);
        $cloudFrontSignedCookieService->setSignedCookies();

        return $result;
    }
}
