<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use App\Models\Guest;

//ゲスト一覧取得
final class GuestsQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, [
            "last_name",
            "first_name",
            "guest_group_name",
            "guest_tag_name",
            "orderBy",
        ]);

        $last_name = $params["last_name"];
        $first_name = $params["first_name"];
        $guest_group_name = $params["guest_group_name"];
        $guest_tag_name = $params["guest_tag_name"];
        $orderBy = $params["orderBy"]->value;

        return Guest::where("member_id", Auth::user()->id)
            ->when(!empty($last_name), function ($subQuery) use ($last_name) {
                $subQuery->where("last_name", "LIKE", "%{$last_name}%");
            })
            ->when(!empty($first_name), function ($subQuery) use ($first_name) {
                $subQuery->where("first_name", "LIKE", "%{$first_name}%");
            })
            ->when(!empty($guest_group_name), function ($subQuery) use (
                $guest_group_name
            ) {
                $subQuery->whereHas("guest_list.guest_groups", function (
                    $subQuery
                ) use ($guest_group_name) {
                    $subQuery->where([
                        ["member_id", Auth::user()->id],
                        ["name", "LIKE", "%{$guest_group_name}%"],
                    ]);
                });
            })
            ->when(!empty($guest_tag_name), function ($subQuery) use (
                $guest_tag_name
            ) {
                $subQuery->whereHas("guest_tags", function ($subQuery) use (
                    $guest_tag_name
                ) {
                    $subQuery->where([
                        ["member_id", Auth::user()->id],
                        ["tag", "LIKE", "%{$guest_tag_name}%"],
                    ]);
                });
            })
            ->orderBy("created_at", $orderBy)
            ->get();
    }
}
