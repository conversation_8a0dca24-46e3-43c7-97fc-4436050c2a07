<?php

namespace App\GraphQL\Queries;

use App\Models\ExampleSentence;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use App\Consts\SystemConst;
final class ExampleSentenceQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["example_type1", "example_type2"]);

        $query = ExampleSentence::query();

        if (!empty($params["example_type1"])) {
            $query->where("example_type1", $params["example_type1"]);
        }

        if (!empty($params["example_type2"])) {
            $query->where("example_type2", $params["example_type2"]);
        }

        $query->orderBy("display_order", "asc");

        $exampleSentences = $query->get();
        $exampleContentSeason = [];
        $path = SystemConst::EXAMPLE_CONTENT_SEASON_JSON;
        if (Storage::disk("s3_public")->exists($path)) {
            $json = Storage::disk("s3_public")->get($path);
            $exampleContentSeason = json_decode($json, true);
        }

        return [
            "example_content_season" => $exampleContentSeason,
            "example_sentences" => $exampleSentences,
        ];
    }
}
