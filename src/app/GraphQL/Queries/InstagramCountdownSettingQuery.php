<?php

namespace App\GraphQL\Queries;

use App\Models\InstagramCountdownSetting;
use Illuminate\Support\Facades\Auth;

class InstagramCountdownSettingQuery
{
    /**
     * 認証ユーザーのInstagramカウントダウン画像設定一覧を取得
     *
     * @param null $_
     * @param array $args
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function __invoke($_, array $args)
    {
        $user = Auth::user();
        
        return InstagramCountdownSetting::getByMember($user->id);
    }

    /**
     * 特定のInstagram種別の設定を取得
     *
     * @param null $_
     * @param array $args
     * @return InstagramCountdownSetting|null
     */
    public function findByType($_, array $args)
    {
        $user = Auth::user();
        $instagramType = $args['instagram_type'];
        
        return InstagramCountdownSetting::findByMemberAndType($user->id, $instagramType);
    }
}
