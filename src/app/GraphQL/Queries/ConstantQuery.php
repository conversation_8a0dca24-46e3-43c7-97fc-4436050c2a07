<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use Illuminate\Support\Arr;
use App\Enums\ConstantEnum;
use App\Enums\RelationshipNameEnum;
use App\Enums\RelationshipEnum;

final class ConstantQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $params = Arr::only($args, ["constant"]);

        $constantList = [
            "relation_ship_list" => [],
        ];

        foreach ($params["constant"] ?? [] as $param) {
            $this->processConstant($param->value, $constantList);
        }

        return $constantList;
    }

    /**
     * リクエストの値で取得する定数を切り替える
     *
     * @param string $constantType
     * @param array $constantList
     */
    private function processConstant(
        string $constantType,
        array &$constantList
    ): void {
        switch ($constantType) {
            //間柄
            case ConstantEnum::CONSTANT_RELATIONSHIP:
                $this->addRelationships($constantList);
                break;
        }
    }

    /**
     * 間柄の定数を取得
     *
     * @param array $constantList
     */
    private function addRelationships(array &$constantList): void
    {
        $constantList["relation_ship_list"] = RelationshipEnum::RELATION_LIST;
        // foreach (
        //     RelationshipEnum::RELATION_LIST
        //     as $relationship_name => $constants
        // ) {
        //     foreach ($constants as $constant) {
        //     }
        // }
    }
}
