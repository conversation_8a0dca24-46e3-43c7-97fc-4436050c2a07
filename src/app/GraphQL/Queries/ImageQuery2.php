<?php declare(strict_types=1);

namespace App\GraphQL\Queries;

use App\Models\Image;
use App\Enums\OwnerType;
use App\Enums\FileType;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Models\Member;
use App\Enums\ImageTypeEnum;
use Illuminate\Support\Facades\Config;

final class ImageQuery2
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $credentials = Arr::only($args, ["uuids"]);

        $splittedUuids = [];
        $imageSizeList = [];
        foreach ($credentials["uuids"] as $uuid) {
            $parts = explode("_", $uuid);
            $splittedUuids[$parts[0]] = $parts[0];
            $imageSizeList[$parts[0]][] = isset($parts[1])
                ? "_" . $parts[1]
                : "_m";
        }

        //ファイル情報リクエスト順番通り取得
        $orderByUuids = implode(
            ",",
            array_map(function ($uuid) {
                return "'$uuid'";
            }, $splittedUuids)
        );

        $images = [];
        if (!empty($splittedUuids) && !empty($orderByUuids)) {
            $images = Image::whereIn("uuid", $splittedUuids)
                ->orderByRaw("FIELD(uuid, $orderByUuids)")
                ->get();
        }

        //S3の設定を取得
        $storage = Storage::disk("s3");
        $config = $storage->getConfig();
        if ($config["use_path_style_endpoint"]) {
            $endpoint = Str::match("/https?:\/\/[^\/]*/", $config["url"]);
            $storage = Storage::build(Arr::set($config, "endpoint", $endpoint));
        }

        $mediaConvertNameDecorator = Config::get(
            "services.media_convert.name_decorator"
        );

        $tmpImages = [];
        foreach ($images as $image) {

            //一時署名付きURLパス取得
            if ($image->type == ImageTypeEnum::IMAGE) {
                foreach ($imageSizeList[$image->uuid] ?? [] as $size) {
                    $status = true;
                    $fileUrl = null;
                    $filePath = "";
                    if (
                        $image->file_type >= 1 &&
                        $image->file_type < 30 &&
                        $image->file_type !=
                            FileType::FILE_TYPE_GUEST_UPLOAD_FILE
                    ) {
                        $filePath =
                            env("AWS_DIR_MEMBER") .
                            "{$image->owner_id}/{$image->name}{$size}.{$image->extension_type}";
                    } elseif ($image->file_type >= 30) {
                        $filePath =
                            env("AWS_DIR_ADMIN") .
                            "{$image->name}{$size}.{$image->extension_type}";
                    } else {
                        $filePath =
                            env("AWS_DIR_MEMBER") .
                            "{$image->owner_id}/" .
                            env("AWS_DIR_GUEST") .
                            "{$image->name}{$size}.{$image->extension_type}";
                    }

                    if (!Storage::disk("s3")->exists($filePath)) {
                        $defaultSize = "_m";
                        $defaultFilePath = str_replace(
                            $size,
                            $defaultSize,
                            $filePath
                        );
                        if (!Storage::disk("s3")->exists($defaultFilePath)) {
                            $status = false;
                        } else {
                            $fileUrl = Storage::disk("s3")->url($defaultFilePath);
                        }
                    } else {
                        $fileUrl = Storage::disk("s3")->url($filePath);
                    }
                    $tmpImages["{$image->uuid}{$size}"] = [
                        "uuid" => $image->uuid,
                        "presigned_url" => $fileUrl,
                        "status" => $status,
                        "presigned_url_main" => null,
                        "type" => $image->type,
                    ];
                }
            } else {
                $status = true;
                $fileUrl = null;
                $presignedUrlMain = null;

                $filePathImage = "";
                $filePathVideo = "";
                if (
                    $image->file_type >= 1 &&
                    $image->file_type < 30 &&
                    $image->file_type == FileType::FILE_TYPE_GUEST_UPLOAD_FILE
                ) {
                    $filePathImage =
                        env("AWS_DIR_MEMBER") .
                        "{$image->owner_id}/" .
                        env("AWS_DIR_GUEST") .
                        "{$image->name}_s.jpg";
                    $filePathVideo =
                        env("AWS_DIR_MEMBER") .
                        "{$image->owner_id}/" .
                        env("AWS_DIR_GUEST") .
                        "{$image->name}{$mediaConvertNameDecorator}.{$image->extension_type}";
                } elseif ($image->file_type <= 30) {
                    $filePathImage =
                        env("AWS_DIR_MEMBER") .
                        "{$image->owner_id}/{$image->name}_s.jpg";
                    $filePathVideo =
                        env("AWS_DIR_MEMBER") .
                        "{$image->owner_id}/{$image->name}{$mediaConvertNameDecorator}.{$image->extension_type}";
                }

                if (!Storage::disk("s3")->exists($filePathImage)) {
                    $status = false;
                } else {
                    $fileUrl = Storage::disk("s3")->url($filePathImage);

                }

                if (!Storage::disk("s3")->exists($filePathVideo)) {
                    $status = false;
                } else {
                    $presignedUrlMain = Storage::disk("s3")->url($filePathVideo);
                }

                $tmpImages["{$image->uuid}_main"] = [
                    "uuid" => $image->uuid,
                    "presigned_url" => $fileUrl,
                    "status" => $status,
                    "presigned_url_main" => $presignedUrlMain,
                    "type" => $image->type,
                ];
            }
        }

        return $tmpImages;
    }
}
