<?php

namespace App\GraphQL\Queries;

use Illuminate\Support\Facades\Auth;
use App\Models\Admin;
use App\Models\Member;

final class GetMemberMeQuery
{
    /**
     * @param  null  $_
     * @param  array{}  $args
     */
    public function __invoke($_, array $args)
    {
        $member = Auth::user();
        if ($member && $member instanceof Member) {
            return $member;
        }
        return null;
    }
}
