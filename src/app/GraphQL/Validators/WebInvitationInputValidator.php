<?php declare(strict_types=1);

namespace App\GraphQL\Validators;

use Illuminate\Validation\Rule;
use Nuwave\Lighthouse\Validation\Validator;

final class WebInvitationInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            "public_url" => [
                Rule::unique("web_invitations", "public_url")->ignore(
                    $this->arg("id"),
                    "id"
                ),
                'not_regex:/\.$/',
            ],
        ];
    }

    /**
     * Return the custom validation messages.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            "public_url.unique" => "この招待URLは既に使用されています",
            "public_url.not_regex" =>
                "URLの最後にピリオド (.) をつけることはできません",
        ];
    }
}
