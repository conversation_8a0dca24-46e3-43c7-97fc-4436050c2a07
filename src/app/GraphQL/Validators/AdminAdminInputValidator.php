<?php declare(strict_types=1);

namespace App\GraphQL\Validators;

use Illuminate\Validation\Rule;
use Nuwave\Lighthouse\Validation\Validator;

final class AdminAdminInputValidator extends Validator
{
    /**
     * Return the validation rules.
     *
     * @return array<string, array<mixed>>
     */
    public function rules(): array
    {
        return [
            "email" => [
                Rule::unique("admins", "email")
                    ->ignore($this->arg("id"), "id")
                    ->whereNull("deleted_at"),
            ],
        ];
    }
}
