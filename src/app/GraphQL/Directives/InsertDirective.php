<?php declare(strict_types=1);

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\Relation;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Schema\Directives\MutationExecutorDirective;
use Nuwave\Lighthouse\Execution\Arguments\SaveModel;

final class InsertDirective extends MutationExecutorDirective
{
    // TODO implement the directive https://lighthouse-php.com/master/custom-directives/getting-started.html

    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<'GRAPHQL'
"""
Create a new Eloquent model with the given arguments.
"""
directive @insert(
  """
  Specify the class name of the model to use.
  This is only needed when the default model detection does not work.
  """
  model: String

  """
  Specify the name of the relation on the parent model.
  This is only needed when using this directive as a nested arg
  resolver and if the name of the relation is not the arg name.
  """
  relation: String
) on FIELD_DEFINITION | ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION
GRAPHQL;
    }

    public function resolveField(FieldValue $fieldValue): callable
    {
        $modelClass = $this->getModelClass();

        return function (
            mixed $root,
            array $args,
            GraphQLContext $context,
            ResolveInfo $resolveInfo
        ) use ($modelClass): Model {
            $model = new $modelClass();
            unset($resolveInfo->argumentSet->arguments["id"]);
            return $this->transactionalMutations->execute(function () use (
                $model,
                $resolveInfo
            ): Model {
                $mutated = $this->executeMutation(
                    $model,
                    $resolveInfo->argumentSet
                );
                assert($mutated instanceof Model);

                return $mutated->refresh();
            }, $model->getConnectionName());
        };
    }

    protected function makeExecutionFunction(
        Relation $parentRelation = null
    ): callable {
        return new SaveModel($parentRelation);
    }
}
