<?php

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Exceptions\DefinitionException;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

final class CanAccessDirective extends BaseDirective implements FieldMiddleware
{
    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<GRAPHQL
"""
Abilityによってアクセスを制限します。
"""
directive @canAccess(
  """
  アクセスに必要なアビリティを指定します。
  一旦コメントアウト
  ability: String!
  """
) on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn(callable $resolver) => function (
                mixed $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($resolver) {
                $user = $context->user();

                //認証エラー
                if (!$user) {
                    throw new AuthenticationException();
                }
                //フィールド名を取得
                $fieldName = substr($resolveInfo->fieldDefinition->name, 0, 5);

                //モデル名を取得
                $modelName = class_basename($context->user());

                //認証チェック
                if (
                    ($fieldName === "admin" && $modelName === "Admin") ||
                    ($fieldName !== "admin" && $modelName === "Member")
                ) {
                    return $resolver($root, $args, $context, $resolveInfo);
                }

                throw new AuthenticationException();
            }
        );
    }
}
