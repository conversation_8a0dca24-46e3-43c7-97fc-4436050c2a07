<?php declare(strict_types=1);

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Exceptions\DefinitionException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

final class CheckMemberSpoofingDirective extends BaseDirective implements
    FieldMiddleware
{
    // TODO implement the directive https://lighthouse-php.com/master/custom-directives/getting-started.html

    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<'GRAPHQL'
directive @checkMemberSpoofing on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn(callable $resolver) => function (
                mixed $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($resolver) {
                $user = $context->user();

                if (!$user) {
                    throw new AuthenticationException();
                }

                if (!class_exists($this->getModelClass())) {
                    throw new DefinitionException(
                        "Class {$this->getModelClass()()} does not exist"
                    );
                }

                //モデル名を取得
                $modelName = class_basename($context->user());

                if ($modelName != "Admin" && isset($args["member_id"])) {
                    throw new AuthenticationException();
                }

                return $resolver($root, $args, $context, $resolveInfo);
            }
        );
    }
}
