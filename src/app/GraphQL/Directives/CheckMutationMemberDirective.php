<?php declare(strict_types=1);

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Exceptions\DefinitionException;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use GraphQL\Error\Error;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Model;
use Nuwave\Lighthouse\Execution\Arguments\Argument;
use Nuwave\Lighthouse\Execution\Arguments\UpdateModel;

final class CheckMutationMemberDirective extends BaseDirective implements
    FieldMiddleware
{
    // TODO implement the directive https://lighthouse-php.com/master/custom-directives/getting-started.html

    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<'GRAPHQL'
"""
会員自身以外のデータにはアクセスできないようにする
"""
directive @checkMutationMember on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn(callable $resolver) => function (
                mixed $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($resolver) {
                $user = $context->user();

                if (!$user) {
                    throw new AuthenticationException();
                }
                $user_id = $user->id ?? 0;

                if (!class_exists($this->getModelClass())) {
                    throw new DefinitionException(
                        "Class {$this->getModelClass()()} does not exist"
                    );
                }

                $className = $this->getModelClass();
                $model = new $className();

                $id =
                    Arr::pull($args, "id") ??
                    (Arr::pull($args, $model->getKeyName()) ??
                        throw new Error(
                            "Missing primary key for update or delete."
                        ));
                $model = $model->newQuery()->findOrFail($id);
                if (isset($model->member_id) && $model->member_id != $user_id) {
                    throw new AuthenticationException();
                }

                return $resolver($root, $args, $context, $resolveInfo);
            }
        );
    }
}
