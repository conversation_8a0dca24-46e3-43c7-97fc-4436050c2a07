<?php declare(strict_types=1);

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;
use GraphQL\Error\Error;
use Illuminate\Database\Eloquent\Model;
use GraphQL\Language\AST\FieldDefinitionNode;
use GraphQL\Language\AST\InterfaceTypeDefinitionNode;
use GraphQL\Language\AST\ObjectTypeDefinitionNode;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;
use Laravel\Scout\Builder as ScoutBuilder;
use Nuwave\Lighthouse\Schema\AST\DocumentAST;
use Nuwave\Lighthouse\Support\Contracts\FieldManipulator;
use Nuwave\Lighthouse\Support\Contracts\FieldResolver;
use Illuminate\Database\Eloquent\Builder;

final class CheckQueryMemberDirective extends BaseDirective implements
    FieldMiddleware
{
    // TODO implement the directive https://lighthouse-php.com/master/custom-directives/getting-started.html

    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<'GRAPHQL'
"""
会員自身以外のデータにはアクセスできないようにする
"""
directive @checkQueryMember on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn(callable $resolver) => function (
                mixed $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($resolver) {
                $user = $context->user();

                if (!$user) {
                    throw new AuthenticationException();
                }
                $user_id = $user->id ?? 0;

                /** @phpstan-ignore-next-line */
                $results = $resolveInfo
                    ->enhanceBuilder(
                        $this->getModelClass()::query(),
                        $this->directiveArgValue("scopes", []),
                        $root,
                        $args,
                        $context,
                        $resolveInfo
                    )
                    ->get();

                foreach ($results as $result) {
                    if (
                        isset($result->member_id) &&
                        $result->member_id != $user_id
                    ) {
                        throw new AuthenticationException();
                    }
                }

                return $resolver($root, $args, $context, $resolveInfo);
            }
        );
    }
}
