<?php declare(strict_types=1);

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Execution\ResolveInfo;
use App\Services\CloudFrontSignedCookieService;
use Illuminate\Support\Facades\Auth;

// CloudFront署名付きCookieの生成
final class CloudFrontCookieDirective extends BaseDirective implements
    FieldMiddleware
{
    // TODO implement the directive https://lighthouse-php.com/master/custom-directives/getting-started.html

    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<'GRAPHQL'
directive @cloudFrontCookie on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn(callable $resolver) => function (
                mixed $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($resolver) {
                $user_id = $context->user()->id ?? null;
                if($user_id){
                    $cloudFrontSignedCookieService = new CloudFrontSignedCookieService($user_id);
                    $cloudFrontSignedCookieService->setSignedCookies();
                }
                return $resolver($root, $args, $context, $resolveInfo);
            }
        );
    }
}
