<?php declare(strict_types=1);

namespace App\GraphQL\Directives;

use Nuwave\Lighthouse\Execution\ResolveInfo;
use Nuwave\Lighthouse\Exceptions\DefinitionException;
use Nuwave\Lighthouse\Schema\Directives\BaseDirective;
use Nuwave\Lighthouse\Schema\Values\FieldValue;
use Nuwave\Lighthouse\Support\Contracts\FieldMiddleware;
use Nuwave\Lighthouse\Support\Contracts\GraphQLContext;
use Nuwave\Lighthouse\Exceptions\AuthenticationException;

final class CanAccessSpoofingDirective extends BaseDirective implements
    FieldMiddleware
{
    // TODO implement the directive https://lighthouse-php.com/master/custom-directives/getting-started.html

    public static function definition(): string
    {
        return /** @lang GraphQL */ <<<'GRAPHQL'
directive @canAccessSpoofing on FIELD_DEFINITION
GRAPHQL;
    }

    public function handleField(FieldValue $fieldValue): void
    {
        $fieldValue->wrapResolver(
            fn(callable $resolver) => function (
                mixed $root,
                array $args,
                GraphQLContext $context,
                ResolveInfo $resolveInfo
            ) use ($resolver) {
                $user = $context->user();

                //認証エラー
                if (!$user) {
                    throw new AuthenticationException();
                }
                //フィールド名を取得
                $fieldName = substr($resolveInfo->fieldDefinition->name, 0, 5);

                //モデル名を取得
                $modelName = class_basename($context->user());

                //管理者APIに会員はアクセスできない
                if ($fieldName === "admin" && $modelName === "Member") {
                    throw new AuthenticationException();
                }

                return $resolver($root, $args, $context, $resolveInfo);
            }
        );
    }
}
