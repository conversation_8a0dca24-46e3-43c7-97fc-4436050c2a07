<?php declare(strict_types=1);

namespace App\Enums;

use <PERSON><PERSON>amp<PERSON>\Enum\Enum;

//ファイル種別
final class FileType extends Enum
{
    const FILE_TYPE_MEMBER_MATERIAL = 10; //会員アップロード素材
    const FILE_TYPE_MEMBER_ETC = 11; //会員アップロードその他
    const FILE_TYPE_GUEST_UPLOAD_FILE = 12; // ゲストアップロードファイル
    const FILE_TYPE_WEB_INVITATION_MATERIAL = 13; // Web招待状素材
    const FILE_TYPE_MATERIAL = 31; //素材画像
    const FILE_TYPE_PRODUCT_MASTER = 32; //商品マスタ
    const FILE_TYPE_GUEST_CELEBRATION_IMAGE = 33; //ゲスト向けお祝い用画像素材
    const FILE_TYPE_ETC_MASTER = 99; //マスタその他

    /**
     * 指定のファイル種別が対象のリストに含まれているか判定
     *
     * @return bool
     */
    public static function isNotAuthTargetFileType($value): bool
    {
        return in_array($value, [
            self::FILE_TYPE_MATERIAL,
            self::FILE_TYPE_PRODUCT_MASTER,
            self::FILE_TYPE_GUEST_CELEBRATION_IMAGE,
        ]);
    }
}
