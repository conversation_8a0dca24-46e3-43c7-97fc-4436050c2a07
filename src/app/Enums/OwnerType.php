<?php declare(strict_types=1);

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static OptionOne()
 * @method static static OptionTwo()
 * @method static static OptionThree()
 */
final class OwnerType extends Enum
{
    const OWNER_TYPE_MEMBER = 1; //会員
    const OWNER_TYPE_ADMIN = 2; //管理者

    const OWNER_TYPE_LIST = [
        "Member" => self::OWNER_TYPE_MEMBER,
        "Admin" => self::OWNER_TYPE_ADMIN,
    ];

    public static function getValue($key): mixed
    {
        return self::OWNER_TYPE_LIST[$key] ?? 0;
    }
}
