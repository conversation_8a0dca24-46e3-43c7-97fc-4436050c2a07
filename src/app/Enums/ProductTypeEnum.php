<?php declare(strict_types=1);

namespace App\Enums;

use BenSampo\Enum\Enum;

final class ProductTypeEnum extends Enum
{
    const NORMAL_PRODUCT = 1; //通常商品(物販)
    const SET_PRODUCT = 2; //セット商品
    const INVITATION = 3; //招待状
    const SEATING_CHART = 4; //席次表
    const WELCOME_BOARD = 5; //ウェルカムボード
    const PETIT_GIFT = 6; //プチギフト
    const MARRIAGE_ANNOUNCEMENT_CARD = 7; //結婚報告はがき
    const GIFT_DELIVERY_ARRANGEMENT = 8; //引き出物宅配手配
    const WEB_INVITATION = 11; //Web招待状
    const DOWNLOAD_PRODUCT = 21; //ダウンロード商品
}
