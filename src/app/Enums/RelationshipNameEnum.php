<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

//関係性
final class RelationshipNameEnum extends Enum
{
    // 関係性
    const RELATIONSHIP_NAME_FRIEND = "友人";
    const RELATIONSHIP_NAME_COMPANY = "会社";
    const RELATIONSHIP_NAME_FAMILY = "家族";

    // 関係性リスト
    const RELATIONSHIP_NAME_LIST = [
        self::RELATIONSHIP_NAME_FRIEND => self::RELATIONSHIP_NAME_FRIEND,
        self::RELATIONSHIP_NAME_COMPANY => self::RELATIONSHIP_NAME_COMPANY,
        self::REL<PERSON>IONSHIP_NAME_FAMILY => self::RELATIONSHIP_NAME_FAMILY,
    ];
}
