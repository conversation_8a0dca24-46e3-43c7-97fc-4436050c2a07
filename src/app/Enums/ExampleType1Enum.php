<?php declare(strict_types=1);

namespace App\Enums;

use BenSampo\Enum\Enum;

final class ExampleType1Enum extends Enum
{
    const EDITOR_GREETING = 1; //エディターの挨拶・メッセージ
    const EDITOR_GROOM_PROFILE = 2; //エディターの新郎プロフィールのメッセージ欄
    const EDITOR_BRIDE_PROFILE = 3; //エディターの新婦プロフィールのメッセージ欄
    const EDITOR_FAMILY_PROFILE = 4; //エディターの家族プロフィールのメッセージ欄
    const MESSAGE_TO_GUESTS = 5; //「ゲストに送る」のメッセージ欄
    const MESSAGE_FREE = 6; //フリー項目
}
