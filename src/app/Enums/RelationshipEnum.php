<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use App\Enums\RelationshipNameEnum;

//間柄
final class RelationshipEnum extends Enum
{
    // 間柄 友人
    const RELATION_FRIEND = 1; // 友人
    const RELATION_CHILDHOOD_FRIEND = 2; // 幼なじみ
    const RELATION_UNIVERSITY_FRIEND = 3; // 大学友人
    const RELATION_VOCATIONAL_FRIEND = 4; // 専門学校友人
    const RELATION_HIGH_SCHOOL_FRIEND = 5; // 高校友人
    const RELATION_MIDDLE_SCHOOL_FRIEND = 6; // 中学友人
    const RELATION_UNIVERSITY_SENIOR = 7; // 大学先輩
    const RELATION_HIGH_SCHOOL_SENIOR = 8; // 高校先輩
    const RELATION_MIDDLE_SCHOOL_SENIOR = 9; // 中学先輩
    const RELATION_UNIVERSITY_JUNIOR = 10; // 大学後輩
    const RELATION_HIGH_SCHOOL_JUNIOR = 11; // 高校後輩
    const RELATION_MIDDLE_SCHOOL_JUNIOR = 12; // 中学後輩
    const RELATION_TEACHER = 13; // 恩師
    const RELATION_ACQUAINTANCE = 14; // 知人

    // 間柄 会社関係
    const RELATION_PRESIDENT = 15; // 社長
    const RELATION_SUPERVISOR = 16; // 上司
    const RELATION_SENIOR_COLLEAGUE = 17; // 先輩
    const RELATION_COLLEAGUE = 18; // 同僚
    const RELATION_FORMER_COLLEAGUE = 19; // 元同僚
    const RELATION_JUNIOR_COLLEAGUE = 20; // 後輩
    const RELATION_OTHER = 21; // その他

    // 間柄 親族
    const RELATION_FATHER_CODE = 22; // 父
    const RELATION_MOTHER_CODE = 23; // 母
    const RELATION_ELDER_BROTHER_CODE = 24; // 兄
    const RELATION_YOUNGER_BROTHER_CODE = 25; // 弟
    const RELATION_ELDER_SISTER_CODE = 26; // 姉
    const RELATION_YOUNGER_SISTER_CODE = 27; // 妹
    const RELATION_BROTHER_IN_LAW_CODE = 28; // 義兄
    const RELATION_YOUNGER_BROTHER_IN_LAW_CODE = 29; // 義弟
    const RELATION_SISTER_IN_LAW_CODE = 30; // 義姉
    const RELATION_YOUNGER_SISTER_IN_LAW_CODE = 31; // 義妹
    const RELATION_NEPHEW_CODE = 32; // 甥
    const RELATION_NIECE_CODE = 33; // 姪
    const RELATION_GRANDFATHER_CODE = 34; // 祖父
    const RELATION_GRANDMOTHER_CODE = 35; // 祖母
    const RELATION_PATERNAL_UNCLE_CODE = 36; // 伯父（父母の兄）
    const RELATION_PATERNAL_AUNT_CODE = 37; // 伯母（父母の姉）
    const RELATION_MATERNAL_UNCLE_CODE = 38; // 叔父（父母の弟）
    const RELATION_MATERNAL_AUNT_CODE = 39; // 叔母（父母の妹）
    const RELATION_COUSIN_ELDER_CODE = 40; // 従兄（年上のいとこ）
    const RELATION_COUSIN_YOUNGER_CODE = 41; // 従弟（年下のいとこ）
    const RELATION_COUSIN_ELDER_SISTER_CODE = 42; // 従姉（年上のいとこ）
    const RELATION_COUSIN_YOUNGER_SISTER_CODE = 43; // 従妹（年下のいとこ）
    const RELATION_COUSIN_NEPHEW_CODE = 44; // 従甥
    const RELATION_COUSIN_NIECE_CODE = 45; // 従姪
    const RELATION_GRAND_UNCLE_CODE = 46; // 大伯父（祖父母の兄）
    const RELATION_GRAND_AUNT_CODE = 47; // 大伯母（祖父母の姉）
    const RELATION_GRAND_UNCLE_YOUNGER_CODE = 48; // 大叔父（祖父母の弟）
    const RELATION_GRAND_AUNT_YOUNGER_CODE = 49; // 大叔母（祖父母の妹）
    const RELATION_RELATIVE_CODE = 50; // 親戚
    const RELATION_SON_CODE = 51; // 息子
    const RELATION_DAUGHTER_CODE = 52; // 娘

    //間柄リスト
    const RELATION_LIST = [
        // 友人
        [
            "category" => RelationshipNameEnum::RELATIONSHIP_NAME_FRIEND,
            "options" => [
                ["id" => self::RELATION_FRIEND, "name" => "友人"],
                ["id" => self::RELATION_CHILDHOOD_FRIEND, "name" => "幼なじみ"],
                [
                    "id" => self::RELATION_UNIVERSITY_FRIEND,
                    "name" => "大学友人",
                ],
                [
                    "id" => self::RELATION_VOCATIONAL_FRIEND,
                    "name" => "専門学校友人",
                ],
                [
                    "id" => self::RELATION_HIGH_SCHOOL_FRIEND,
                    "name" => "高校友人",
                ],
                [
                    "id" => self::RELATION_MIDDLE_SCHOOL_FRIEND,
                    "name" => "中学友人",
                ],
                [
                    "id" => self::RELATION_UNIVERSITY_SENIOR,
                    "name" => "大学先輩",
                ],
                [
                    "id" => self::RELATION_HIGH_SCHOOL_SENIOR,
                    "name" => "高校先輩",
                ],
                [
                    "id" => self::RELATION_MIDDLE_SCHOOL_SENIOR,
                    "name" => "中学先輩",
                ],
                [
                    "id" => self::RELATION_UNIVERSITY_JUNIOR,
                    "name" => "大学後輩",
                ],
                [
                    "id" => self::RELATION_HIGH_SCHOOL_JUNIOR,
                    "name" => "高校後輩",
                ],
                [
                    "id" => self::RELATION_MIDDLE_SCHOOL_JUNIOR,
                    "name" => "中学後輩",
                ],
                ["id" => self::RELATION_TEACHER, "name" => "恩師"],
                ["id" => self::RELATION_ACQUAINTANCE, "name" => "知人"],
            ],
        ],
        // 会社関係
        [
            "category" => RelationshipNameEnum::RELATIONSHIP_NAME_COMPANY,
            "options" => [
                ["id" => self::RELATION_PRESIDENT, "name" => "社長"],
                ["id" => self::RELATION_SUPERVISOR, "name" => "上司"],
                ["id" => self::RELATION_SENIOR_COLLEAGUE, "name" => "先輩"],
                ["id" => self::RELATION_COLLEAGUE, "name" => "同僚"],
                ["id" => self::RELATION_FORMER_COLLEAGUE, "name" => "元同僚"],
                ["id" => self::RELATION_JUNIOR_COLLEAGUE, "name" => "後輩"],
                ["id" => self::RELATION_OTHER, "name" => "その他"],
            ],
        ],
        // 親族
        [
            "category" => RelationshipNameEnum::RELATIONSHIP_NAME_FAMILY,
            "options" => [
                ["id" => self::RELATION_FATHER_CODE, "name" => "父"],
                ["id" => self::RELATION_MOTHER_CODE, "name" => "母"],
                ["id" => self::RELATION_ELDER_BROTHER_CODE, "name" => "兄"],
                ["id" => self::RELATION_YOUNGER_BROTHER_CODE, "name" => "弟"],
                ["id" => self::RELATION_ELDER_SISTER_CODE, "name" => "姉"],
                ["id" => self::RELATION_YOUNGER_SISTER_CODE, "name" => "妹"],
                ["id" => self::RELATION_BROTHER_IN_LAW_CODE, "name" => "義兄"],
                [
                    "id" => self::RELATION_YOUNGER_BROTHER_IN_LAW_CODE,
                    "name" => "義弟",
                ],
                ["id" => self::RELATION_SISTER_IN_LAW_CODE, "name" => "義姉"],
                [
                    "id" => self::RELATION_YOUNGER_SISTER_IN_LAW_CODE,
                    "name" => "義妹",
                ],
                ["id" => self::RELATION_NEPHEW_CODE, "name" => "甥"],
                ["id" => self::RELATION_NIECE_CODE, "name" => "姪"],
                ["id" => self::RELATION_GRANDFATHER_CODE, "name" => "祖父"],
                ["id" => self::RELATION_GRANDMOTHER_CODE, "name" => "祖母"],
                [
                    "id" => self::RELATION_PATERNAL_UNCLE_CODE,
                    "name" => "伯父（父母の兄）",
                ],
                [
                    "id" => self::RELATION_PATERNAL_AUNT_CODE,
                    "name" => "伯母（父母の姉）",
                ],
                [
                    "id" => self::RELATION_MATERNAL_UNCLE_CODE,
                    "name" => "叔父（父母の弟）",
                ],
                [
                    "id" => self::RELATION_MATERNAL_AUNT_CODE,
                    "name" => "叔母（父母の妹）",
                ],
                [
                    "id" => self::RELATION_COUSIN_ELDER_CODE,
                    "name" => "従兄（年上のいとこ）",
                ],
                [
                    "id" => self::RELATION_COUSIN_YOUNGER_CODE,
                    "name" => "従弟（年下のいとこ）",
                ],
                [
                    "id" => self::RELATION_COUSIN_ELDER_SISTER_CODE,
                    "name" => "従姉（年上のいとこ）",
                ],
                [
                    "id" => self::RELATION_COUSIN_YOUNGER_SISTER_CODE,
                    "name" => "従妹（年下のいとこ）",
                ],
                ["id" => self::RELATION_COUSIN_NEPHEW_CODE, "name" => "従甥"],
                ["id" => self::RELATION_COUSIN_NIECE_CODE, "name" => "従姪"],
                [
                    "id" => self::RELATION_GRAND_UNCLE_CODE,
                    "name" => "大伯父（祖父母の兄）",
                ],
                [
                    "id" => self::RELATION_GRAND_AUNT_CODE,
                    "name" => "大伯母（祖父母の姉）",
                ],
                [
                    "id" => self::RELATION_GRAND_UNCLE_YOUNGER_CODE,
                    "name" => "大叔父（祖父母の弟）",
                ],
                [
                    "id" => self::RELATION_GRAND_AUNT_YOUNGER_CODE,
                    "name" => "大叔母（祖父母の妹）",
                ],
                ["id" => self::RELATION_RELATIVE_CODE, "name" => "親戚"],
                ["id" => self::RELATION_SON_CODE, "name" => "息子"],
                ["id" => self::RELATION_DAUGHTER_CODE, "name" => "娘"],
            ],
        ],
    ];

    /**
     * 各キーに対応する配列を返す
     *
     * @param string $relationshipName 配列のキー
     * @return array
     */
    public static function getRelationship(string $relationshipName): array
    {
        return self::RELATION_LIST[$relationshipName] ?? [];
    }

    /**
     * 定数のそれぞれの値を文章で持つ
     *
     * @return array
     */
    public static function getRelationNames(): array
    {
        return [
            "父",
            "母",
            "兄",
            "弟",
            "姉",
            "妹",
            "義姉",
            "義兄",
            "義妹",
            "義弟",
            "甥",
            "姪",
            "祖父",
            "祖母",
            "大伯父",
            "大伯母",
            "大叔父",
            "大叔母",
            "伯父",
            "伯母",
            "叔父",
            "叔母",
            "従兄",
            "従姉",
            "従弟",
            "従妹",
            "従甥",
            "従姪",
            "親戚",
            "配偶者",
            "息子",
            "娘",
            "幼なじみ",
            "友人",
            "知人",
            "大学先輩",
            "高校先輩",
            "中学先輩",
            "大学同級生",
            "高校同級生",
            "中学同級生",
            "大学後輩",
            "高校後輩",
            "中学後輩",
            "恩師",
            "社長",
            "上司",
            "先輩",
            "同僚",
            "元同僚",
            "後輩",
            "その他",
        ];
    }
}
