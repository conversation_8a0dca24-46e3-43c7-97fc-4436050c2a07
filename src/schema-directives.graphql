# File generated by "php artisan lighthouse:ide-helper".
# Do not edit this file directly.
# This file should be ignored by git as it can be autogenerated.

"""
Placeholder type for various directives such as `@orderBy`.
Will be replaced by a generated type during schema manipulation.
"""
scalar _

# Directive class: Nuwave\Lighthouse\Schema\Directives\AggregateDirective
"""
Returns an aggregate of a column in a given relationship or model.
"""
directive @aggregate(
    """
    The column to aggregate.
    """
    column: String!

    """
    The aggregate function to compute.
    """
    function: AggregateFunction!

    """
    The relationship with the column to aggregate.
    Mutually exclusive with `model` and `builder`.
    """
    relation: String

    """
    The model with the column to aggregate.
    Mutually exclusive with `relation` and `builder`.
    """
    model: String

    """
    Point to a function that provides a Query Builder instance.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    Mutually exclusive with `relation` and `model`.
    """
    builder: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

"""
Options for the `function` argument of `@aggregate`.
"""
enum AggregateFunction {
    """
    Return the average value.
    """
    AVG

    """
    Return the sum.
    """
    SUM

    """
    Return the minimum.
    """
    MIN

    """
    Return the maximum.
    """
    MAX
}

# Directive class: Nuwave\Lighthouse\Schema\Directives\AllDirective
"""
Fetch all Eloquent models and return the collection as the result.
"""
directive @all(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    Mutually exclusive with `builder`.
    """
    model: String

    """
    Point to a function that provides a Query Builder instance.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    Mutually exclusive with `model`.
    """
    builder: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\BelongsToDirective
"""
Resolves a field through the Eloquent `BelongsTo` relationship.
"""
directive @belongsTo(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\BelongsToManyDirective
"""
Resolves a field through the Eloquent `BelongsToMany` relationship.
"""
directive @belongsToMany(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Allows to resolve the relation as a paginated list.
    """
    type: BelongsToManyType

    """
    Allow clients to query paginated lists without specifying the amount of items.
    Overrules the `pagination.default_count` setting from `lighthouse.php`.
    Setting this to `null` means clients have to explicitly ask for the count.
    """
    defaultCount: Int

    """
    Limit the maximum amount of items that clients can request from paginated lists.
    Overrules the `pagination.max_count` setting from `lighthouse.php`.
    Setting this to `null` means the count is unrestricted.
    """
    maxCount: Int

    """
    Specify a custom type that implements the Edge interface
    to extend edge object.
    Only applies when using Relay style "connection" pagination.
    """
    edgeType: String
) on FIELD_DEFINITION

"""
Options for the `type` argument of `@belongsToMany`.
"""
enum BelongsToManyType {
    """
    Offset-based pagination, similar to the Laravel default.
    """
    PAGINATOR

    """
    Offset-based pagination like the Laravel "Simple Pagination", which does not count the total number of records.
    """
    SIMPLE

    """
    Cursor-based pagination, compatible with the Relay specification.
    """
    CONNECTION
}

# Directive class: Nuwave\Lighthouse\Schema\Directives\BuilderDirective
"""
Manipulate the query builder with a method.
"""
directive @builder(
    """
    Reference a method that is passed the query builder.
    Consists of two parts: a class name and a method name, separated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    method: String!

    """
    Pass a value to the method as the second argument after the query builder.
    Only used when the directive is added on a field.
    """
    value: BuilderValue
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

"""
Any constant literal value: https://graphql.github.io/graphql-spec/draft/#sec-Input-Values
"""
scalar BuilderValue

# Directive class: Nuwave\Lighthouse\Schema\Directives\ComplexityDirective
"""
Customize the calculation of a fields complexity score before execution.
"""
directive @complexity(
    """
    Reference a function to customize the complexity score calculation.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    resolver: String!
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\ConvertEmptyStringsToNullDirective
"""
Replaces `""` with `null`.
"""
directive @convertEmptyStringsToNull on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\CountDirective
"""
Returns the count of a given relationship or model.
"""
directive @count(
    """
    The relationship to count.
    Mutually exclusive with `model`.
    """
    relation: String

    """
    The model to count.
    Mutually exclusive with `relation`.
    """
    model: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Count only rows where the given columns are non-null.
    `*` counts every row.
    """
    columns: [String!]! = ["*"]

    """
    Should exclude duplicated rows?
    """
    distinct: Boolean! = false
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\CreateDirective
"""
Create a new Eloquent model with the given arguments.
"""
directive @create(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Specify the name of the relation on the parent model.
    This is only needed when using this directive as a nested arg
    resolver and if the name of the relation is not the arg name.
    """
    relation: String
) on FIELD_DEFINITION | ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\DeleteDirective
"""
Delete one or more models.
"""
directive @delete(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Specify the name of the relation on the parent model.
    This is only needed when using this directive as a nested arg
    resolver and if the name of the relation is not the arg name.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION | ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\DeprecatedDirective
"""
Marks an element of a GraphQL schema as no longer supported.
"""
directive @deprecated(
    """
    Explains why this element was deprecated, usually also including a
    suggestion for how to access supported similar data. Formatted
    in [Markdown](https://daringfireball.net/projects/markdown).
    """
    reason: String = "No longer supported"
) on FIELD_DEFINITION | ENUM_VALUE

# Directive class: Nuwave\Lighthouse\Schema\Directives\DropArgsDirective
"""
Apply the @drop directives on the incoming arguments.
"""
directive @dropArgs on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\DropDirective
"""
Ignore the user given value, don't pass it to the resolver.
"""
directive @drop on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\EnumDirective
"""
Assign an internal value to an enum key.
When dealing with the Enum type in your code,
you will receive the defined value instead of the string key.
"""
directive @enum(
    """
    The internal value of the enum key.
    """
    value: EnumValue
) on ENUM_VALUE

"""
Any constant literal value: https://graphql.github.io/graphql-spec/draft/#sec-Input-Values
"""
scalar EnumValue

# Directive class: Nuwave\Lighthouse\Schema\Directives\EqDirective
"""
Add an equal conditional to a database query.
"""
directive @eq(
    """
    Specify the database column to compare.
    Required if the directive is:
    - used on an argument and the database column has a different name
    - used on a field
    """
    key: String

    """
    Provide a value to compare against.
    Exclusively required when this directive is used on a field.
    """
    value: EqValue
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

"""
Any constant literal value: https://graphql.github.io/graphql-spec/draft/#sec-Input-Values
"""
scalar EqValue

# Directive class: Nuwave\Lighthouse\Schema\Directives\EventDirective
"""
Dispatch an event after the resolution of a field.

The event constructor will be called with a single argument:
the resolved value of the field.
"""
directive @event(
    """
    Specify the fully qualified class name (FQCN) of the event to dispatch.
    """
    dispatch: String!
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\FieldDirective
"""
Assign a resolver function to a field.
"""
directive @field(
    """
    A reference to the resolver function to be used.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    resolver: String!
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\FindDirective
"""
Find a model based on the arguments provided.
"""
directive @find(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\FirstDirective
"""
Get the first query result from a collection of Eloquent models.
"""
directive @first(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\HasManyDirective
"""
Corresponds to [the Eloquent relationship HasMany](https://laravel.com/docs/eloquent-relationships#one-to-many).
"""
directive @hasMany(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Allows to resolve the relation as a paginated list.
    """
    type: HasManyType

    """
    Allow clients to query paginated lists without specifying the amount of items.
    Overrules the `pagination.default_count` setting from `lighthouse.php`.
    Setting this to `null` means clients have to explicitly ask for the count.
    """
    defaultCount: Int

    """
    Limit the maximum amount of items that clients can request from paginated lists.
    Overrules the `pagination.max_count` setting from `lighthouse.php`.
    Setting this to `null` means the count is unrestricted.
    """
    maxCount: Int

    """
    Specify a custom type that implements the Edge interface
    to extend edge object.
    Only applies when using Relay style "connection" pagination.
    """
    edgeType: String
) on FIELD_DEFINITION

"""
Options for the `type` argument of `@hasMany`.
"""
enum HasManyType {
    """
    Offset-based pagination, similar to the Laravel default.
    """
    PAGINATOR

    """
    Offset-based pagination like the Laravel "Simple Pagination", which does not count the total number of records.
    """
    SIMPLE

    """
    Cursor-based pagination, compatible with the Relay specification.
    """
    CONNECTION
}

# Directive class: Nuwave\Lighthouse\Schema\Directives\HasManyThroughDirective
"""
Corresponds to [the Eloquent relationship HasManyThrough](https://laravel.com/docs/eloquent-relationships#has-many-through).
"""
directive @hasManyThrough(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Allows to resolve the relation as a paginated list.
    """
    type: HasManyThroughType

    """
    Allow clients to query paginated lists without specifying the amount of items.
    Overrules the `pagination.default_count` setting from `lighthouse.php`.
    """
    defaultCount: Int

    """
    Limit the maximum amount of items that clients can request from paginated lists.
    Overrules the `pagination.max_count` setting from `lighthouse.php`.
    """
    maxCount: Int

    """
    Specify a custom type that implements the Edge interface
    to extend edge object.
    Only applies when using Relay style "connection" pagination.
    """
    edgeType: String
) on FIELD_DEFINITION

"""
Options for the `type` argument of `@hasManyThrough`.
"""
enum HasManyThroughType {
    """
    Offset-based pagination, similar to the Laravel default.
    """
    PAGINATOR

    """
    Offset-based pagination like the Laravel "Simple Pagination", which does not count the total number of records.
    """
    SIMPLE

    """
    Cursor-based pagination, compatible with the Relay specification.
    """
    CONNECTION
}

# Directive class: Nuwave\Lighthouse\Schema\Directives\HasOneDirective
"""
Corresponds to [the Eloquent relationship HasOne](https://laravel.com/docs/eloquent-relationships#one-to-one).
"""
directive @hasOne(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\HashDirective
"""
Use Laravel hashing to transform an argument value.

Useful for hashing passwords before inserting them into the database.
This uses the default hashing driver defined in `config/hashing.php`.
"""
directive @hash on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\HideDirective
"""
Excludes the annotated element from the schema conditionally.
"""
directive @hide(
    """
    Specify which environments must not use this field, e.g. ["production"].
    """
    env: [String!]!
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\InDirective
"""
Use the client given list value to add an IN conditional to a database query.
"""
directive @in(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\InjectDirective
"""
Inject a value from the context object into the arguments.
"""
directive @inject(
    """
    A path to the property of the context that will be injected.
    If the value is nested within the context, you may use dot notation
    to get it, e.g. "user.id".
    """
    context: String!

    """
    The target name of the argument into which the value is injected.
    You can use dot notation to set the value at arbitrary depth
    within the incoming argument.
    """
    name: String!
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\InterfaceDirective
"""
Use a custom resolver to determine the concrete type of an interface.
"""
directive @interface(
    """
    Reference to a custom type-resolver function.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    resolveType: String!
) on INTERFACE

# Directive class: Nuwave\Lighthouse\Schema\Directives\LazyLoadDirective
"""
Perform a [lazy eager load](https://laravel.com/docs/eloquent-relationships#lazy-eager-loading)
on the relations of a list of models.
"""
directive @lazyLoad(
    """
    The names of the relationship methods to load.
    """
    relations: [String!]!
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\LikeDirective
"""
Add a `LIKE` conditional to a database query.
"""
directive @like(
    """
    Specify the database column to compare.
    Required if the directive is:
    - used on an argument and the database column has a different name
    - used on a field
    """
    key: String

    """
    Fixate the positions of wildcards (`%`, `_`) in the LIKE comparison around the
    placeholder `{}`, e.g. `%{}`, `__{}` or `%{}%`.
    If specified, wildcard characters in the client-given input are escaped.
    If not specified, the client can pass wildcards unescaped.
    """
    template: String

    """
    Provide a value to compare against.
    Only used when the directive is added on a field.
    """
    value: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\LimitDirective
"""
Allow clients to specify the maximum number of results to return.
"""
directive @limit on ARGUMENT_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\MethodDirective
"""
Resolve a field by calling a method on the parent object.

Use this if the data is not accessible through simple property access or if you
want to pass argument to the method.
"""
directive @method(
    """
    Specify the method of which to fetch the data from.
    Defaults to the name of the field if not given.
    """
    name: String
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\ModelDirective
"""
Map a model class to an object type.

This can be used when the name of the model differs from the name of the type.
"""
directive @model(
    """
    The class name of the corresponding model.
    """
    class: String!
) on OBJECT

# Directive class: Nuwave\Lighthouse\Schema\Directives\MorphManyDirective
"""
Corresponds to [Eloquent's MorphMany-Relationship](https://laravel.com/docs/eloquent-relationships#one-to-many-polymorphic-relations).
"""
directive @morphMany(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Allows to resolve the relation as a paginated list.
    """
    type: MorphManyType

    """
    Allow clients to query paginated lists without specifying the amount of items.
    Overrules the `pagination.default_count` setting from `lighthouse.php`.
    Setting this to `null` means clients have to explicitly ask for the count.
    """
    defaultCount: Int

    """
    Limit the maximum amount of items that clients can request from paginated lists.
    Overrules the `pagination.max_count` setting from `lighthouse.php`.
    Setting this to `null` means the count is unrestricted.
    """
    maxCount: Int

    """
    Specify a custom type that implements the Edge interface
    to extend edge object.
    Only applies when using Relay style "connection" pagination.
    """
    edgeType: String
) on FIELD_DEFINITION

"""
Options for the `type` argument of `@morphMany`.
"""
enum MorphManyType {
    """
    Offset-based pagination, similar to the Laravel default.
    """
    PAGINATOR

    """
    Offset-based pagination like the Laravel "Simple Pagination", which does not count the total number of records.
    """
    SIMPLE

    """
    Cursor-based pagination, compatible with the Relay specification.
    """
    CONNECTION
}

# Directive class: Nuwave\Lighthouse\Schema\Directives\MorphOneDirective
"""
Corresponds to [Eloquent's MorphOne-Relationship](https://laravel.com/docs/eloquent-relationships#one-to-one-polymorphic-relations).
"""
directive @morphOne(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\MorphToDirective
"""
Corresponds to [Eloquent's MorphTo-Relationship](https://laravel.com/docs/eloquent-relationships#one-to-one-polymorphic-relations).
"""
directive @morphTo(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\MorphToManyDirective
"""
Corresponds to [Eloquent's ManyToMany-Polymorphic-Relationship](https://laravel.com/docs/eloquent-relationships#many-to-many-polymorphic-relations).
"""
directive @morphToMany(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Allows to resolve the relation as a paginated list.
    """
    type: MorphToManyType

    """
    Allow clients to query paginated lists without specifying the amount of items.
    Overrules the `pagination.default_count` setting from `lighthouse.php`.
    Setting this to `null` means clients have to explicitly ask for the count.
    """
    defaultCount: Int

    """
    Limit the maximum amount of items that clients can request from paginated lists.
    Overrules the `pagination.max_count` setting from `lighthouse.php`.
    Setting this to `null` means the count is unrestricted.
    """
    maxCount: Int

    """
    Specify a custom type that implements the Edge interface
    to extend edge object.
    Only applies when using Relay style "connection" pagination.
    """
    edgeType: String
) on FIELD_DEFINITION

"""
Options for the `type` argument of `@morphToMany`.
"""
enum MorphToManyType {
    """
    Offset-based pagination, similar to the Laravel default.
    """
    PAGINATOR

    """
    Offset-based pagination like the Laravel "Simple Pagination", which does not count the total number of records.
    """
    SIMPLE

    """
    Cursor-based pagination, compatible with the Relay specification.
    """
    CONNECTION
}

# Directive class: Nuwave\Lighthouse\Schema\Directives\NamespaceDirective
"""
Redefine the default namespaces used in other directives.
The arguments are a map from directive names to namespaces.
"""
directive @namespace repeatable on FIELD_DEFINITION | OBJECT

# Directive class: Nuwave\Lighthouse\Schema\Directives\NeqDirective
"""
Use the client given value to add an not-equal conditional to a database query.
"""
directive @neq(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\NestDirective
"""
A no-op nested arg resolver that delegates all calls
to the ArgResolver directives attached to the children.
"""
directive @nest on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\NotInDirective
"""
Use the client given value to add a NOT IN conditional to a database query.
"""
directive @notIn(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\RenameArgsDirective
"""
Apply the @rename directives on the incoming arguments.
"""
directive @renameArgs on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\RenameDirective
"""
Change the internally used name of a field or argument.

This does not change the schema from a client perspective.
"""
directive @rename(
    """
    The internal name of an attribute/property/key.
    """
    attribute: String!
) on FIELD_DEFINITION | ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\SanitizeDirective
"""
Apply sanitization to the arguments of a field.
"""
directive @sanitize on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\ScalarDirective
"""
Reference a class implementing a scalar definition.
"""
directive @scalar(
    """
    Reference to a class that extends `\GraphQL\Type\Definition\ScalarType`.
    """
    class: String!
) on SCALAR

# Directive class: Nuwave\Lighthouse\Schema\Directives\ScopeDirective
"""
Adds a scope to the query builder.

The scope method will receive the client-given value of the argument as the second parameter.
"""
directive @scope(
    """
    The name of the scope.
    Defaults to the name of the argument.
    """
    name: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\ShowDirective
"""
Includes the annotated element from the schema conditionally.
"""
directive @show(
    """
    Specify which environments may use this field, e.g. ["testing"].
    """
    env: [String!]!
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\SpreadDirective
"""
Merge the fields of a nested input object into the arguments of its parent
when processing the field arguments given by a client.
"""
directive @spread on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\ThrottleDirective
"""
Sets rate limit to access the field. Does the same as ThrottleRequests Laravel Middleware.
"""
directive @throttle(
    """
    Named preconfigured rate limiter.
    """
    name: String

    """
    Maximum number of attempts in a specified time interval.
    """
    maxAttempts: Int = 60

    """
    Time in minutes to reset attempts.
    """
    decayMinutes: Float = 1.0

    """
    Prefix to distinguish several field groups.
    """
    prefix: String
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\TransformArgsDirective
"""
Transform the arguments of a field.
"""
directive @transformArgs on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\TrimDirective
"""
Remove whitespace from the beginning and end of a given input.

This can be used on:
- a single argument or input field to sanitize that subtree
- a field to trim all strings
"""
directive @trim on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\UnionDirective
"""
Use a custom function to determine the concrete type of unions.
"""
directive @union(
    """
    Reference a function that returns the implementing Object Type.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    resolveType: String!
) on UNION

# Directive class: Nuwave\Lighthouse\Schema\Directives\UpdateDirective
"""
Update an Eloquent model with the input values of the field.
"""
directive @update(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Specify the name of the relation on the parent model.
    This is only needed when using this directive as a nested arg
    resolver and if the name of the relation is not the arg name.
    """
    relation: String
) on FIELD_DEFINITION | ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\UploadDirective
"""
Uploads given file to storage, removes the argument and sets
the returned path to the attribute key provided.

This does not change the schema from a client perspective.
"""
directive @upload(
    """
    The storage disk to be used, defaults to config value `filesystems.default`.
    """
    disk: String

    """
    The path where the file should be stored.
    """
    path: String! = "/"

    """
    Should the visibility be public?
    """
    public: Boolean! = false
) on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\UpsertDirective
"""
Create or update an Eloquent model with the input values of the field.
"""
directive @upsert(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Specify the name of the relation on the parent model.
    This is only needed when using this directive as a nested arg
    resolver and if the name of the relation is not the arg name.
    """
    relation: String
) on FIELD_DEFINITION | ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereBetweenDirective
"""
Verify that a column's value is between two values.

The type of the input value this is defined upon should be
an `input` object with two fields.
"""
directive @whereBetween(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereDirective
"""
Use an input value as a [where filter](https://laravel.com/docs/queries#where-clauses).
"""
directive @where(
    """
    Specify the operator to use within the WHERE condition.
    """
    operator: String = "="

    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String

    """
    Use Laravel's where clauses upon the query builder.
    This only works for clauses with the signature (string $column, string $operator, mixed $value).
    """
    clause: String

    """
    Provide a value to compare against.
    Exclusively required when this directive is used on a field.
    """
    value: WhereValue
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

"""
Any constant literal value: https://graphql.github.io/graphql-spec/draft/#sec-Input-Values
"""
scalar WhereValue

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereJsonContainsDirective
"""
Use an input value as a [whereJsonContains filter](https://laravel.com/docs/queries#json-where-clauses).
"""
directive @whereJsonContains(
    """
    Specify the database column and path inside the JSON to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereKeyDirective
"""
Add a where clause on the primary key to the Eloquent Model query.
"""
directive @whereKey(
    """
    Provide a value to compare against.
    Exclusively required when this directive is used on a field.
    """
    value: WhereKeyValue
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

"""
Any constant literal value: https://graphql.github.io/graphql-spec/draft/#sec-Input-Values
"""
scalar WhereKeyValue

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereNotBetweenDirective
"""
Verify that a column's value lies outside two values.

The type of the input value this is defined upon should be
an `input` object with two fields.
"""
directive @whereNotBetween(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereNotNullDirective
"""
Filter the value is not null.
"""
directive @whereNotNull(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String

    """
    Should the value not be null?
    Exclusively required when this directive is used on a field.
    """
    value: Boolean
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WhereNullDirective
"""
Filter the value is null.
"""
directive @whereNull(
    """
    Specify the database column to compare.
    Only required if database column has a different name than the attribute in your schema.
    """
    key: String

    """
    Should the value be null?
    Exclusively required when this directive is used on a field.
    """
    value: Boolean
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WithCountDirective
"""
Eager-load the count of an Eloquent relation if the field is queried.

Note that this does not return a value for the field, the count is simply
prefetched, assuming it is used to compute the field value. Use `@count`
if the field should simply return the relation count.
"""
directive @withCount(
    """
    Specify the relationship method name in the model class.
    """
    relation: String!

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Schema\Directives\WithDirective
"""
Eager-load an Eloquent relation.
"""
directive @with(
    """
    Specify the relationship method name in the model class,
    if it is named different from the field in the schema.
    """
    relation: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) repeatable on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Auth\AuthDirective
"""
Return the currently authenticated user as the result of a query.
"""
directive @auth(
    """
    Specify which guards to use, e.g. ["api"].
    When not defined, the default from `lighthouse.php` is used.
    """
    guards: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Auth\CanDirective
"""
Check a Laravel Policy to ensure the current user is authorized to access a field.

When `injectArgs` and `args` are used together, the client given
arguments will be passed before the static args.
"""
directive @can(
    """
    The ability to check permissions for.
    """
    ability: String!

    """
    Check the policy against the model instances returned by the field resolver.
    Only use this if the field does not mutate data, it is run before checking.

    Mutually exclusive with `query` and `find`.
    """
    resolved: Boolean! = false

    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Pass along the client given input data as arguments to `Gate::check`.
    """
    injectArgs: Boolean! = false

    """
    Statically defined arguments that are passed to `Gate::check`.

    You may pass arbitrary GraphQL literals,
    e.g.: [1, 2, 3] or { foo: "bar" }
    """
    args: CanArgs

    """
    Query for specific model instances to check the policy against, using arguments
    with directives that add constraints to the query builder, such as `@eq`.

    Mutually exclusive with `resolved` and `find`.
    """
    query: Boolean! = false

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    If your policy checks against specific model instances, specify
    the name of the field argument that contains its primary key(s).

    You may pass the string in dot notation to use nested inputs.

    Mutually exclusive with `resolved` and `query`.
    """
    find: String

    """
    Should the query fail when the models of `find` were not found?
    """
    findOrFail: Boolean! = true
) repeatable on FIELD_DEFINITION

"""
Any constant literal value: https://graphql.github.io/graphql-spec/draft/#sec-Input-Values
"""
scalar CanArgs

# Directive class: Nuwave\Lighthouse\Auth\GuardDirective
"""
Run authentication through one or more guards.

This is run per field and may allow unauthenticated
users to still receive partial results.

Used upon an object, it applies to all fields within.
"""
directive @guard(
    """
    Specify which guards to use, e.g. ["web"].
    When not defined, the default from `lighthouse.php` is used.
    """
    with: [String!]
) repeatable on FIELD_DEFINITION | OBJECT

# Directive class: Nuwave\Lighthouse\Auth\WhereAuthDirective
"""
Filter a type to only return instances owned by the current user.
"""
directive @whereAuth(
    """
    Name of the relationship that links to the user model.
    """
    relation: String!

    """
    Specify which guards to use, e.g. ["api"].
    When not defined, the default from `lighthouse.php` is used.
    """
    guards: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Cache\CacheDirective
"""
Cache the result of a resolver.

Place this after other field middleware to ensure it caches the correct transformed value.
"""
directive @cache(
    """
    Set the duration it takes for the cache to expire in seconds.
    If not given, the result will be stored forever.
    """
    maxAge: Int

    """
    Limit access to cached data to the currently authenticated user.
    When the field is accessible by guest users, this will not have
    any effect, they will access a shared cache.
    """
    private: Boolean = false
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Cache\CacheKeyDirective
"""
Specify the field to use as a key when creating a cache.
"""
directive @cacheKey on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Cache\ClearCacheDirective
"""
Clear a resolver cache by tags.
"""
directive @clearCache(
    """
    Name of the parent type of the field to clear.
    """
    type: String!

    """
    Source of the parent ID to clear.
    """
    idSource: ClearCacheIdSource

    """
    Name of the field to clear.
    """
    field: String
) repeatable on FIELD_DEFINITION

"""
Options for the `id` argument on `@clearCache`.

Exactly one of the fields must be given.
"""
input ClearCacheIdSource {
    """
    Path of an argument the client passes to the field `@clearCache` is applied to.
    """
    argument: String

    """
    Path of a field in the result returned from the field `@clearCache` is applied to.
    """
    field: String
}

# Directive class: Nuwave\Lighthouse\GlobalId\GlobalIdDirective
"""
Converts between IDs/types and global IDs.

When used upon a field, it encodes;
when used upon an argument, it decodes.
"""
directive @globalId(
    """
    Decoding a global id produces a tuple of `$type` and `$id`.
    This setting controls which of those is passed along.
    """
    decode: GlobalIdDecode = ARRAY
) on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ARGUMENT_DEFINITION

"""
Options for the `decode` argument of `@globalId`.
"""
enum GlobalIdDecode {
    """
    Return an array of `[$type, $id]`.
    """
    ARRAY

    """
    Return just `$type`.
    """
    TYPE

    """
    Return just `$id`.
    """
    ID
}

# Directive class: Nuwave\Lighthouse\GlobalId\NodeDirective
"""
Register a type for Relay's global object identification.

When used without any arguments, Lighthouse will attempt
to resolve the type through a model with the same name.
"""
directive @node(
    """
    Reference to a function that receives the decoded `id` and returns a result.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.

    Mutually exclusive with `model`.
    """
    resolver: String

    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.

    Mutually exclusive with `resolver`.
    """
    model: String
) on OBJECT

# Directive class: Nuwave\Lighthouse\OrderBy\OrderByDirective
"""
Sort a result list by one or more given columns.
"""
directive @orderBy(
    """
    Restrict the allowed column names to a well-defined list.
    This improves introspection capabilities and security.
    Mutually exclusive with `columnsEnum`.
    Only used when the directive is added on an argument.
    """
    columns: [String!]

    """
    Use an existing enumeration type to restrict the allowed columns to a predefined list.
    This allows you to re-use the same enum for multiple fields.
    Mutually exclusive with `columns`.
    Only used when the directive is added on an argument.
    """
    columnsEnum: String

    """
    Allow clients to sort by aggregates on relations.
    Only used when the directive is added on an argument.
    """
    relations: [OrderByRelation!]

    """
    The database column for which the order by clause will be applied on.
    Only used when the directive is added on a field.
    """
    column: String

    """
    The direction of the order by clause.
    Only used when the directive is added on a field.
    """
    direction: OrderByDirection = ASC
) on ARGUMENT_DEFINITION | FIELD_DEFINITION

"""
Options for the `direction` argument on `@orderBy`.
"""
enum OrderByDirection {
    """
    Sort in ascending order.
    """
    ASC

    """
    Sort in descending order.
    """
    DESC
}

"""
Options for the `relations` argument on `@orderBy`.
"""
input OrderByRelation {
    """
    Name of the relation.
    """
    relation: String!

    """
    Restrict the allowed column names to a well-defined list.
    This improves introspection capabilities and security.
    Mutually exclusive with `columnsEnum`.
    """
    columns: [String!]

    """
    Use an existing enumeration type to restrict the allowed columns to a predefined list.
    This allows you to re-use the same enum for multiple fields.
    Mutually exclusive with `columns`.
    """
    columnsEnum: String
}

# Directive class: Nuwave\Lighthouse\Pagination\PaginateDirective
"""
Query multiple entries as a paginated list.
"""
directive @paginate(
    """
    Which pagination style should be used.
    """
    type: PaginateType = PAGINATOR

    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    Mutually exclusive with `builder` and `resolver`.
    """
    model: String

    """
    Point to a function that provides a Query Builder instance.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    Mutually exclusive with `model` and `resolver`.
    """
    builder: String

    """
    Reference a function that resolves the field by directly returning data in a Paginator instance.
    Mutually exclusive with `builder` and `model`.
    Not compatible with `scopes` and builder arguments such as `@eq`.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    resolver: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]

    """
    Allow clients to query paginated lists without specifying the amount of items.
    Overrules the `pagination.default_count` setting from `lighthouse.php`.
    Setting this to `null` means clients have to explicitly ask for the count.
    """
    defaultCount: Int

    """
    Limit the maximum amount of items that clients can request from paginated lists.
    Overrules the `pagination.max_count` setting from `lighthouse.php`.
    Setting this to `null` means the count is unrestricted.
    """
    maxCount: Int

    """
    Reference a function to customize the complexity score calculation.
    Consists of two parts: a class name and a method name, seperated by an `@` symbol.
    If you pass only a class name, the method name defaults to `__invoke`.
    """
    complexityResolver: String
) on FIELD_DEFINITION

"""
Options for the `type` argument of `@paginate`.
"""
enum PaginateType {
    """
    Offset-based pagination, similar to the Laravel default.
    """
    PAGINATOR

    """
    Offset-based pagination like the Laravel "Simple Pagination", which does not count the total number of records.
    """
    SIMPLE

    """
    Cursor-based pagination, compatible with the Relay specification.
    """
    CONNECTION
}

# Directive class: Nuwave\Lighthouse\SoftDeletes\ForceDeleteDirective
"""
Permanently remove one or more soft deleted models.
"""
directive @forceDelete(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\SoftDeletes\RestoreDirective
"""
Un-delete one or more soft deleted models.
"""
directive @restore(
    """
    Specify the class name of the model to use.
    This is only needed when the default model detection does not work.
    """
    model: String

    """
    Apply scopes to the underlying query.
    """
    scopes: [String!]
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\SoftDeletes\SoftDeletesDirective
"""
Allows to filter if trashed elements should be fetched.
This manipulates the schema by adding the argument
`trashed: Trashed @trashed` to the field.
"""
directive @softDeletes on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\SoftDeletes\TrashedDirective
"""
Allows to filter if trashed elements should be fetched.
"""
directive @trashed on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Testing\MockDirective
"""
Allows you to easily hook up a resolver for an endpoint.
"""
directive @mock(
    """
    Specify a unique key for the mock resolver.
    """
    key: String = "default"
) on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Validation\RulesDirective
"""
Validate an argument using [Laravel validation](https://laravel.com/docs/validation).
"""
directive @rules(
    """
    Specify the validation rules to apply to the field.
    This can either be a reference to [Laravel's built-in validation rules](https://laravel.com/docs/validation#available-validation-rules),
    or the fully qualified class name of a custom validation rule.

    Rules that mutate the incoming arguments, such as `exclude_if`, are not supported
    by Lighthouse. Use ArgTransformerDirectives or FieldMiddlewareDirectives instead.
    """
    apply: [String!]!

    """
    Specify a custom attribute name to use in your validation message.
    """
    attribute: String

    """
    Specify the messages to return if the validators fail.
    """
    messages: [RulesMessage!]
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

"""
Input for the `messages` argument of `@rules`.
"""
input RulesMessage {
    """
    Name of the rule, e.g. `"email"`.
    """
    rule: String!

    """
    Message to display if the rule fails, e.g. `"Must be a valid email"`.
    """
    message: String!
}

# Directive class: Nuwave\Lighthouse\Validation\RulesForArrayDirective
"""
Run validation on an array itself, using [Laravel built-in validation](https://laravel.com/docs/validation).
"""
directive @rulesForArray(
    """
    Specify the validation rules to apply to the field.
    This can either be a reference to any of Laravel's built-in validation rules: https://laravel.com/docs/validation#available-validation-rules,
    or the fully qualified class name of a custom validation rule.
    """
    apply: [String!]!

    """
    Specify a custom attribute name to use in your validation message.
    """
    attribute: String

    """
    Specify the messages to return if the validators fail.
    """
    messages: [RulesForArrayMessage!]
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

"""
Input for the `messages` argument of `@rulesForArray`.
"""
input RulesForArrayMessage {
    """
    Name of the rule, e.g. `"email"`.
    """
    rule: String!

    """
    Message to display if the rule fails, e.g. `"Must be a valid email"`.
    """
    message: String!
}

# Directive class: Nuwave\Lighthouse\Validation\ValidateDirective
"""
Run validation on a field.
"""
directive @validate on FIELD_DEFINITION

# Directive class: Nuwave\Lighthouse\Validation\ValidatorDirective
"""
Provide validation rules through a PHP class.
"""
directive @validator(
    """
    The name of the class to use.

    If defined on an input, this defaults to a class called `{$inputName}Validator` in the
    default validator namespace. For fields, it uses the namespace of the parent type
    and the field name: `{$parent}\{$field}Validator`.
    """
    class: String
) repeatable on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION | FIELD_DEFINITION | INPUT_OBJECT
