APP_NAME=favori
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=database
DB_USERNAME=root
DB_PASSWORD=password

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_CONTACT_ADDRESS="<EMAIL>"
MAIL_BCC_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
MAIL_FRONT_URL_RESET="https://localhost/reset-password"
MAIL_FRONT_URL_TMP_MEMBER="https://localhost/tmp-member"
FROM_MAIL_DEVELOPER_ADDRESS=<EMAIL> # 開発者メールアドレス 2025/05/08
TO_MAIL_DEVELOPER_ADDRESS=<EMAIL>,<EMAIL> #開発者のメールアドレス 2025/05/08

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"


AWS_ACCESS_KEY_ID=user #minio ユーザー名
AWS_SECRET_ACCESS_KEY=password #minio パスワード
AWS_DEFAULT_REGION=ap-northeast-1 #リージョン
AWS_BUCKET=test #minio or S3に同じバケット名を作成
AWS_USE_PATH_STYLE_ENDPOINT=true #minio true S3 false
AWS_ENDPOINT=http://minio:9001
AWS_URL=http://localhost:9000
AWS_URL_MEMBER_EXPIRATION=60 #会員ディレクト以下の有効期限
AWS_URL_ADMIN_EXPIRATION=1440 #管理人ディレクトリ以下の有効期限
AWS_DIR_ADMIN="admin/"
AWS_DIR_MEMBER="member/"
AWS_DIR_GUEST="guest/"




GOOGLE_CLIENT_ID=535593718142-provem9ghrpf7nvip9vv3h0o8tpesrqp.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=http://localhost/api/auth/google/callback

LINE_CLIENT_ID=2003334559
LINE_CLIENT_SECRET=1d2c3365bc1f69db8d381cf74263b57f
LINE_CALLBACK_URL=http://localhost/api/auth/line/callback
LINE_APP_STATE=favori_dev

SNS_LOGIN_URL="http://localhost:3000/login"
SNS_REGISTER_URL="http://localhost:3000/register"

# GMO Payment
GMO_PG_API_ENDPOINT=https://pt01.mul-pay.jp/
GMO_PG_SHOP_ID=tshop00024104
GMO_PG_SHOP_PASS=35yzuzh6


# 動画関連
FFMPEG_BINARIES=/usr/bin/ffmpeg
FFPROBE_BINARIES=/usr/bin/ffprobe

# APIキー
API_KEY=b2Hy8mF4qL9p

# cloundFrontの環境変数
AWS_CLOUDFRONT_KEY_PAIR_ID=KACBLCCVPKTK5
AWS_CLOUDFRONT_PRIVATE_KEY_PATH=app/secrets/cloudfront/private_key.pem
AWS_CLOUDFRONT_RESOURCE_URL=https://dev8695.cloudfront.favori.wedding/*
AWS_CLOUDFRONT_DOWNLOAD_URL=https://dev8695.cloudfront.favori.wedding
AWS_CLOUDFRONT_COOKIE_DOMAIN=.favori.wedding
AWS_CLOUDFRONT_COOKIE_SECURE=true
CLOUDFRONT_COOKIE_EXPIRY=86400

# MediaConvert アクセスロール
AWS_MEDIACONVERT_DECORATOR_ROLE='arn:aws:iam::613841076301:role/service-role/MediaConvert_Default_Role'

# MediaConvert ファイル名の名前装飾子
AWS_MEDIACONVERT_DECORATOR='_hls'
