<?php

namespace Database\Factories;

use App\Models\Member;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Member>
 */
class MemberFactory extends Factory
{
    protected $model = Member::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'last_name' => $this->faker->lastName,
            'first_name' => $this->faker->firstName,
            'email' => $this->faker->unique()->safeEmail,
            'email_verified_at' => now(),
            'password' => bcrypt('password'),
            'wedding_date' => $this->faker->date(),
            'wedding_venue' => $this->faker->numberBetween(1, 3),
            'guest_count' => $this->faker->numberBetween(1, 300),
            'questionnaire_type' => $this->faker->numberBetween(1, 300),
            'questionnaire_other' => $this->faker->realText($maxNbChars = 35),
        ];
    }
}
