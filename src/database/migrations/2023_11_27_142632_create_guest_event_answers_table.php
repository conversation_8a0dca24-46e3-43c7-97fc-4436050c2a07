<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_event_answers', function (Blueprint $table) {
            $table->comment('ゲストイベント別回答');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('guest_id')->comment('ゲストID');
            $table->string('name')->nullable()->comment('イベント名');
            $table->date('date')->nullable()->comment('イベント日付');
            $table->unsignedInteger('payment_amount')->nullable()->default(0)->comment('支払金額');
            $table->unsignedTinyInteger('attendance')->nullable()->default(0)->comment('出欠');
            $table->unsignedTinyInteger('payment_method')->nullable()->default(0)->comment('支払方法');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('guest_id')->references('id')->on('guests');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_event_answers');
    }
};
