<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('money_transfers', function (Blueprint $table) {
            $table->decimal('guest_system_fee', 10, 2)->nullable()->default(null)->comment('ゲストシステム料')->change();
            $table->dropColumn('member_system_fee');
        });
    }

    /**
     * Reverse the migrations.
     *∆
     * @return void
     */
    public function down()
    {
        Schema::table('money_transfers', function (Blueprint $table) {
            $table->decimal('guest_system_fee', 10, 2)->nullable()->default(0)->comment('ゲストシステム料')->change();
            $table->decimal('member_system_fee', 10, 2)->nullable()->default(0)->comment('会員システム料')->after('system_fee');
        });
    }
};
