<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use App\Enums\SnsKind;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('members', function (Blueprint $table) {
            $table->comment('会員（新郎・新婦）マスタ');

            $table->uuid('id')->primary();
            $table->string('last_name')->nullable()->comment('姓');
            $table->string('first_name')->nullable()->comment('名');
            $table->string('last_name_kana')->nullable()->comment('姓(カナ)');
            $table->string('first_name_kana')->nullable()->comment('名(カナ)');
            $table->string('last_name_romaji')->nullable()->comment('姓(ローマ字)');
            $table->string('first_name_romaji')->nullable()->comment('名(ローマ字)');
            $table->string('email')->unique()->nullable()->comment('メールアドレス');
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable()->comment('パスワード');
            $table->date('birthdate')->nullable()->comment("生年月日");
            $table->boolean('is_regist')->default(false)->comment("本会員登録FLG");
            $table->string('tmp_uuid')->nullable()->comment("仮登録UUID");
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('members');
    }
};
