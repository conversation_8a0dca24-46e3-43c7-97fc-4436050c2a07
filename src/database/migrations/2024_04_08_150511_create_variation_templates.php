<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('variation_templates', function (Blueprint $table) {
            $table->comment('バリエーションテンプレート');

            $table->uuid('id')->primary();
            $table->string('name')->nullable()->comment('テンプレート名称');
            $table->uuid('basic_variation_template_id')->comment('基本バリエーションテンプレートID (FK)');
            $table->unsignedTinyInteger('product_type')->nullable()->comment('商品種別');
            $table->json('json_data')->nullable()->comment('JSONデータ');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('basic_variation_template_id')->references('id')->on('basic_variation_templates');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('variation_templates');
    }
};
