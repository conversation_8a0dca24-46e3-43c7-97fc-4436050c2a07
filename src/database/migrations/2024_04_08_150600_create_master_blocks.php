<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('master_blocks', function (Blueprint $table) {
            $table->comment('マスターブロック');
            $table->uuid('id')->primary();
            $table->string('name')->nullable()->comment('ブロック名称');
            $table->unsignedTinyInteger('product_type')->nullable()->comment('商品種別');
            $table->json('json_data')->nullable()->comment('JSONデータ');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('master_blocks');
    }
};
