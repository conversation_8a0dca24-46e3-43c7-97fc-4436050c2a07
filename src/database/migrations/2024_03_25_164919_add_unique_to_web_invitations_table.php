<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->string('public_url')->nullable()->unique()->comment('公開URL')->change();
            $table->foreign('m_web_invitation_id')->references('id')->on('m_web_invitations');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->string('public_url')->nullable()->comment('公開URL')->change();
        });
    }
};
