<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->date('scheduled_transfer_date')->nullable()->comment('送金予定日時')->after('is_public');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->dropColumn('scheduled_transfer_date');
        });
    }
};
