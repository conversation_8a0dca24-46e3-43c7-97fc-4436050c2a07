<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_search_condition_items', function (Blueprint $table) {
            $table->comment('商品検索条件項目');
            $table->uuid('id')->primary();
            $table->uuid('product_search_condition_id')->comment('検索条件ID');
            $table->integer('display_order')->comment('表示順');
            $table->string('label')->comment('検索条件項目表記名');
            $table->string('value')->comment('検索条件項目値');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約を追加する場合はここに追加します

            $table->foreign('product_search_condition_id', 'fk_product_search_condition_items_product_search_condition')
                ->references('id')
                ->on('product_search_conditions');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_search_condition_items');
    }
};
