<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->dropColumn('is_member_confirmed');
            $table->unsignedTinyInteger('member_confirm_type')->default(0)->comment('会員確認済種別')->after('web_invite_reply_datetime');
            $table->unsignedTinyInteger('media_type')->default(0)->comment('お祝い画像・動画の種類 (画像か動画かのフラグ)')->after('message');
            $table->uuid('media_uuid')->nullable()->comment('お祝い画像・動画のファイルUUID')->after('media_type');
            $table->string('relationship_name')->nullable()->comment('関係性')->after('relationship');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->boolean('is_member_confirmed')->default(false)->comment('会員確認済種別')->after('web_invite_reply_datetime');
            $table->dropColumn('member_confirm_type');
            $table->dropColumn('media_type');
            $table->dropColumn('media_uuid');
            $table->dropColumn('relationship_name');
        });
    }
};
