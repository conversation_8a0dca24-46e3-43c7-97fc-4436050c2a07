<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('systems', function (Blueprint $table) {
            $table->comment('システム設定');
            $table->uuid('id')->primary();
            $table->dateTime('prepayment_pause_from_at')->nullable()->comment('事前支払い一時停止開始日時');
            $table->dateTime('prepayment_pause_to_at')->nullable()->comment('事前支払い一時停止終了日時');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('systems');
    }
};
