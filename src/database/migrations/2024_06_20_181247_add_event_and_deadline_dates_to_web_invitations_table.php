<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->date('scheduled_date')->nullable()->comment('開催日')->after('is_public');
            $table->date('reply_deadline_date')->nullable()->comment('締切日')->after('scheduled_date');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->dropColumn('scheduled_date');
            $table->dropColumn('reply_deadline_date');
        });
    }
};
