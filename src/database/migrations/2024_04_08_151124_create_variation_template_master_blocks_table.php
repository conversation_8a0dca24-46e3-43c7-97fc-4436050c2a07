<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('variation_template_master_blocks', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->comment('バリエーションテンプレート - マスターブロック');
            $table->uuid('variation_template_id')->comment('バリエーションテンプレートID (FK)');
            $table->uuid('master_block_id')->comment('マスターブロックID (FK)');
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('variation_template_id')->references('id')->on('variation_templates');
            $table->foreign('master_block_id')->references('id')->on('master_blocks');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('variation_template_master_blocks');
    }
};
