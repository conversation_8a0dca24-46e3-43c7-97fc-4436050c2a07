<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_option_products', function (Blueprint $table) {
            $table->comment('引き出物オプション商品マスタ');
            $table->uuid('id')->primary();
            $table->string('name')->comment('オプション商品名');
            $table->text('description')->nullable()->comment('説明');
            $table->string('image')->comment('画像');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_option_products');
    }
};
