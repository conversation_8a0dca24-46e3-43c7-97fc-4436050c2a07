<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guest_survey_answers', function (Blueprint $table) {
            $table->text('question')->nullable()->comment('質問内容')->after('ui_type');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guest_survey_answers', function (Blueprint $table) {
            $table->dropColumn('question');
        });
    }
};
