<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('web_invitation_design_images', function (Blueprint $table) {
            $table->comment('WEB招待状デザイン画像');
            $table->uuid('id')->primary();
            $table->uuid('m_web_invitation_id')->constrained('m_web_invitations')->comment('WEB招待状マスタID');
            $table->string('upload_file_name')->nullable()->comment('画像ファイル名(アップロード時)');
            $table->string('file_name')->nullable()->comment('画像ファイル名');
            $table->string('file_format')->nullable()->comment('画像ファイル形式');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('m_web_invitation_id')->references('id')->on('m_web_invitations');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('web_invitation_design_images');
    }
};
