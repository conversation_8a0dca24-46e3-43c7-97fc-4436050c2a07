<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_title_masters', function (Blueprint $table) {
            $table->comment('ゲスト肩書リスト初期値マスタ');
            $table->uuid('id')->primary();
            $table->string('name')->nullable()->comment('肩書名');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_title_masters');
    }
};
