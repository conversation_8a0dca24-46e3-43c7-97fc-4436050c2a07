<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guests', function (Blueprint $table) {
            $table->comment('ゲスト');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('guest_list_id')->comment('ゲストリストID');
            $table->uuid('guest_group_id')->nullable()->comment('ゲストグループID');
            $table->uuid('parent_guest_id')->nullable()->comment('連名者ID');

            $table->string('last_name')->nullable()->comment('姓');
            $table->string('first_name')->nullable()->comment('名');
            $table->string('last_name_kana')->nullable()->comment('姓(カナ)');
            $table->string('first_name_kana')->nullable()->comment('名(カナ)');
            $table->string('last_name_romaji')->nullable()->comment('姓(ローマ字)');
            $table->string('first_name_romaji')->nullable()->comment('名(ローマ字)');
            $table->tinyInteger('gender')->comment('性別');
            $table->tinyInteger('attendance')->nullable()->comment('出欠');
            $table->text('allergy')->nullable()->comment('アレルギー');
            $table->date('birthdate')->nullable()->comment('生年月日');
            $table->string('image_url')->nullable()->comment('プロフィール画像');
            $table->string('postal_code')->nullable()->comment('郵便番号');
            $table->string('prefecture')->nullable()->comment('都道府県');
            $table->string('city')->nullable()->comment('市区町村');
            $table->string('address')->nullable()->comment('丁目・番地');
            $table->string('building')->nullable()->comment('建物名・部屋番号');
            $table->string('phone')->nullable()->comment('電話番号');
            $table->string('email')->nullable()->comment('メールアドレス');
            $table->text('message')->nullable()->comment('メッセージ');
            $table->tinyInteger('invitation_delivery')->nullable()->default(0)->comment('招待状お届け方法');
            $table->string('guest_title')->nullable()->comment('肩書');
            $table->string('guest_honor')->nullable()->comment('敬称');
            $table->string('relationship')->nullable()->comment('間柄');
            $table->dateTime('web_invite_reply_datetime')->nullable()->comment('Web招待状返信日時');
            $table->boolean('is_member_confirmed')->default(false)->comment('会員確認済FLG');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('guest_list_id')->references('id')->on('guest_lists');
            // $table->foreign('parent_guest_id')->references('id')->on('guests');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guests');
    }
};
