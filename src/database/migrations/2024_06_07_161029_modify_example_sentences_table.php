<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('example_sentences', function (Blueprint $table) {
            $table->dropColumn('product_type');
            $table->dropColumn('example_type');
            $table->unsignedTinyInteger('example_type1')->nullable()->comment('文例集種別1')->after("id");
            $table->unsignedTinyInteger('example_type2')->nullable()->comment('文例集種別2')->after("example_type1");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('example_sentences', function (Blueprint $table) {
            $table->dropColumn('example_type1');
            $table->dropColumn('example_type2');
            $table->unsignedTinyInteger('product_type')->nullable()->comment('商品種別')->after("id");
            $table->unsignedTinyInteger('example_type')->nullable()->comment('文例集種別')->after("product_type");
        });
    }
};
