<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tags', function (Blueprint $table) {
            $table->comment('タグマスタ');
            $table->uuid('id')->primary();
            $table->uuid('tag_group_id')->comment('タグID');
            $table->string('name')->comment('タグ名');
            $table->string('image_url')->nullable()->comment('タグアイコン画像URL');
            $table->boolean('is_display')->default(true)->comment('タグアイコン画像表示FLG');
            $table->softDeletes();
            $table->timestamps();


            // 外部キー制約の定義
            $table->foreign('tag_group_id')->references('id')->on('tag_groups');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tags');
    }
};
