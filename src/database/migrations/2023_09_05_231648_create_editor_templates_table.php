<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('editor_templates', function (Blueprint $table) {
            $table->comment('エディタテンプレート');
            $table->uuid('id')->primary();
            $table->string('name')->comment('テンプレート名');
            $table->json('editor_data')->comment('エディタ情報（JSONデータ）');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('editor_templates');
    }
};
