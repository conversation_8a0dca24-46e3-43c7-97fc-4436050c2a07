<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('money_transfers', function (Blueprint $table) {
            $table->renameColumn('reservation_datetime', 'transfer_date');
        });

        DB::statement("ALTER TABLE money_transfers MODIFY transfer_date DATE NULL COMMENT '送金日'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('money_transfers', function (Blueprint $table) {
            $table->renameColumn('transfer_date', 'reservation_datetime');
        });

        DB::statement("ALTER TABLE money_transfers MODIFY reservation_datetime DATETIME NULL COMMENT '送金予約日時'");
    }
};
