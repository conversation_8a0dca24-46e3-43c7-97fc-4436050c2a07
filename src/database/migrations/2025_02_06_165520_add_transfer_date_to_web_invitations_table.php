<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->date('transfer_date')->nullable()->after('reply_deadline_date')->comment('送金日');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->dropColumn('transfer_date');
        });
    }
};
