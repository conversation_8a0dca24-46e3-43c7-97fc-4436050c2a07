<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_box_info_histories', function (Blueprint $table) {
            $table->comment('引き出物Box情報履歴');
            $table->uuid('id')->primary();
            $table->uuid('gift_arrangement_history_id')->comment('引き出物手配履歴ID');
            $table->uuid('gift_option_product_id')->comment('引出物オプション商品マスタID');
            $table->string('gift_option_product_name')->comment('引出物オプション商品名');
            $table->text('gift_option_product_description')->nullable()->comment('引出物オプション商品説明');
            $table->string('gift_option_product_image')->comment('引出物オプション商品画像（１枚）');
            $table->softDeletes();
            $table->timestamps();
            $table->foreign('gift_arrangement_history_id')->references('id')->on('gift_arrangement_histories');
            $table->foreign('gift_option_product_id')->references('id')->on('gift_option_products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_box_info_histories');
    }
};
