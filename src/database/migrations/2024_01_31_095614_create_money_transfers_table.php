<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('money_transfers', function (Blueprint $table) {
            $table->comment('送金');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID (FK)');
            $table->uuid('web_invitation_id')->comment('Web招待状ID');
            $table->uuid('admin_id')->nullable()->comment('担当者ID');
            $table->unsignedTinyInteger('status')->nullable()->default(0)->comment('送金ステータス');
            $table->date('deadline_date')->nullable()->nullable()->comment('送金期限');
            $table->decimal('prepayment_amount', 10, 2)->nullable()->default(0)->comment('事前支払金額');
            $table->decimal('system_fee', 10, 2)->nullable()->default(0)->comment('システム使用料');
            $table->decimal('commission_fee', 10, 2)->nullable()->default(0)->comment('手数料');
            $table->decimal('transfer_amount', 10, 2)->nullable()->default(0)->comment('送金金額');
            $table->dateTime('reservation_datetime')->nullable()->comment('送金予約日時');
            $table->dateTime('completion_datetime')->nullable()->comment('送金完了日時');
            $table->string('bank_code', 4)->nullable()->comment('銀行コード');
            $table->string('bank_name')->nullable()->comment('銀行名');
            $table->string('branch_code', 3)->nullable()->comment('支店コード');
            $table->string('branch_name')->nullable()->comment('支店名');
            $table->tinyInteger('account_type')->unsigned()->nullable()->comment('口座種別');
            $table->string('account_name', 30)->comment('口座名義');
            $table->unsignedInteger('account_number')->nullable()->comment('口座番号');
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('web_invitation_id')->references('id')->on('web_invitations');
            $table->foreign('admin_id')->references('id')->on('admins');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('money_transfers');
    }
};
