<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_web_invitation_templates', function (Blueprint $table) {
            $table->comment('WEB招待状共通テンプレートマスタ');
            $table->uuid('id')->primary();
            $table->string('name')->nullable()->comment('テンプレート名称');
            $table->text('description')->nullable()->comment('テンプレート説明');
            $table->string('image')->nullable()->comment('テンプレートイメージ画像のパス');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_web_invitation_templates');
    }
};
