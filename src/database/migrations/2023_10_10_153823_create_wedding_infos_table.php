<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wedding_infos', function (Blueprint $table) {
            $table->comment('結婚式情報');
            $table->uuid("id")->comment('ID');
            $table->uuid('member_id')->comment('会員ID');
            $table->date('wedding_date')->nullable()->comment('挙式日');
            $table->string('wedding_venue')->nullable()->comment('挙式会場名');
            $table->date('reception_date')->nullable()->comment('披露宴日');
            $table->integer('guest_count')->nullable()->default(0)->comment('招待人数');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wedding_infos');
    }
};
