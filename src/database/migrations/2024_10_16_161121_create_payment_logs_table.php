<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->comment('決済ログ');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->nullable()->comment('会員ID');
            $table->uuid('web_invitation_id')->nullable()->comment('Web招待状ID');
            $table->unsignedTinyInteger('type')->nullable()->comment('処理区分');
            $table->string('guest_name')->nullable()->comment('ゲスト姓名');
            $table->json('input_parameters')->nullable()->comment('入力値パラメータ (JSON)');
            $table->string('payment_id')->nullable()->comment('決済ID');
            $table->json('payment_post_parameters')->nullable()->comment('決済ポストパラメータ (JSON)');
            $table->json('payment_post_results')->nullable()->comment('GMO API結果 (JSON)');
            $table->json('api_results')->nullable()->comment('API結果 (JSON)');
            $table->longText('error_results')->nullable()->comment('例外エラー');
            $table->ipAddress('ip_address')->nullable()->comment('IPアドレス');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_logs');
    }
};
