<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('option_product_masters', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('option_group_master_id')->comment('オプショングループマスタID');
            $table->string('name')->comment('オプション名称');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('option_group_master_id')
                ->references('id')
                ->on('option_product_group_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('option_product_masters');
    }
};
