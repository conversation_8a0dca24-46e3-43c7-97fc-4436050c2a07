<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('web_invitation_template_blocks', function (Blueprint $table) {
            $table->comment('WEB招待状共通テンプレートブロック');
            $table->uuid('id')->primary();
            $table->uuid('m_web_invitation_template_id')->comment('WEB招待状テンプレートマスタID');
            $table->string('block_name')->nullable()->comment('ブロック名称');
            $table->string('vue_component_name')->nullable()->comment('ブロックVueコンポーネント名');
            $table->integer('display_order')->nullable()->default(1)->comment('表示順');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('m_web_invitation_template_id')->references('id')->on('m_web_invitation_templates')->name('fk_template_to_block_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('web_invitation_template_blocks');
    }
};
