<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->json('allergies')->nullable()->comment('アレルギー品目')->after('allergy');
            $table->text('allergy')->nullable()->comment('その他アレルギー')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->dropColumn('allergies');
            $table->text('allergy')->nullable()->comment('アレルギー')->change();
        });
    }
};
