<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('unit_values', function (Blueprint $table) {
            $table->comment('数量単位値マスタ');
            $table->uuid('id')->primary();
            $table->uuid('unit_id')->comment('数量単位マスタID');
            $table->integer('value')->comment('数値');
            $table->softDeletes();
            $table->timestamps();
            $table->foreign('unit_id')->references('id')->on('units');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('unit_values');
    }
};
