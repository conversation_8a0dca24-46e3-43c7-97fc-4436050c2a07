<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_card_bag_arrangement_histories', function (Blueprint $table) {
            $table->comment('引き出物カードバッグ手配履歴');
            $table->uuid('id')->primary();
            $table->uuid('gift_arrangement_history_id')->comment('引き出物手配履歴ID');
            $table->tinyInteger('status')->comment('ステータス');
            $table->tinyInteger('shipping_status')->comment('出荷ステータス');
            $table->date('submission_schedule')->comment('入稿予定日');
            $table->date('shipping_deadline')->comment('出荷期日');
            $table->date('shipping_date')->comment('出荷日');
            $table->string('shipping_origin')->comment('出荷元');
            $table->date('delivery_date')->comment('お届け希望日');
            $table->time('delivery_time')->nullable()->comment('お届け希望時間');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('gift_arrangement_history_id')->references('id')->on('gift_arrangement_histories')->name('gah_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_card_bag_arrangement_histories');
    }
};
