<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_category_search_conditions', function (Blueprint $table) {
            $table->comment('商品カテゴリ検索条件ID');
            $table->uuid('id')->primary();
            $table->uuid('product_category_combination_id')->comment('商品カテゴリ組み合わせID');
            $table->uuid('product_search_condition_id')->comment('商品検索条件ID');
            $table->uuid('product_search_condition_item_id')->comment('商品検索条件項目ID');
            $table->boolean('is_unused')->default(true)->comment('未使用フラグ');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('product_category_combination_id', 'fk_category_combination')
                ->references('id')
                ->on('product_category_combinations');

            $table->foreign('product_search_condition_id', 'fk_search_condition')
                ->references('id')
                ->on('product_search_conditions');

            $table->foreign('product_search_condition_item_id', 'fk_earch_condition_item')
                ->references('id')
                ->on('product_search_condition_items');

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_category_search_conditions');
    }
};
