<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_tags', function (Blueprint $table) {
            $table->comment('商品タグ');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            $table->uuid('tag_id')->comment('タグID');
            $table->unsignedTinyInteger('tag_type')->default(0)->nullable()->comment('タグ利用分類');
            $table->integer('sort_order')->default(0)->nullable()->comment('並び順');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('product_id')->references('id')->on('products');
            $table->foreign('tag_id')->references('id')->on('tags');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_tags');
    }
};
