<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_category_combinations', function (Blueprint $table) {
            $table->comment('商品カテゴリ組み合わせマスタ');
            $table->uuid('id')->primary();
            $table->uuid('product_category_id1')->comment('商品大カテゴリマスタID');
            $table->uuid('product_category_id2')->comment('商品中カテゴリマスタID');
            $table->uuid('product_category_id3')->comment('商品小カテゴリマスタID');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('product_category_id1')
                ->references('id')
                ->on('product_categories');

            $table->foreign('product_category_id2')
                ->references('id')
                ->on('product_categories');

            $table->foreign('product_category_id3')
                ->references('id')
                ->on('product_categories');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_category_combinations');
    }
};
