<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('web_invitations', function (Blueprint $table) {
            $table->comment('ユーザー作成WEB招待状');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('m_web_invitation_id')->comment('WEB招待状マスタID');
            $table->uuid('guest_list_id')->comment('ゲストリストID');
            $table->string('name')->nullable()->comment('招待状名');
            $table->string('public_url')->nullable()->comment('公開URL');
            $table->string('password')->nullable()->comment('パスワード');
            $table->boolean('is_password')->default(false)->comment('パスワード利用フラグ');
            $table->boolean('is_public')->default(false)->comment('公開済フラグ');
            $table->json('editor_settings')->nullable()->comment('エディタ設定値');
            $table->json('block_settings')->nullable()->comment('ブロック設定値');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            // $table->foreign('m_web_invitation_id')->references('id')->on('m_web_invitations');
            $table->foreign('guest_list_id')->references('id')->on('guest_lists');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('web_invitations');
    }
};
