<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('event_lists', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('guest_list_id')->nullable()->comment('ゲストリストID（外部キー）');
            $table->string('event_name')->nullable()->comment('イベント名');
            $table->date('event_date')->nullable()->comment('開催日');
            $table->json('event_time')->nullable()->comment('時間（JSON形式）');
            $table->string('venue_name')->comment('会場名');
            $table->string('venue_name_kana')->nullable()->comment('会場名（よみがな）');
            $table->string('venue_postal_code')->nullable()->comment('会場郵便番号');
            $table->string('venue_address')->nullable()->comment('会場住所');
            $table->string('venue_tel')->nullable()->comment('会場TEL');
            $table->string('venue_url')->nullable()->comment('会場URL');
            $table->json('fees')->nullable()->comment('会費・ご祝儀（JSON形式）');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('guest_list_id')->references('id')->on('guest_lists');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('event_lists');
    }
};
