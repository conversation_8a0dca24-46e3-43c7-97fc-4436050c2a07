<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_tags', function (Blueprint $table) {
            $table->comment('ゲストタグ');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('guest_list_id')->comment('ゲストリストID');
            $table->string('tag')->nullable()->comment('タグ');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('guest_list_id')->references('id')->on('guest_lists');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_tags');
    }
};
