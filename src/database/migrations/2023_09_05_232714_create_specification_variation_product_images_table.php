<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('specification_variation_product_images', function (Blueprint $table) {
            $table->comment('規格・バリエーション別商品画像');
            $table->uuid('id')->primary();
            $table->uuid('specification_variation_product_info_id')->comment('規格・バリエーション別商品情報ID');
            $table->string('image_url')->comment('画像URL');
            $table->string('image_comment')->comment('画像コメント');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('specification_variation_product_info_id', 'fk_specification_variation_product_info')
                ->references('id')
                ->on('specification_variation_product_infos');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('specification_variation_product_images');
    }
};
