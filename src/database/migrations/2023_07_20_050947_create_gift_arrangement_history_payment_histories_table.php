<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_arrangement_history_payment_histories', function (Blueprint $table) {
            $table->comment('引き出物手配履歴-決済履歴');
            $table->uuid('id')->primary();
            $table->uuid('payment_history_id');
            $table->uuid('gift_arrangement_history_id');
            $table->timestamps();
            $table->foreign('payment_history_id')->references('id')->on('payment_histories')->name('ph_id');
            $table->foreign('gift_arrangement_history_id')->references('id')->on('gift_arrangement_histories')->name('gah_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_arrangement_history_payment_histories');
    }
};
