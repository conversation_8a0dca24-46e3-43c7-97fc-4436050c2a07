<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_assignments', function (Blueprint $table) {
            $table->comment('手配前引き出物割り当てメインギフト');
            $table->uuid('id')->primary();
            $table->uuid('gift_shipping_info_id')->comment('手配前送付先情報ID');
            $table->boolean('is_purchase')->default(false)->comment('購入済みフラグ');
            $table->uuid('payment_history_detail_id')->nullable()->comment('決済履歴明細ID');
            $table->uuid('product_id')->nullable()->comment('商品マスタID');
            $table->softDeletes();
            $table->timestamps();
            // 外部キー制約の定義
            $table->foreign('gift_shipping_info_id')->references('id')->on('gift_shipping_infos');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_assignments');
    }
};
