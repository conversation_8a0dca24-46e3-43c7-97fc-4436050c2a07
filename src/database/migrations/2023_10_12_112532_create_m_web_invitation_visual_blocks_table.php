<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_web_invitation_visual_blocks', function (Blueprint $table) {
            $table->comment('WEB招待状メインビジュアルブロックマスタ');
            $table->uuid('id')->primary();
            $table->string('logical_name')->nullable()->comment('メインビジュアルコンポーネント論理名');
            $table->string('physical_name')->nullable()->comment('メインビジュアルコンポーネント物理名');
            $table->string('image_file_name')->nullable()->comment('メインビジュアルコンポーネント画像ファイル名');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_web_invitation_visual_blocks');
    }
};
