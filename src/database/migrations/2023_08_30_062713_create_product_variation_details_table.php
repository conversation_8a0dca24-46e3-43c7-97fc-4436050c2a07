<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_variation_details', function (Blueprint $table) {
            $table->comment('商品バリエーション明細');
            $table->uuid('id')->primary();
            $table->uuid('product_variation_master_id')->comment('バリエーシマスタID');
            $table->string('name')->comment('バリエーション明細名');
            $table->string('image_url')->nullable()->comment('画像URL');
            $table->softDeletes();
            $table->foreign('product_variation_master_id')->references('id')->on('product_variation_masters');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_variation_details');
    }
};
