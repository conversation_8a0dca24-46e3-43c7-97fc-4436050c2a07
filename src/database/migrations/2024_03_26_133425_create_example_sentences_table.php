<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('example_sentences', function (Blueprint $table) {
            $table->comment('文例集');
            $table->uuid('id')->primary();
            $table->unsignedTinyInteger('product_type')->nullable()->comment('商品種別');
            $table->unsignedTinyInteger('example_type')->nullable()->comment('文例集種別');
            $table->text('example_text')->nullable()->comment('文例テキスト');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('example_sentences');
    }
};
