<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('option_products', function (Blueprint $table) {
            $table->comment('オプション商品');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            $table->uuid('option_product_group_master_id')->comment('オプション商品グループマスタID');
            $table->integer('sort_order')->comment('並び順');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('product_id')
                ->references('id')
                ->on('products');

            $table->foreign('option_product_group_master_id')
                ->references('id')
                ->on('option_product_group_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('option_products');
    }
};
