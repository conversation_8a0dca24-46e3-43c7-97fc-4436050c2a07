<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_assignment_histories', function (Blueprint $table) {
            $table->comment('引き出物割り当てメインギフト履歴');
            $table->uuid('id')->primary();
            $table->uuid('gift_delivery_info_history_id')->comment('引き出物送付先情報ID');
            $table->uuid('payment_history_detail_id')->comment('決済履歴明細ID');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('gift_delivery_info_history_id')->references('id')->on('gift_delivery_info_histories');
            $table->foreign('payment_history_detail_id')->references('id')->on('payment_history_details');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_assignment_histories');
    }
};
