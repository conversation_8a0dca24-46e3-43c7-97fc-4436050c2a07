<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guest_lists', function (Blueprint $table) {
            $table->boolean('is_default')->nullable()->default(false)->comment("デフォルトゲストリストフラグ")->after('name');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guest_lists', function (Blueprint $table) {
            $table->dropColumn('is_default');
        });
    }
};
