<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('m_web_invitations', function (Blueprint $table) {
            $table->uuid('first_view_id')->nullable()->comment('選択ファーストビューID (FK)')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('m_web_invitations', function (Blueprint $table) {
            $table->uuid('first_view_id')->comment('選択ファーストビューID (FK)')->change();
        });
    }
};
