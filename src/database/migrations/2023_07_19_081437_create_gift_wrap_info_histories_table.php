<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_wrap_info_histories', function (Blueprint $table) {
            $table->comment('引き出物のし情報履歴');
            $table->uuid('id')->primary();
            $table->uuid('gift_arrangement_history_id')->comment('引き出物手配履歴ID');
            $table->unsignedInteger('noshi_rows')->comment('のし行数');
            $table->string('noshi_content_1')->nullable()->comment('のし記載1');
            $table->string('noshi_content_2')->nullable()->comment('のし記載2');
            $table->text('kanji_alphabet_details')->nullable()->comment('旧字・外字指定詳細');
            $table->string('noshi_pdf_url')->nullable()->comment('のしPDFURL');
            $table->string('submission_noshi_pdf_url')->nullable()->comment('入稿用のしPDFURL');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('gift_arrangement_history_id')->references('id')->on('gift_arrangement_histories');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_wrap_info_histories');
    }
};
