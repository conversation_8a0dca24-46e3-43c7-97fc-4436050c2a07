<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->boolean('is_update_web_invitation_complet_mail')->default(false)->nullable()->comment("Web招待状の回答受付更新メールFLG")->after("card_settlement_id");
            $table->boolean('is_previousday_party_mail')->default(false)->nullable()->comment("挙式前日パーティー開催のご案内メールFLG")->after("is_update_web_invitation_complet_mail");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->dropColumn(['is_update_web_invitation_complet_mail', 'is_previousday_party_mail']);
        });
    }
};
