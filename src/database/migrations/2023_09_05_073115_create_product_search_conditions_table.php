<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_search_conditions', function (Blueprint $table) {
            $table->comment('商品検索条件');
            $table->uuid('id')->primary();
            $table->string('name')->comment('検索条件名');
            $table->tinyInteger('ui_type')->comment('検索UI種別');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_search_conditions');
    }
};
