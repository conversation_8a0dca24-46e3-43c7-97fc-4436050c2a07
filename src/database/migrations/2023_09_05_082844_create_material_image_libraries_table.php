<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('material_image_libraries', function (Blueprint $table) {
            $table->comment('素材画像ライブラリ');
            $table->uuid('id')->primary();
            $table->string('product_type')->comment('商品種別');
            $table->string('image_url')->comment('画像URL');
            $table->integer('display_order')->comment('表示順');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('material_image_libraries');
    }
};
