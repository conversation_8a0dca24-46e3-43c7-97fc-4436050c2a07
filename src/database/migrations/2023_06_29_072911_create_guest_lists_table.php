<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_lists', function (Blueprint $table) {
            $table->comment('ゲストリスト');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->string('name')->nullable()->comment('ゲストリスト名');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_lists');
    }
};
