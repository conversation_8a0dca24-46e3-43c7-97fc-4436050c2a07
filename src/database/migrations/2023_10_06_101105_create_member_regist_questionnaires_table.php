<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('member_regist_questionnaires', function (Blueprint $table) {
            $table->comment('会員登録時アンケート情報');
            $table->uuid("id")->comment('ID');
            $table->uuid('member_id')->comment('会員ID');
            $table->string('question')->nullable()->comment('質問');
            $table->string('answer')->nullable()->comment('回答');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_regist_questionnaires');
    }
};
