<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;


return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('members', function (Blueprint $table) {
            // member_numberカラムを追加（既存のデータを扱うため、インクリメントは後で設定）
            $table->unsignedBigInteger('member_number')->nullable()->unique()->after('birthdate')->comment('会員番号');
        });

        // 既存データに対して会員番号を付与
        DB::statement('SET @row_number = 99'); // 初期値99を設定
        DB::statement('UPDATE members SET member_number = (@row_number:=@row_number + 1) ORDER BY id ASC');

        // member_numberを自動インクリメントに設定し、次に挿入されるレコードがインクリメントされるようにする
        Schema::table('members', function (Blueprint $table) {
            $table->unsignedBigInteger('member_number')->autoIncrement()->comment('会員番号')->change();
        });

        // AUTO_INCREMENTの値を設定（100からスタート）
        DB::statement('ALTER TABLE members AUTO_INCREMENT = 100;');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('members', function (Blueprint $table) {
            $table->dropColumn('member_number');
        });
    }
};
