<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guest_event_answers', function (Blueprint $table) {
            $table->unique(['guest_id', 'date', 'name']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guest_event_answers', function (Blueprint $table) {
            $table->dropUnique(['guest_id', 'date', 'name']);
        });
    }
};
