<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('m_web_invitations', function (Blueprint $table) {
            // カラム追加
            $table->uuid('m_web_invitation_visual_block_id')->nullable()->comment('WEB招待状メインビジュアルブロックマスタID')->after('m_web_invitation_template_id');
            $table->dropColumn('block_settings_json');
            $table->json('image_aspect_settings_json')->nullable()->comment('画像のアスペクト比設定値')->after('editor_settings_json');

            // カラムの外部キー制約追加
            $table->foreign('m_web_invitation_visual_block_id')->references('id')->on('m_web_invitation_visual_blocks');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('m_web_invitations', function (Blueprint $table) {
            // $table->dropColumn('m_web_invitation_visual_block_id');
            // $table->dropColumn('image_aspect_settings_json');
            // $table->json('block_settings_json')->nullable()->after('editor_settings_json');
            $table->dropForeign(['m_web_invitation_visual_block_id']);
        });
    }
};
