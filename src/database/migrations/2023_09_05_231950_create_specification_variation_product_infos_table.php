<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('specification_variation_product_infos', function (Blueprint $table) {
            $table->comment('規格・バリエーション別商品情報ID');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            $table->uuid('product_specification_detail_info_id1')->comment('規格明細ID1');
            $table->uuid('product_specification_detail_info_id2')->comment('規格明細ID2');
            $table->uuid('product_variation_detail_info_id1')->comment('バリエーション明細ID1');
            $table->uuid('product_variation_detail_info_id2')->comment('バリエーション明細ID2');
            $table->uuid('product_variation_detail_info_id3')->comment('バリエーション明細ID3');
            $table->boolean('is_listed')->comment('一覧表示FLG');
            $table->uuid('editor_template_id')->comment('エディタテンプレートID');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('product_id')
                ->references('id')
                ->on('products');

            $table->foreign('editor_template_id')
                ->references('id')
                ->on('editor_templates');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('specification_variation_product_infos');
    }
};
