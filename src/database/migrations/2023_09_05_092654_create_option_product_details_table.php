<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('option_product_details', function (Blueprint $table) {
            $table->comment('オプション商品明細');
            $table->uuid('id')->primary();
            $table->uuid('option_product_id')->comment('オプション商品ID');
            $table->uuid('option_product_master_id')->comment('オプション商品マスタID');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('option_product_id')
                ->references('id')
                ->on('option_products');

            $table->foreign('option_product_master_id')
                ->references('id')
                ->on('option_product_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('option_product_details');
    }
};
