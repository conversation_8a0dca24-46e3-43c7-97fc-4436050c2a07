<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_tag_guests', function (Blueprint $table) {
            $table->comment('ゲストタグ-ゲスト');
            $table->uuid('id')->primary();
            $table->uuid('guest_tag_id')->comment('タグID');
            $table->uuid('guest_id')->comment('ゲストタグID');
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('guest_tag_id')->references('id')->on('guest_tags');
            $table->foreign('guest_id')->references('id')->on('guests');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_tag_guests');
    }
};
