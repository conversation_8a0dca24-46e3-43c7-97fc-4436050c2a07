<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_specification_products', function (Blueprint $table) {
            $table->comment('規格別商品マスタ');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            $table->uuid('product_specification_detail_info_id1')->nullable()->comment('規格明細ID1');
            $table->uuid('product_specification_detail_info_id2')->nullable()->comment('規格明細ID2');
            $table->uuid('product_specification_detail_info_id3')->nullable()->comment('規格明細ID3');
            $table->uuid('variation_template_id')->nullable()->comment('バリエーションテンプレートID');
            $table->uuid('editor_template_id')->nullable()->comment('エディタテンプレートID');
            $table->string('item_number')->comment('品番');
            $table->integer('regular_price')->comment('通常価格');
            $table->integer('sale_price')->comment('販売価格');
            $table->datetime('sale_price_start')->nullable()->comment('販売価格利用期間(FROM日時)');
            $table->datetime('sale_price_end')->nullable()->comment('販売価格利用期間(TO日時)');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('product_id')
                ->references('id')
                ->on('products');

            // $table->foreign('product_specification_detail_info_id_1')
            //     ->references('id')
            //     ->on('specification_details');

            // $table->foreign('product_specification_detail_info_id_2')
            //     ->references('id')
            //     ->on('specification_details');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('m_specification_products');
    }
};
