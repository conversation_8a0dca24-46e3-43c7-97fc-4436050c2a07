<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_delivery_info_histories', function (Blueprint $table) {
            $table->comment('引き出物送付先情報履歴');
            $table->uuid('id')->primary();
            $table->uuid('gift_arrangement_history_id')->comment('引き出物手配履歴ID');
            $table->uuid('guest_id')->comment('ゲストID');
            $table->uuid('guest_tag_id')->nullable()->comment('ゲストタグID');
            $table->string('recipient_name')->comment('宛名');
            $table->string('postal_code')->comment('郵便番号');
            $table->string('prefecture')->comment('都道府県');
            $table->string('city')->comment('市区町村');
            $table->string('address')->comment('丁目・番地');
            $table->string('building')->nullable()->comment('建物名・部屋番号');
            $table->string('phone')->comment('電話番号');
            $table->string('shipping_number')->nullable()->comment('配送伝票番号');
            $table->unsignedInteger('shipping_status')->nullable()->comment('配送状況');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('gift_arrangement_history_id')->references('id')->on('gift_arrangement_histories');
            $table->foreign('guest_id')->references('id')->on('guests');
            // $table->foreign('tag_id')->references('id')->on('guest_tags');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_delivery_info_histories');
    }
};
