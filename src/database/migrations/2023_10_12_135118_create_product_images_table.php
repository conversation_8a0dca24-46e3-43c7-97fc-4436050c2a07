<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_images', function (Blueprint $table) {
            $table->comment('規格別商品画像');
            $table->uuid('id')->primary();
            $table->uuid('m_specification_product_id')->comment('規格別商品情報ID');
            $table->uuid('uuid')->unique()->nullable()->comment('画像uuid');
            $table->text('image_comment')->nullable()->comment('画像コメント');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('m_specification_product_id')->references('id')->on('m_specification_products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_images');
    }
};
