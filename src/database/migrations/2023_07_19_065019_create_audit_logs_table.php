<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('audit_logs', function (Blueprint $table) {
            $table->comment('操作ログ');
            $table->uuid('id')->primary();
            $table->uuid('staff_id')->comment('変更担当者ID');
            $table->text('change_data')->comment('変更データ');
            $table->timestamp('change_date')->default(DB::raw('CURRENT_TIMESTAMP'))->comment('変更日時');
            $table->softDeletes();
            $table->timestamps();
            // 外部キー制約の定義
            $table->foreign('staff_id')->references('id')->on('staffs');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('audit_logs');
    }
};
