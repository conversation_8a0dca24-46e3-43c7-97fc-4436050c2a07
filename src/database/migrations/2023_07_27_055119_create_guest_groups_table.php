<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_groups', function (Blueprint $table) {
            $table->comment('ゲストグループ');
            $table->uuid('id')->primary();
            $table->uuid('guest_list_id')->comment('ゲストID');
            $table->uuid('member_id')->comment('会員ID');
            $table->string('name')->nullable()->comment('ゲストグループ名');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('guest_list_id')->references('id')->on('guest_lists');
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_groups');
    }
};
