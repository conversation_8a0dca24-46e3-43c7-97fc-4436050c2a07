<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_wrappings', function (Blueprint $table) {
            $table->comment('手配前引き出物ラッピング');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->boolean('is_purchase')->default(false)->comment('購入済みフラグ');
            $table->uuid('payment_history_detail_id')->nullable()->comment('決済履歴明細ID(購入済FLG=trueのときのみ使用)');
            $table->uuid('product_id')->comment('商品マスタID(購入済FLG=falseのときのみ使用)');
            $table->uuid('gift_option_product_id')->comment('引き出物オプション商品マスタID');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('gift_option_product_id')->references('id')->on('gift_option_products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_wrappings');
    }
};
