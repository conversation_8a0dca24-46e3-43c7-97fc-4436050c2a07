<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('member_email_changes', function (Blueprint $table) {
            $table->comment('メールアドレス変更');
            $table->uuid("id")->comment('ID');
            $table->uuid('member_id')->comment('会員ID');
            $table->string('new_email');
            $table->string('token')->nullable();
            $table->timestamp('created_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_email_changes');
    }
};
