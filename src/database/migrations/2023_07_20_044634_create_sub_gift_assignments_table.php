<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('sub_gift_assignments', function (Blueprint $table) {
            $table->comment('引き出物割り当てサブギフト');
            $table->id();
            $table->uuid('gift_assignment_id')->comment('手配前引き出物割り当てメインギフトID');
            $table->uuid('gift_option_product_id')->comment('引き出物オプション商品マスタID');
            $table->unsignedTinyInteger('sub_gift_number')->comment('サブギフト番号 (1 or 2)');
            $table->softDeletes();
            $table->timestamps();
            // 外部キー制約の定義
            $table->foreign('gift_assignment_id')->references('id')->on('gift_assignments');
            $table->foreign('gift_option_product_id')->references('id')->on('gift_option_products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('sub_gift_assignments');
    }
};
