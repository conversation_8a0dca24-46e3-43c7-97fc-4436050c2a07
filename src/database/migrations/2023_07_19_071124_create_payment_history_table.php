<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_histories', function (Blueprint $table) {
            $table->comment('決済履歴');
            $table->uuid('id')->primary();
            $table->uuid('order_history_id')->comment('注文履歴ID');
            $table->uuid('member_id')->comment('会員ID');
            $table->softDeletes();
            $table->timestamps();
            // $table->foreign('order_history_id')->references('id')->on('order_histories');
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_histories');
    }
};
