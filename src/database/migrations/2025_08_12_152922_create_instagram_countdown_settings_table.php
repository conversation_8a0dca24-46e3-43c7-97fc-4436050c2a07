<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('instagram_countdown_settings', function (Blueprint $table) {
            $table->comment('Instagramカウントダウン画像設定');
            $table->uuid('uuid')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->unsignedTinyInteger('instagram_type')->comment('Instagram画像種別');
            $table->json('image_data')->nullable()->comment('画像データ（JSON）');
            $table->boolean('show_names')->default(true)->comment('名前表示フラグ');
            $table->boolean('show_event_date')->default(true)->comment('挙式日表示フラグ');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('member_id')->references('id')->on('members');
            $table->index(['member_id', 'instagram_type']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('instagram_countdown_settings');
    }
};
