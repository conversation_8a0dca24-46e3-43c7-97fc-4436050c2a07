<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_specification_details', function (Blueprint $table) {
            $table->comment('商品規格明細');
            $table->uuid('id')->primary();
            $table->uuid('product_specification_master_id')->comment('商品規格マスタID');
            $table->string('detail_name')->comment('規格明細名');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('product_specification_master_id', 'fk_product_specification_details_product_specification_master_id')
                ->references('id')
                ->on('product_specification_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_specification_details');
    }
};
