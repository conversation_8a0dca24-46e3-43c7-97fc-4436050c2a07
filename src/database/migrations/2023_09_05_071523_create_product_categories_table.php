<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_categories', function (Blueprint $table) {
            $table->comment('商品カテゴリマスタ');
            $table->uuid('id')->primary();
            $table->string('name')->comment('商品カテゴリ名');
            $table->integer('level')->comment('商品カテゴリレベル');
            $table->boolean('is_category_page')->default(true)->comment('カテゴリページありFLG');
            $table->text('category_page_content')->nullable()->comment('カテゴリページコンテンツ');
            $table->boolean('is_show_products_category_page')->default(true)->comment('カテゴリページ内商品表示FLG');
            $table->string('meta_title')->nullable()->comment('meta_title');
            $table->text('meta_description')->nullable()->comment('meta_description');
            $table->string('meta_canonical')->nullable()->comment('meta_canonical');
            $table->string('meta_keywords')->nullable()->comment('meta_keywords');
            $table->softDeletes();
            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_categories');
    }
};
