<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_specification_infos', function (Blueprint $table) {
            $table->comment('商品規格情報');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            $table->uuid('product_specification_master_id')->comment('商品規格マスタID');
            $table->integer('sort_order')->comment('並び順');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('product_id')
                ->references('id')
                ->on('products');

            $table->foreign('product_specification_master_id', 'fk_product_specification')
                ->references('id')
                ->on('product_specification_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_specification_infos');
    }
};
