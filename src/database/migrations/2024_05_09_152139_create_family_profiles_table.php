<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('family_profiles', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->string('last_name')->nullable()->comment('姓');
            $table->string('first_name')->nullable()->comment('名');
            $table->unsignedInteger('order')->nullable()->comment('並び順');
            $table->string('last_name_kana')->nullable()->comment('姓（カナ）');
            $table->string('first_name_kana')->nullable()->comment('名（カナ）');
            $table->string('last_name_romaji')->nullable()->comment('姓（ローマ字）');
            $table->string('first_name_romaji')->nullable()->comment('名（ローマ字）');
            $table->date('birth_date')->nullable()->comment('生年月日');
            $table->unsignedTinyInteger('type')->nullable()->comment('新郎新婦種別');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('family_profiles');
    }
};
