<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 既存のユニーク制約を削除
        Schema::table('guest_event_answers', function (Blueprint $table) {
            // 外部キー制約を削除（実際の制約名は異なる可能性があります）
            $table->dropForeign(['guest_id']);

            // ユニーク制約を削除
            $table->dropUnique('guest_event_answers_guest_id_date_name_unique');

            // 列の削除
            $table->dropColumn('name');
            $table->dropColumn('date');

            // 新しい列の追加
            $table->uuid('event_list_id')->nullable()->comment('イベントリストID')->after('guest_id');
            $table->foreign('event_list_id')->references('id')->on('event_lists');

            // guest_id の外部キー制約を再追加（必要な場合）
            $table->foreign('guest_id')->references('id')->on('guests');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guest_event_answers', function (Blueprint $table) {
            // // 外部キー制約を削除
            $table->dropForeign(['event_list_id']);
            $table->dropColumn('event_list_id');

            // // 列の追加
            $table->string('name')->nullable()->comment('イベント名')->after('guest_id');
            $table->date('date')->nullable()->comment('イベント日付')->after('name');
        });
    }
};
