<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_card_bag_arrangement_history_details', function (Blueprint $table) {
            $table->comment('引き出物カードバッグ手配履歴明細');
            $table->uuid('id')->primary();
            $table->uuid('gift_card_bag_arrangement_history_id')->comment('引き出物カードバッグ手配履歴ID');
            $table->uuid('product_id')->comment('商品マスタID');
            $table->unsignedInteger('quantity')->comment('数量');
            $table->softDeletes();
            $table->timestamps();

            $table->foreign('gift_card_bag_arrangement_history_id')->references('id')->on('gift_card_bag_arrangement_histories')->name('gcbah_id');
            $table->foreign('product_id')->references('id')->on('products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_card_bag_arrangement_history_details');
    }
};
