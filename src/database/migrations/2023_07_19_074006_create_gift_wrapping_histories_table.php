<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_wrapping_histories', function (Blueprint $table) {
            $table->comment('引き出物ラッピング履歴');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('payment_history_detail_id')->nullable()->comment('決済履歴明細ID');
            $table->uuid('gift_option_product_id')->comment('引き出物オプション商品マスタID');
            $table->string('option_product_name')->comment('引き出物オプション商品名');
            $table->text('option_product_description')->nullable()->comment('引き出物オプション商品説明');
            $table->string('option_product_image')->comment('引き出物オプション商品画像');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('payment_history_detail_id')->references('id')->on('payment_history_details');
            $table->foreign('gift_option_product_id')->references('id')->on('gift_option_products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_wrapping_histories');
    }
};
