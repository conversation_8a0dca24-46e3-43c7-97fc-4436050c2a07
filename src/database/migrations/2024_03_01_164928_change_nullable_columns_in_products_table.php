<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->uuid('counter_word_id')->comment('助数詞マスタID')->nullable()->change();
            $table->uuid('unit_id')->nullable()->comment('数量単位マスタID')->change();
            $table->integer('max_sales_quantity')->nullable()->default(0)->comment('最大販売数')->change();
            $table->uuid('tax_rate_id')->nullable()->comment('税率マスタID')->change();
            $table->uuid('product_category_combination_id')->nullable()->comment('商品カテゴリ組み合わせID')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('products', function (Blueprint $table) {
            $table->unsignedBigInteger('counter_word_id')->nullable(false)->comment('助数詞マスタID')->change();
            $table->unsignedBigInteger('unit_id')->nullable(false)->comment('数量単位マスタID')->change();
            $table->integer('max_sales_quantity')->nullable(false)->comment('最大販売数')->change();
            $table->unsignedBigInteger('tax_rate_id')->nullable(false)->comment('税率マスタID')->change();
            $table->unsignedBigInteger('product_category_combination_id')->nullable(false)->comment('商品カテゴリ組み合わせID')->change();
        });
    }
};
