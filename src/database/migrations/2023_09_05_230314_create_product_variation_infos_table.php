<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_variation_infos', function (Blueprint $table) {
            $table->comment('商品バリエーション情報');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            $table->uuid('product_variation_master_id')->comment('商品バリエーションマスタID');
            $table->integer('sort_order')->nullable()->comment('並び順');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_variation_infos');
    }
};
