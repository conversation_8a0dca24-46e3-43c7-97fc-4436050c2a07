<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('specification_product_components', function (Blueprint $table) {
            $table->comment('規格別商品構成部材');
            $table->uuid('id')->primary();
            $table->uuid('m_specification_product_id')->comment('規格別商品情報ID');
            $table->uuid('component_master_id')->comment('部材マスタID');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('m_specification_product_id', 'fk_m_specification_product3')
                ->references('id')
                ->on('m_specification_products');

            // $table->foreign('component_master_id')
            //     ->references('id')
            //     ->on('component_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('specification_product_components');
    }
};
