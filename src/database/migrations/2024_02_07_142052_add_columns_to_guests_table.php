<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->unsignedInteger('guest_type')->nullable()->default(0)->comment('ゲストタイプ')->after('m_web_invitation_id');
            // $table->string('honor')->nullable()->comment('敬称')->after('first_name_romaji');
            // $table->string('relationship_name')->nullable()->comment('関係性')->after('relationship');
            $table->unsignedTinyInteger('payment_method')->nullable()->comment('会費・ご祝儀支払い方法(事前支払い利用/当日会場へ持参/すでに支払い済み/NULL)')->after('is_member_confirmed');
            $table->boolean('is_system_fee')->default(false)->nullable()->comment('システム利用料負担FLG')->after('gift_amount');
            $table->unsignedInteger('system_fee')->nullable()->default(0)->comment('システム利用料')->after('is_system_fee');
            $table->unsignedDecimal('system_fee_rate', 5, 2)->nullable()->default(0)->comment('システム利用料率')->after('system_fee');
            $table->unsignedInteger('total_amount')->nullable()->default(0)->comment('会費・ご祝儀・お気持ち金額合計金額')->after('system_fee_rate');
            $table->unsignedInteger('settlement_amount')->nullable()->default(0)->comment('決算金額')->after('total_amount');
            $table->string('card_settlement_id')->nullable()->comment('カード決算ID')->after('settlement_amount');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->dropColumn(['guest_type', 'payment_method', 'is_system_fee', 'system_fee', 'system_fee_rate', 'total_amount', 'settlement_amount', 'card_settlement_id']);
        });
    }
};
