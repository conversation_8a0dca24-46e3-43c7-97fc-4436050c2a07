<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('gift_arrangement_histories', function (Blueprint $table) {
            $table->comment('引き出物手配履歴');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('shipping_company_id')->comment('引き出物出荷元業者ID');
            $table->uuid('staff_id')->nullable()->comment('担当者マスタID');
            $table->datetime('arrangement_datetime')->comment('手配日時');
            $table->date('desired_delivery_date')->comment('お届け希望日');
            $table->tinyInteger('status')->comment('ステータス');
            $table->tinyInteger('shipping_status')->comment('出荷ステータス');
            $table->date('order_deadline')->nullable()->comment('発注期日');
            $table->date('shipping_date')->nullable()->comment('出荷日');
            $table->text('note')->nullable()->comment('手配会社への伝達事項');
            $table->text('memo')->nullable()->comment('作業メモ');
            $table->boolean('is_apply_complete')->default(false)->comment('申し込み済フラグ');
            $table->string('sender_name')->nullable()->comment('送り主氏名');
            $table->string('sender_phone_number')->nullable()->comment('送り主電話番号');
            $table->string('slip_title')->nullable()->comment('伝票件名');
            $table->string('sender_postal_code')->nullable()->comment('送り主郵便番号');
            $table->string('sender_prefecture')->nullable()->comment('送り主都道府県');
            $table->string('sender_city')->nullable()->comment('送り主市区町村');
            $table->string('sender_address1')->nullable()->comment('送り主丁目・番地');
            $table->string('sender_address2')->nullable()->comment('送り主建物名・部屋番号');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            // $table->foreign('shipping_company_id')->references('id')->on('shipping_companies');
            $table->foreign('staff_id')->references('id')->on('staffs');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('gift_arrangement_histories');
    }
};
