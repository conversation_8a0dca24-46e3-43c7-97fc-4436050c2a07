<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('specification_product_infos', function (Blueprint $table) {
            $table->comment('規格別商品発注手配用情報');
            $table->uuid('id')->primary();
            $table->uuid('m_specification_product_id')->comment('規格別商品情報ID');
            $table->string('ordering_number')->comment('発注手配用品番');
            $table->string('ordering_name')->comment('発注手配用品名');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の追加
            $table->foreign('m_specification_product_id', 'fk_m_specification_product1')
                ->references('id')
                ->on('m_specification_products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('specification_product_infos');
    }
};
