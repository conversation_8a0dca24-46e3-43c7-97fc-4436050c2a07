<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guest_event_answers', function (Blueprint $table) {
            $table->string('attendance')->nullable()->comment('出欠')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guest_event_answers', function (Blueprint $table) {
            $table->string('attendance')->nullable()->default(0)->comment('出欠')->change();
        });
    }
};
