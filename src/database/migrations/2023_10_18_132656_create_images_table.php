<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('images', function (Blueprint $table) {
            $table->comment('画像管理');
            $table->uuid('uuid')->primary();
            $table->unsignedTinyInteger('owner_type')->nullable()->comment('オーナー種類');
            $table->uuid('owner_id')->nullable()->comment('オーナーID');
            $table->unsignedTinyInteger('file_type')->nullable()->comment('ファイル種別');
            $table->string('name')->nullable()->comment('ユーザーファイル名');
            $table->string('extension_type')->nullable()->comment('ファイル種類');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('images');
    }
};
