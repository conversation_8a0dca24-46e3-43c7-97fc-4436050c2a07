<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->date('prepayment_due_date')->nullable()->comment("事前支払い締め日")->after("scheduled_transfer_date");
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->dropColumn('prepayment_due_date');
        });
    }
};
