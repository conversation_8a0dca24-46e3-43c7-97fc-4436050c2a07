<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_variation_masters', function (Blueprint $table) {
            $table->comment('商品バリエーションマスタ');
            $table->uuid('id')->primary();
            $table->string('name')->comment('バリエーション名');
            $table->tinyInteger('ui_type')->comment('表示UI種別');
            $table->integer('additional_price')->comment('追加価格');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_variation_masters');
    }
};
