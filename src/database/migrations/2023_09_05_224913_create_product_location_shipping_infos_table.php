<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_location_shipping_infos', function (Blueprint $table) {
            $table->comment('商品拠点別配送情報');
            $table->uuid('id')->primary();
            $table->uuid('product_id')->comment('商品マスタID');
            // $table->unsignedBigInteger('vendor_location_id')->comment('取引業者拠点ID(FK)');
            // $table->unsignedBigInteger('delivery_lead_time_id')->comment('配送リードタイムマスタID(FK)');
            // $table->unsignedBigInteger('delivery_time_id')->comment('配送時間マスタID(FK)');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('product_id')
                ->references('id')
                ->on('products');

            // $table->foreign('vendor_location_id')
            //     ->references('id')
            //     ->on('vendor_locations');

            // $table->foreign('delivery_lead_time_id')
            //     ->references('id')
            //     ->on('delivery_lead_time_masters');


            // $table->foreign('delivery_time_id')
            //     ->references('id')
            //     ->on('delivery_time_masters');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_location_shipping_infos');
    }
};
