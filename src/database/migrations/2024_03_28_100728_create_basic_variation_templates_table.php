<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('basic_variation_templates', function (Blueprint $table) {
            $table->comment('基本バリエーションテンプレート');
            $table->uuid('id')->primary();
            $table->string('name')->nullable()->comment('テンプレート名称');
            $table->unsignedInteger('width')->nullable()->default(0)->comment('サイズ(幅)');
            $table->unsignedInteger('height')->nullable()->default(0)->comment('サイズ(高さ)');
            $table->string('unit')->nullable()->comment('サイズ(単位)');
            $table->boolean('color_extension')->nullable()->default(false)->comment('色延ばし有無');
            $table->unsignedTinyInteger('fold_line_type')->nullable()->default(0)->comment('折り線種類');
            $table->boolean('watermark')->nullable()->default(false)->comment('ウォーターマーク有無');
            $table->json('json_data')->nullable()->comment('JSONデータ');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('basic_variation_templates');
    }
};
