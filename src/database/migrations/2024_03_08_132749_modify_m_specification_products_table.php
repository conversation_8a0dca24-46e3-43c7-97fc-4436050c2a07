<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('m_specification_products', function (Blueprint $table) {
            $table->string('item_number')->nullable()->comment('品番')->change();
            $table->integer('regular_price')->nullable()->default(0)->comment('通常価格')->change();
            $table->integer('sale_price')->nullable()->default(0)->comment('販売価格')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('m_specification_products', function (Blueprint $table) {
            $table->string('item_number')->comment('品番')->change();
            $table->integer('regular_price')->comment('通常価格')->change();
            $table->integer('sale_price')->comment('販売価格')->change();
        });
    }
};
