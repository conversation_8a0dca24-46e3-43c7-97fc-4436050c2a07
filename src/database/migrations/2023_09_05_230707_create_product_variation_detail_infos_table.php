<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_variation_detail_infos', function (Blueprint $table) {
            $table->comment('商品バリエーション明細情報');
            $table->uuid('id')->primary();
            $table->uuid('product_variation_info_id')->comment('商品バリエーション情報ID');
            $table->uuid('product_variation_detail_id')->comment('商品バリエーション明細ID');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            $table->foreign('product_variation_info_id', 'fk_product_variation_info')
                ->references('id')
                ->on('product_variation_infos');

            $table->foreign('product_variation_detail_id', 'fk_product_variation_detail')
                ->references('id')
                ->on('product_variation_details');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_variation_detail_infos');
    }
};
