<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('m_web_invitations', function (Blueprint $table) {
            $table->comment('WEB招待状マスタ');
            $table->uuid('id')->primary();
            $table->uuid('m_specification_product_id')->comment('規格別商品マスタID');
            $table->uuid('m_web_invitation_template_id')->comment('WEB招待状共通テンプレートマスタID');
            $table->uuid('first_view_id')->nullable()->comment('選択ファーストビューID (FK)');
            $table->boolean('is_main_visual_image_disabled')->default(false)->comment('メインビジュアル画像利用不可FLG');
            $table->boolean('is_profile_image_disabled')->default(false)->comment('プロフィール画像利用不可FLG');
            $table->boolean('is_main_visual_image_replaceable')->default(false)->comment('メインビジュアル画像差し替え可能FLG');
            $table->text('css_code')->nullable()->comment('CSSコード');
            $table->json('editor_settings_json')->nullable()->comment('エディタ設定値JSON');
            $table->json('block_settings_json')->nullable()->comment('ブロック設定値JSON');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('m_specification_product_id')->references('id')->on('m_specification_products');
            $table->foreign('m_web_invitation_template_id')->references('id')->on('m_web_invitation_templates');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitations', function (Blueprint $table) {
            $table->dropForeign(['m_web_invitation_id']);
        });
        Schema::dropIfExists('m_web_invitations');
    }
};
