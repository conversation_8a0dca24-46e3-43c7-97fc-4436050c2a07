<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_specification_masters', function (Blueprint $table) {
            $table->comment('商品規格マスタ');
            $table->uuid('id')->primary();
            $table->string('name')->comment('規格名');
            $table->tinyInteger('ui_type')->comment('表示UI種別');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('product_specification_masters');
    }
};
