<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('money_transfers', function (Blueprint $table) {
            $table->boolean('is_error')->default(false)->comment('送金エラーフラグ')->after('status');
            $table->decimal('member_system_fee', 10, 2)->nullable()->default(0)->comment('会員システム料')->after('system_fee');
            $table->decimal('guest_system_fee', 10, 2)->nullable()->default(0)->comment('ゲストシステム料')->after('member_system_fee');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('money_transfers', function (Blueprint $table) {
            $table->dropColumn(['guest_system_fee', 'member_system_fee', 'is_error']);
        });
    }
};
