<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Step 1: 既存のユニーク制約を一時的に削除（存在する場合）
        try {
            Schema::table('members', function (Blueprint $table) {
                $table->dropUnique(['member_number']);
            });
        } catch (\Exception $e) {
            // ユニーク制約が存在しない場合は無視
        }

        // Step 2: Laravelスキーマビルダーでの変更
        // - Laravelのマイグレーション履歴を正しく更新
        // - スキーマ定義の整合性を保つ
        // - ただし、autoIncrementが完全に削除されない場合がある
        Schema::table('members', function (Blueprint $table) {
            $table->unsignedBigInteger('member_number')->nullable()->comment('会員番号')->change();
        });

        // Step 3: 直接SQLでの確実な削除とユニーク制約の再追加
        // - データベースレベルでautoIncrementを完全に削除
        // - Laravelスキーマビルダーだけでは不十分な場合の保険
        // - MySQLのAUTO_INCREMENT属性を確実に除去
        DB::statement('ALTER TABLE members MODIFY COLUMN member_number BIGINT UNSIGNED NULL UNIQUE COMMENT "会員番号"');
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Step 1: 既存のユニーク制約を削除（存在する場合）
        try {
            DB::statement('ALTER TABLE members DROP INDEX members_member_number_unique');
        } catch (\Exception $e) {
            // ユニーク制約が存在しない場合は無視
        }

        // Step 2: ロールバック時はautoIncrementを復元
        Schema::table('members', function (Blueprint $table) {
            $table->unsignedBigInteger('member_number')->autoIncrement()->unique()->comment('会員番号')->change();
        });

        // Step 3: 現在の最大値を取得してAUTO_INCREMENTを設定
        $maxMemberNumber = DB::table('members')->max('member_number');
        $nextAutoIncrement = $maxMemberNumber ? $maxMemberNumber + 1 : 100;

        DB::statement("ALTER TABLE members AUTO_INCREMENT = {$nextAutoIncrement}");
    }
};
