<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('option_product_group_masters', function (Blueprint $table) {
            $table->comment('オプション商品グループ');
            $table->uuid('id')->primary();
            $table->string('name')->comment('オプショングループ名称');
            $table->tinyInteger('selection_ui')->comment('選択UI');
            $table->boolean('is_selection')->default(false)->comment('選択必須FLG');
            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('option_product_group_masters');
    }
};
