<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('member_bank_accounts', function (Blueprint $table) {
            $table->string('phone')->nullable()->after('account_number')->comment('電話番号');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('member_bank_accounts', function (Blueprint $table) {
            $table->dropColumn('phone');
        });
    }
};
