<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('payment_history_details', function (Blueprint $table) {
            $table->comment('決済履歴明細');
            $table->uuid('id')->primary();
            $table->uuid('payment_history_id')->comment('決済履歴ID');
            $table->uuid('product_id')->comment('商品ID');
            $table->string('product_name')->comment('商品名');
            $table->unsignedInteger('unit_price')->comment('商品単価');
            $table->unsignedInteger('quantity')->comment('数量');
            $table->boolean('is_cancel')->default(false)->comment('キャンセルフラグ');
            $table->timestamp('cancel_date')->nullable()->comment('キャンセル日時');
            $table->softDeletes();
            $table->timestamps();
            $table->foreign('payment_history_id')->references('id')->on('payment_histories');
            $table->foreign('product_id')->references('id')->on('products');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('payment_history_details');
    }
};
