<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('member_bank_accounts', function (Blueprint $table) {
            $table->comment('会員銀行口座');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->string('bank_code', 4)->nullable()->comment('銀行コード');
            $table->string('bank_name')->nullable()->comment('銀行名');
            $table->string('branch_code', 3)->nullable()->comment('支店コード');
            $table->string('branch_name')->nullable()->comment('支店名');
            $table->unsignedTinyInteger('account_type')->nullable()->comment('口座種別');
            $table->string('account_name', 30)->nullable()->comment('口座名義');
            $table->unsignedInteger('account_number')->nullable()->comment('口座番号');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('member_bank_accounts');
    }
};
