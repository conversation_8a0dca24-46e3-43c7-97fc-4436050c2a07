<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->dropColumn('attendance');
            $table->uuid('web_invitation_id')->nullable()->comment('ユーザー作成WEB招待状')->after('parent_guest_id');
            $table->uuid('m_web_invitation_id')->nullable()->comment('WEB招待状マスタ')->after('web_invitation_id');
            $table->unsignedInteger('gift_amount')->nullable()->default(0)->comment('お気持ち金額')->after('is_member_confirmed');

            //カラムの外部キー制約追加
            $table->foreign('web_invitation_id')->references('id')->on('web_invitations');
            $table->foreign('m_web_invitation_id')->references('id')->on('m_web_invitations');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('guests', function (Blueprint $table) {
            $table->tinyInteger('attendance')->nullable()->comment('出欠')->after('gender');
            $table->dropForeign(['web_invitation_id']);
            $table->dropForeign(['m_web_invitation_id']);
            $table->dropColumn('web_invitation_id');
            $table->dropColumn('m_web_invitation_id');
            $table->dropColumn('gift_amount');
        });
    }
};
