<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        DB::statement("ALTER TABLE guests MODIFY COLUMN media_type TINYINT UNSIGNED NULL COMMENT 'お祝い画像・動画の種類 (画像か動画かのフラグ)'");
        Schema::table('guests', function (Blueprint $table) {
            $table->uuid('image_url')->nullable()->comment('プロフィール画像(UUID)')->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
    }
};
