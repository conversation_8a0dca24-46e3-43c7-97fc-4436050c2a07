<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('products', function (Blueprint $table) {
            $table->comment('商品マスタ');

            $table->uuid('id')->primary();
            $table->string('name')->comment('商品名');
            $table->uuid('counter_word_id')->nullable()->comment('助数詞マスタID');
            $table->uuid('unit_id')->nullable()->comment('数量単位マスタID');
            $table->uuid('max_sales_quantity')->nullable()->comment('最大販売数');
            $table->uuid('tax_rate_id')->nullable()->comment('税率マスタID');
            // $table->uuid('point_value', 10, 2)->comment('ポイント付与数値');
            // $table->unsignedBigInteger('point_unit_id')->comment('ポイント付与単位マスタID');
            // $table->unsignedBigInteger('manufacturer_id')->comment('メーカーID');
            // $table->unsignedBigInteger('packaging_group_id')->comment('同梱手配グループマスタID');
            // $table->unsignedBigInteger('delivery_group_id')->comment('納期指定グループマスタID');
            $table->uuid('product_category_combination_id')->nullable()->comment('商品カテゴリ組み合わせID');
            $table->integer('shipping_cost')->default(0)->nullable()->comment('送料');
            $table->integer('estimated_delivery_days')->default(0)->nullable()->comment('発送目安日数');
            $table->boolean('is_sample_request_allowed')->default(false)->nullable()->comment('サンプル請求可能FLG');
            $table->boolean('is_use_component')->default(false)->nullable()->comment('構成部材利用FLG(falseの場合は部材)');
            $table->boolean('is_sales_period_specified')->default(false)->nullable()->comment('販売期間指定ありFLG');
            $table->dateTime('sales_period_start')->nullable()->comment('販売期間(FROM日時)');
            $table->dateTime('sales_period_end')->nullable()->comment('販売期間(TO日時)');
            $table->boolean('is_reservation_period_specified')->default(false)->nullable()->comment('販売予約期間指定ありFLG');
            $table->dateTime('reservation_period_start')->nullable()->comment('販売予約期間(FROM日時)');
            $table->dateTime('reservation_period_end')->nullable()->comment('販売予約期間(TO日時)');
            $table->integer('product_inventory')->default(0)->nullable()->comment('商品在庫数');
            $table->text('admin_notes')->nullable()->comment('管理用備考');
            $table->text('product_description')->nullable()->comment('商品説明');
            $table->boolean('is_editor')->default(false)->nullable()->comment('エディタありFLG');
            $table->boolean('is_specification')->default(false)->nullable()->comment('規格指定ありFLG');
            $table->boolean('is_variation_specification_')->default(false)->nullable()->comment('バリエーション指定ありFLG');
            $table->string('product_type_code')->nullable()->comment('商品種別コード');
            $table->integer('option_product_price_difference')->default(0)->nullable()->comment('オプション商品差分価格');
            $table->boolean('is_unpublished')->default(false)->nullable()->comment('非公開FLG');
            $table->string('meta_title')->nullable()->comment('meta_title');
            $table->string('meta_description')->nullable()->comment('meta_description');
            $table->string('meta_canonical')->nullable()->comment('meta_canonical');
            $table->string('meta_keywords')->nullable()->comment('meta_keywords');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約
            // $table->foreign('counter_word_id')->references('id')->on('counter_words');
            // $table->foreign('unit_id')->references('id')->on('units');
            // $table->foreign('tax_rate_id')->references('id')->on('tax_rates');
            // $table->foreign('point_unit_id')->references('id')->on('point_units');
            // $table->foreign('packaging_group_id')->references('id')->on('packaging_groups');
            // $table->foreign('delivery_group_id')->references('id')->on('delivery_groups');
            // $table->foreign('product_category_combination_id')->references('id')->on('product_category_combinations');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('products');
    }
};
