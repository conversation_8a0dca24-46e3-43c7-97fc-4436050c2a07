<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('example_sentences', function (Blueprint $table) {
            $table->string('title')->nullable()->comment("タイトル")->after('id');
            $table->string('display_order')->nullable()->comment("表示順")->after('example_text');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('example_sentences', function (Blueprint $table) {
            $table->dropColumn(['title', 'display_order']);
        });
    }
};
