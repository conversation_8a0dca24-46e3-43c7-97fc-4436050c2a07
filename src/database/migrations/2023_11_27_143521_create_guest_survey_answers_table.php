<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('guest_survey_answers', function (Blueprint $table) {
            $table->comment('ゲストアンケート回答');
            $table->uuid('id')->primary();
            $table->uuid('member_id')->comment('会員ID');
            $table->uuid('guest_id')->comment('ゲストID');
            $table->unsignedTinyInteger('ui_type')->nullable()->default(0)->comment('UI種類(1:ラジオボタン,2:チェックボックス,3:テキスト)');
            $table->text('answer_content')->nullable()->comment('回答内容');
            $table->softDeletes();
            $table->timestamps();

            // 外部キー制約の定義
            $table->foreign('member_id')->references('id')->on('members');
            $table->foreign('guest_id')->references('id')->on('guests');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('guest_survey_answers');
    }
};
