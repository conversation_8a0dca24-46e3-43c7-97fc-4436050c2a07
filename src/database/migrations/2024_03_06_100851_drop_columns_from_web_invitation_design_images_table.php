<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('web_invitation_design_images', function (Blueprint $table) {
            $table->dropColumn('upload_file_name');
            $table->dropColumn('file_format');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('web_invitation_design_images', function (Blueprint $table) {
            $table->string('upload_file_name')->nullable()->comment('画像ファイル名(アップロード時)')->after('m_web_invitation_id');
            $table->string('file_format')->nullable()->comment('画像ファイル形式')->after('file_name');
        });
    }
};
