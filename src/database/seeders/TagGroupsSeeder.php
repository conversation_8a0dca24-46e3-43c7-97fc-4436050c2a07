<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\TagGroup;
use Illuminate\Support\Str;

class TagGroupsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        TagGroup::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        DB::table('tag_groups')->insert([
            [
                'id'         => (string) Str::orderedUuid(),
                'name' => 'Web招待状テーマ',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
