<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Member;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;
use Illuminate\Support\Str;

class MemberSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Member::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'id'         => (string) Str::orderedUuid(),
                'last_name' => "姓1",
                'first_name' => "名1",
                'last_name_kana' => "セイ",
                'first_name_kana' => "メイ",
                'last_name_romaji' => "last_romaji",
                'first_name_romaji' => "first_romaji",
                'email' => "member{$i}@member.com",
                'email_verified_at' => null,
                'password' => Hash::make('password'),
                'birthdate' => '1990-06-11',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('members')->insert($data);
    }
}
