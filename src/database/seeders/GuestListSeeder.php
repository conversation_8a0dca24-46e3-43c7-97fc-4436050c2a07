<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestList;
use App\Models\Member;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GuestListSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestList::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $member = Member::first();

        $data = [];
        for($i =1; $i <= 1; $i++){
            $data[] = [
                'id' => (string) Str::orderedUuid(),
                'member_id'  => $member->id,
                'name'       => 'ゲストリスト名'.$i,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('guest_lists')->insert($data);
    }
}
