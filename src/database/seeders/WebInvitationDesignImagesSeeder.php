<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\WebInvitationDesignImage;

class WebInvitationDesignImagesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        WebInvitationDesignImage::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        DB::table('web_invitation_design_images')->insert([
            [
                'm_web_invitation_id' => 1,
                'file_name' => 'image1.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'm_web_invitation_id' => 2,
                'file_name' => 'image2.png',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'm_web_invitation_id' => 3,
                'file_name' => 'image3.jpeg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
