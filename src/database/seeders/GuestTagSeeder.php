<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestTag;
use Illuminate\Support\Facades\DB;

class GuestTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestTag::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'member_id'     => $i,
                'guest_list_id' => $i,
                'tag'           => 'タグ'.$i,
                'created_at'    => now(),
                'updated_at'    => now(),
            ];
        }
        DB::table('guest_tags')->insert($data);
    }
}
