<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestProfileIconMaster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GuestProfileMasterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestProfileIconMaster::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'id' => (string) Str::orderedUuid(),
                'photo_url'  => 'image'.$i,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('guest_profile_icon_masters')->insert($data);
    }
}
