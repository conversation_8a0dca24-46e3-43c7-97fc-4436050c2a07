<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\WeddingInfo;

class WeddingInfosTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        WeddingInfo::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $weddingInfos = [
            [
                'member_id' => 1,
                'wedding_date' => '2023-11-20',
                'wedding_venue' => 'ホテルA',
                'reception_date' => '2023-11-20',
                'guest_count' => 100,
            ],
            [
                'member_id' => 2,
                'wedding_date' => '2023-12-05',
                'wedding_venue' => 'ホテルB',
                'reception_date' => '2023-12-05',
                'guest_count' => 80,
            ],
            [
                'member_id' => 3,
                'wedding_date' => '2024-01-15',
                'wedding_venue' => 'ホテルC',
                'reception_date' => '2024-01-15',
                'guest_count' => 120,
            ],
        ];

        WeddingInfo::insert($weddingInfos);
    }
}
