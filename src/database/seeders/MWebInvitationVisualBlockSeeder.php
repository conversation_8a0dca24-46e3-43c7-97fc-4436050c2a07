<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\MWebInvitationVisualBlock;
use App\Models\MWebInvitationTemplate;
use Illuminate\Support\Str;


class MWebInvitationVisualBlockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MWebInvitationVisualBlock::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [
            [
                'id'         => (string) Str::orderedUuid(),
                'logical_name' => 'Logical Name 1',
                'physical_name' => 'Physical Name 1',
                'image_file_name' => 'image1.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'logical_name' => 'Logical Name 2',
                'physical_name' => 'Physical Name 2',
                'image_file_name' => 'image2.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'logical_name' => 'Logical Name 3',
                'physical_name' => 'Physical Name 3',
                'image_file_name' => 'image3.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        MWebInvitationVisualBlock::insert($data);
    }
}
