<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\MemberRegistQuestionnaire;

class MemberRegistQuestionnairesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MemberRegistQuestionnaire::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $questionnaires = [
            [
                'member_id' => 1,
                'question' => '結婚式の予定はありますか？',
                'answer' => '来年の夏を予定しています。',
            ],
            [
                'member_id' => 2,
                'question' => '結婚式の予定はありますか？',
                'answer' => 'まだ未定です。',
            ],
            [
                'member_id' => 3,
                'question' => '結婚式の予定はありますか？',
                'answer' => '2年後の冬を考えています。',
            ],
        ];
        MemberRegistQuestionnaire::insert($questionnaires);

    }
}
