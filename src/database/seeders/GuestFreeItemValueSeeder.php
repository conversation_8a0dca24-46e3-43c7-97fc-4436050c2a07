<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestFreeItemValue;
use Illuminate\Support\Facades\DB;

class GuestFreeItemValueSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestFreeItemValue::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'member_id'     => $i,
                'guest_id' => $i,
                'name'          => '項目名称'.$i,
                'content'       => '内容'.$i,
                'created_at'    => now(),
                'updated_at'    => now(),
            ];
        }
        DB::table('guest_free_item_values')->insert($data);
    }
}
