<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestTagGuest;
use Illuminate\Support\Facades\DB;

class GuestTagGuestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestTagGuest::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'guest_tag_id' => $i,
                'guest_id'     => $i,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('guest_tag_guests')->insert($data);
    }
}
