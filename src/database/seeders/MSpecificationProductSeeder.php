<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Product;
use App\Models\MSpecificationProduct;
use Illuminate\Support\Str;


class MSpecificationProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MSpecificationProduct::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $product = Product::withoutGlobalScopes()->first();

        // 規格別商品データの配列を作成
        $specificationProducts = [
            [
                'id'         => (string) Str::orderedUuid(),
                'product_id' => $product->id,  // 商品の ID を指定
                'product_specification_detail_info_id1' => NULL,  // 規格明細ID1 の ID を指定
                'product_specification_detail_info_id2' => NULL,  // 規格明細ID2 の ID を指定
                'item_number' => 'ITEM001',
                'regular_price' => 1000,
                'sale_price' => 800,
                'sale_price_start' => '2023-10-01 00:00:00',
                'sale_price_end' => '2023-10-15 23:59:59',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'product_id' => 1,
            //     'product_specification_detail_info_id1' => NULL,
            //     'product_specification_detail_info_id2' => NULL,
            //     'item_number' => 'ITEM002',
            //     'regular_price' => 1200,
            //     'sale_price' => 950,
            //     'sale_price_start' => '2023-10-16 00:00:00',
            //     'sale_price_end' => '2023-10-30 23:59:59',
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // [
            //     'product_id' => 2,
            //     'product_specification_detail_info_id1' => NULL,
            //     'product_specification_detail_info_id2' => NULL,
            //     'item_number' => 'ITEM003',
            //     'regular_price' => 1500,
            //     'sale_price' => 1200,
            //     'sale_price_start' => '2023-10-01 00:00:00',
            //     'sale_price_end' => '2023-10-15 23:59:59',
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // 他の規格別商品データを追加
        ];

        // データベースにデータを挿入
        DB::table('m_specification_products')->insert($specificationProducts);
    }
}
