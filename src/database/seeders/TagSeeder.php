<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Tag;
use App\Models\TagGroup;
use Illuminate\Support\Str;


class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Tag::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $tagGroup = TagGroup::first();
        // タグデータの配列を作成
        $tags = [
            [
                'id'         => (string) Str::orderedUuid(),
                'tag_group_id' => $tagGroup->id,
                'name' => 'ナチュラル',
                'image_url' => 'タグ1の画像URL',
                'is_display' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'tag_group_id' => $tagGroup->id,
                'name' => 'シック',
                'image_url' => 'タグ2の画像URL',
                'is_display' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'tag_group_id' => $tagGroup->id,
                'name' => 'モダン',
                'image_url' => 'タグ3の画像URL',
                'is_display' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 他のタグデータを追加
        ];

        // データベースにデータを挿入
        DB::table('tags')->insert($tags);
    }
}
