<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestHonorMaster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GuestHonorMasterSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestHonorMaster::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'id' => (string) Str::orderedUuid(),
                'name'       => 'ゲスト敬称'.$i,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('guest_honor_masters')->insert($data);
    }
}
