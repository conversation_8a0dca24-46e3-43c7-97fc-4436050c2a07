<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Models\GuestHonorMaster;
use App\Models\GuestTitleMaster;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        //管理人
        $this->call(AdminSeeder::class);

        //会員情報
        $this->call(MemberSeeder::class);
        // $this->call(WeddingInfosTableSeeder::class);
        // $this->call(MemberRegistQuestionnairesTableSeeder::class);


        //商品マスタ
        $this->call(TagGroupsSeeder::class);
        $this->call(TagSeeder::class);
        $this->call(ProductSeeder::class);
        // $this->call(ProductTagSeeder::class);
        $this->call(MSpecificationProductSeeder::class);

        //商品マスタ(Web招待状)
        $this->call(MWebInvitationTemplateSeeder::class);
        $this->call(MWebInvitationVisualBlockSeeder::class);
        $this->call(WebInvitationTemplateBlockSeeder::class);
        $this->call(MWebInvitationSeeder::class);
        // $this->call(WebInvitationDesignImagesSeeder::class);

        //ゲスト関連
        $this->call(GuestProfileMasterSeeder::class);
        $this->call(GuestHonorMasterSeeder::class);
        $this->call(GuestTitleMasterSeeder::class);
        $this->call(GuestListSeeder::class);
        $this->call(WebInvitationsTableSeeder::class);
        // $this->call(GuestGroupSeeder::class);
        // $this->call(GuestTagSeeder::class);
        // $this->call(GuestSeeder::class);
        // $this->call(GuestFreeItemValueSeeder::class);
        // $this->call(GuestTagGuestSeeder::class);
        // $this->call(GuestEventAnswerSeeder::class);
        // $this->call(GuestSurveyAnswerSeeder::class);

        // //送金管理
        // $this->call(MoneyTransferSeeder::class);

        // //文例集
        $this->call(ExampleSentencesTableSeeder::class);

        // //エディタ
        // $this->call(MasterBlocksTableSeeder::class);
    }
}
