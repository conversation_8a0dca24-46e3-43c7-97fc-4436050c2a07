<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Enums\ProductTypeEnum;
use Illuminate\Support\Facades\DB;
use App\Models\ExampleSentence;
use Illuminate\Support\Str;


class ExampleSentencesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        ExampleSentence::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 12; $i++){
            $data[] = [
                'id'         => (string) Str::orderedUuid(),
                'product_type' => ProductTypeEnum::getValues()[array_rand(ProductTypeEnum::getValues())],
                'example_type' => 1,
                'example_text' => "例文".$i,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('example_sentences')->insert($data);
    }
}
