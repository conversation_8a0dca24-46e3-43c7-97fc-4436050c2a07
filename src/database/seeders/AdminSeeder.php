<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Admin;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Admin::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'id'         => (string) Str::orderedUuid(),
                'name'       => '管理者'.$i,
                'email'      => "admin{$i}@admin.com",
                'email_verified_at' => null,
                'password'   => Hash::make('password'),
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('admins')->insert($data);
    }
}
