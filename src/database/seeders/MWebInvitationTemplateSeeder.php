<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\MWebInvitationTemplate;
use Illuminate\Support\Str;

class MWebInvitationTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MWebInvitationTemplate::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // WEB招待状共通テンプレートデータの配列を作成
        $templates = [
            [
                'id'         => (string) Str::orderedUuid(),
                'name' => 'テンプレート1',
                'description' => 'テンプレート1の説明',
                'image' => 'path/to/template1.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'name' => 'テンプレート2',
                'description' => 'テンプレート2の説明',
                'image' => 'path/to/template2.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'name' => 'テンプレート3',
                'description' => 'テンプレート3の説明',
                'image' => 'path/to/template3.jpg',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 他のテンプレートデータを追加
        ];

        // データベースにデータを挿入
        DB::table('m_web_invitation_templates')->insert($templates);
    }
}
