<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\MWebInvitation;
use App\Models\MSpecificationProduct;
use App\Models\MWebInvitationTemplate;
use App\Models\MWebInvitationVisualBlock;
use Illuminate\Support\Str;

class MWebInvitationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MWebInvitation::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $mSpecificationProduct = MSpecificationProduct::first();
        $mWebInvitationTemplate = MWebInvitationTemplate::first();
        $mWebInvitationVisualBlock = MWebInvitationVisualBlock::first();
         // WEB招待状マスタのデータの配列を作成
        $webInvitations = [
            [
                'id' => (string) Str::orderedUuid(),
                'm_specification_product_id' => $mSpecificationProduct->id, // 規格別商品マスタIDを設定
                'm_web_invitation_template_id' => $mWebInvitationTemplate->id, // WEB招待状共通テンプレートマスタIDを設定
                // 'first_view_id' => 1, // 選択ファーストビューIDを設定
                'm_web_invitation_visual_block_id' => $mWebInvitationVisualBlock->id,
                'is_main_visual_image_disabled' => false,
                'is_profile_image_disabled' => true,
                'is_main_visual_image_replaceable' => true,
                'css_code' => '{
                    "key": "value"
                }',
                'editor_settings_json' => '{
                    "key": "value"
                }', // JSONデータを設定
                'image_aspect_settings_json' => '{
                    "key": "value"
                }', // JSONデータを設定
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'm_specification_product_id' => 2, // 別の商品の規格マスタIDを設定
            //     'm_web_invitation_template_id' => 2, // 別のテンプレートマスタIDを設定
            //     'first_view_id' => 2, // 別の選択ファーストビューIDを設定
            //     'm_web_invitation_visual_block_id' => 1,
            //     'is_main_visual_image_disabled' => true,
            //     'is_profile_image_disabled' => false,
            //     'is_main_visual_image_replaceable' => false,
            //     'css_code' => '{
            //         "key": "value"
            //     }',
            //     'editor_settings_json' => '{
            //         "key": "value"
            //     }', // JSONデータを設定
            //     'image_aspect_settings_json' => '{
            //         "key": "value"
            //     }', // JSONデータを設定
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // [
            //     'm_specification_product_id' => 3, // 別の商品の規格マスタIDを設定
            //     'm_web_invitation_template_id' => 1, // 別のテンプレートマスタIDを設定
            //     'm_web_invitation_visual_block_id' => 1,
            //     'first_view_id' => 3, // 別の選択ファーストビューIDを設定
            //     'is_main_visual_image_disabled' => false,
            //     'is_profile_image_disabled' => false,
            //     'is_main_visual_image_replaceable' => true,
            //     'css_code' => '{
            //         "key": "value"
            //     }',
            //     'editor_settings_json' => '{
            //         "key": "value"
            //     }', // JSONデータを設定
            //     'image_aspect_settings_json' => '{
            //         "key": "value"
            //     }', // JSONデータを設定
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
        ];

        // データベースにデータを挿入
        DB::table('m_web_invitations')->insert($webInvitations);
    }
}
