<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestSurveyAnswer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GuestSurveyAnswerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestSurveyAnswer::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for ($i = 0; $i < 3; $i++) {
            $data[] = [
                'member_id' => 1,
                'guest_id' => 1,
                'ui_type' => rand(1, 3),
                'question' => '質問内容' . $i,
                'answer_content' => '回答内容' . $i,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('guest_survey_answers')->insert($data);
    }
}
