<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\MemberBankAccount;
use Faker\Factory as Faker;
use Illuminate\Support\Facades\DB;
use App\Enums\AccountTypeEnum;

class MemberBankAccountSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 外部キー制約を一時的に無効にする
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // テーブルをトランケートする
        MemberBankAccount::truncate();

        // 外部キー制約を再度有効にする
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $faker = Faker::create();

        $data = [];
        for ($i = 1; $i <= 3; $i++) {
            $data[] = [
                'member_id' => $i,
                'bank_code' => $faker->numberBetween(1000, 9999),
                'bank_name' => '銀行'.$i,
                'branch_code' => $faker->numberBetween(100, 999),
                'branch_name' => '支店'. $i,
                'account_type' => AccountTypeEnum::Normal,
                'account_name' => 'アカウント',
                'account_number' => $faker->numberBetween(1000000, 9999999),
                'phone' => '***********',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // データを挿入する
        MemberBankAccount::insert($data);
    }
}
