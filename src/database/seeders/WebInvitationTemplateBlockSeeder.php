<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\WebInvitationTemplateBlock;
use App\Models\MWebInvitationTemplate;
use Illuminate\Support\Str;

class WebInvitationTemplateBlockSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        WebInvitationTemplateBlock::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        // WEB招待状共通テンプレートブロックデータの配列を作成

        $mWebInvitationTemplate = MWebInvitationTemplate::first();
        $blocks = [
            [
                'id'         => (string) Str::orderedUuid(),
                'm_web_invitation_template_id' => $mWebInvitationTemplate->id,
                'block_name' => 'ブロック1',
                'vue_component_name' => 'BlockComponent1',
                'display_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'm_web_invitation_template_id' => $mWebInvitationTemplate->id,
                'block_name' => 'ブロック2',
                'vue_component_name' => 'BlockComponent2',
                'display_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id'         => (string) Str::orderedUuid(),
                'm_web_invitation_template_id' => $mWebInvitationTemplate->id,
                'block_name' => 'ブロック3',
                'vue_component_name' => 'BlockComponent3',
                'display_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // データベースにデータを挿入
        DB::table('web_invitation_template_blocks')->insert($blocks);
    }
}
