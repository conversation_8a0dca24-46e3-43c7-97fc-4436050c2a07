<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestEventAnswer;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class GuestEventAnswerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestEventAnswer::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for ($i = 0; $i < 3; $i++) {
            if($i == 0){
                $name = "挙式";
            }
            if($i == 1){
                $name = "披露宴";
            }
            if($i == 2){
                $name = "1.5次会";
            }
            $data[] = [
                'member_id' => 1,
                'guest_id' => 1,
                'name' => $name,
                'date' => now()->addDays($i),
                'payment_amount' => random_int(1000, 1000),
                'attendance' => '出席',
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        DB::table('guest_event_answers')->insert($data);
    }
}
