<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\MoneyTransfer;
use App\Enums\AccountTypeEnum;

class MoneyTransferSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MoneyTransfer::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        for ($i = 1; $i <= 3; $i++) {
            DB::table('money_transfers')->insert([
                'member_id' => $i, // 会員ID
                'web_invitation_id' => $i, // Web招待状ID
                'admin_id' => $i, // 担当者ID
                'status' => 0, // 送金ステータス
                'deadline_date' => now(), // 送金期限
                'prepayment_amount' => 100.00 + $i, // 事前支払金額
                'system_fee' => 10.00 + $i, // システム使用料
                'commission_fee' => 5.00 + $i, // 手数料
                'transfer_amount' => 1000.00 * $i, // 送金金額
                'transfer_date' => now(), // 送金予約日時
                'completion_datetime' => now(), // 送金完了日時
                'bank_code' => '1234', // 銀行コード
                'bank_name' => '銀行名', // 銀行名
                'branch_code' => '567', // 支店コード
                'branch_name' => '支店名', // 支店名
                'account_type' => AccountTypeEnum::Normal, // 口座種別
                'account_name' => 'John Doe ' . $i, // 口座名義
                'account_number' => "0123456", // 口座番号
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
}
