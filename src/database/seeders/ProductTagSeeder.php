<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\ProductTag;

class ProductTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        ProductTag::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // 商品とタグの関連データの配列を作成
        $productTags = [
            [
                'product_id' => 1,  // 商品の ID を指定
                'tag_id' => 1,      // タグの ID を指定
                'tag_type' => 3,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'product_id' => 1,
                'tag_id' => 2,
                'tag_type' => 3,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'product_id' => 1,
                'tag_id' => 3,
                'tag_type' => 3,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // 他の商品とタグの関連データを追加
        ];

        // データベースにデータを挿入
        DB::table('product_tags')->insert($productTags);
    }
}
