<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Seeder;
use App\Models\MasterBlock;

class MasterBlocksTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        MasterBlock::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // サンプルデータを用意
        $data = [
            [
                'name' => 'Block A',
                'product_type' => 1,
                'json_data' => json_encode([
                    'color' => 'red',
                    'size' => 'large',
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Block B',
                'product_type' => 2,
                'json_data' => json_encode([
                    'color' => 'blue',
                    'size' => 'medium',
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Block C',
                'product_type' => 3,
                'json_data' => json_encode([
                    'color' => 'green',
                    'size' => 'small',
                ]),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // データを一括で挿入
        DB::table('master_blocks')->insert($data);
    }
}
