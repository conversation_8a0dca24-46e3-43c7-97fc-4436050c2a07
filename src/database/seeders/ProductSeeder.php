<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Product;
use Illuminate\Support\Str;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Product::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        DB::table('products')->insert([
            [
                'id'         => (string) Str::orderedUuid(),
                'name' => '商品1',
                'counter_word_id' => 1,
                'unit_id' => 1,
                'max_sales_quantity' => 100,
                'tax_rate_id' => 1,
                'product_category_combination_id' => 1,
                'shipping_cost' => 500,
                'estimated_delivery_days' => 3,
                'is_sample_request_allowed' => true,
                'is_use_component' => false,
                'is_sales_period_specified' => true,
                'sales_period_start' => '2023-10-15 00:00:00',
                'sales_period_end' => '2023-10-30 23:59:59',
                'product_inventory' => 50,
                'product_description' => '商品1の説明文',
                'is_editor' => true,
                'is_specification' => true,
                'is_variation_specification_' => true,
                'product_type_code' => 'P001',
                'option_product_price_difference' => 10,
                'is_unpublished' => false,
                'meta_title' => '商品1のタイトル',
                'meta_description' => '商品1のメタ説明',
                'meta_canonical' => '商品1のカノニカル',
                'meta_keywords' => '商品1, サンプル, キーワード',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            // [
            //     'name' => '商品2',
            //     'counter_word_id' => 2,
            //     'unit_id' => 2,
            //     'max_sales_quantity' => 200,
            //     'tax_rate_id' => 2,
            //     'product_category_combination_id' => 2,
            //     'shipping_cost' => 700,
            //     'estimated_delivery_days' => 4,
            //     'is_sample_request_allowed' => false,
            //     'is_use_component' => true,
            //     'is_sales_period_specified' => false,
            //     'sales_period_start' => null,
            //     'sales_period_end' => null,
            //     'product_inventory' => 75,
            //     'product_description' => '商品2の説明文',
            //     'is_editor' => false,
            //     'is_specification' => false,
            //     'is_variation_specification_' => false,
            //     'product_type_code' => 'P002',
            //     'option_product_price_difference' => 15,
            //     'is_unpublished' => true,
            //     'meta_title' => '商品2のタイトル',
            //     'meta_description' => '商品2のメタ説明',
            //     'meta_canonical' => '商品2のカノニカル',
            //     'meta_keywords' => '商品2, サンプル, キーワード',
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
            // [
            //     'name' => '商品3',
            //     'counter_word_id' => 3,
            //     'unit_id' => 3,
            //     'max_sales_quantity' => 300,
            //     'tax_rate_id' => 3,
            //     'product_category_combination_id' => 3,
            //     'shipping_cost' => 900,
            //     'estimated_delivery_days' => 5,
            //     'is_sample_request_allowed' => true,
            //     'is_use_component' => true,
            //     'is_sales_period_specified' => true,
            //     'sales_period_start' => '2023-11-01 00:00:00',
            //     'sales_period_end' => '2023-11-15 23:59:59',
            //     'product_inventory' => 100,
            //     'product_description' => '商品3の説明文',
            //     'is_editor' => true,
            //     'is_specification' => true,
            //     'is_variation_specification_' => true,
            //     'product_type_code' => 'P003',
            //     'option_product_price_difference' => 20,
            //     'is_unpublished' => false,
            //     'meta_title' => '商品3のタイトル',
            //     'meta_description' => '商品3のメタ説明',
            //     'meta_canonical' => '商品3のカノニカル',
            //     'meta_keywords' => '商品3, サンプル, キーワード',
            //     'created_at' => now(),
            //     'updated_at' => now(),
            // ],
        ]);
    }
}
