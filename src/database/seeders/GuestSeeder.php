<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Guest;
use Illuminate\Support\Facades\DB;
use App\Enums\PaymentMethodEnum;
use App\Enums\MemberConfirmTypeEnum;

class GuestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Guest::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // システムの利用料負担FLGはランダムに設定
        $isSystemFee = rand(0, 1);

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'guest_list_id' => $i,
                'member_id'     => rand(1, 3),
                'parent_guest_id' => null,
                'last_name' => '姓',
                'first_name' => '名',
                'last_name_kana' => 'セイカナ',
                'first_name_kana' => 'メイカナ',
                'last_name_romaji' => 'sei_romaji',
                'first_name_romaji' => 'kana_romaji',
                'gender' => 1,
                'allergy' => '卵アレルギー',
                'birthdate' => '1990-06-11',
                'image_url' => '',
                'postal_code' => '650-0001',
                'prefecture' => '兵庫県',
                'city' => '神戸市中央区',
                'address' => '1-1-1',
                'building' => '建物名',
                'phone' => '08011112222',
                'email' => "test{$i}@test.com",
                'message' => 'メッセージ',
                'invitation_delivery' => 1,
                'guest_title' => 'ゲスト肩書1',
                'guest_honor' => 'ゲスト敬称1',
                'web_invitation_id' => $i,
                'member_confirm_type' => MemberConfirmTypeEnum::New,
                'payment_method' => PaymentMethodEnum::ADVANCE_PAYMENT,
                'is_system_fee' => $isSystemFee,
                'system_fee' => $isSystemFee ? rand(100, 1000) : null,
                'system_fee_rate' => $isSystemFee ? 0.03 : null,
                'total_amount' => rand(500, 5000),
                'settlement_amount' => rand(500, 5000),
                'card_settlement_id' => $isSystemFee ? 'CARD_' . rand(1000, 9999) : null,
                'created_at'    => now(),
                'updated_at'    => now(),
            ];
        }
        DB::table('guests')->insert($data);
    }
}
