<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\GuestGroup;
use Illuminate\Support\Facades\DB;

class GuestGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        GuestGroup::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $data = [];
        for($i =1; $i <= 3; $i++){
            $data[] = [
                'guest_list_id' => $i,
                'member_id'     => $i,
                'name'          => 'ゲストグループ名'.$i,
                'created_at'    => now(),
                'updated_at'    => now(),
            ];
        }
        DB::table('guest_groups')->insert($data);
    }
}
