<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\WebInvitation;
use App\Models\MWebInvitation;
use App\Models\Member;
use App\Models\GuestList;
use Carbon\Carbon;
use Illuminate\Support\Str;

class WebInvitationsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        WebInvitation::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        $mWebInvitation = MWebInvitation::first();
        $member = Member::first();
        $guestList = GuestList::first();

        for ($i = 1; $i <= 1; $i++) {
            $invitations[] = [
                'id' => (string) Str::orderedUuid(),
                'member_id' => $member->id,
                'm_web_invitation_id' => $mWebInvitation->id,
                'guest_list_id' => $guestList->id,
                'name' => 'Sample Invitation ' . $i,
                'public_url' => 'https://example.com/sample-invitation-' . $i,
                'password' => Hash::make('password' . $i),
                'is_password' => ($i % 2 == 0),
                'is_public' => true,
                'editor_settings' => $this->getEditorSettings(),
                // 'editor_settings' => json_encode(['key' => 'value']),
                'block_settings' => json_encode(['key' => 'value']),
                'scheduled_transfer_date' => Carbon::now(),
            ];
        }

        // データベースに挿入
        DB::table('web_invitations')->insert($invitations);
    }

    private function getEditorSettings()
    {
        return '{
            "blocks": [
                {
                    "name": "メインビジュアル",
                    "id": "mainVisual",
                    "contents": {
                    "selectVisual": "images",
                    "images": [
                        {
                        "src": "",
                        "width": "",
                        "height": "",
                        "x": "",
                        "y": ""
                        },
                        {
                        "src": "",
                        "width": "",
                        "height": "",
                        "x": "",
                        "y": ""
                        },
                        {
                        "src": "",
                        "width": "",
                        "height": "",
                        "x": "",
                        "y": ""
                        }
                    ],
                    "movie": "/src/",
                    "groomName": "新郎の名前",
                    "brideName": "新婦の名前"
                    }
                },
                {
                    "name": "カウントダウン",
                    "id": "countDown",
                    "visible": true,
                    "contents": {
                    "date": null
                    }
                },
                {
                    "name": "挨拶・メッセージ",
                    "id": "message",
                    "visible": true,
                    "contents": {
                    "isShowVisual": true,
                    "selectVisual": "images",
                    "images": [
                        "/src/",
                        "/src/",
                        "/src/"
                    ],
                    "movie": "/src/",
                    "textAlign": "left",
                    "message": ""
                    }
                },
                {
                    "name": "新郎・新婦プロフィール",
                    "id": "profile",
                    "visible": true,
                    "contents": [
                    {
                        "title": "新郎プロフィール",
                        "isShowVisual": true,
                        "selectVisual": "images",
                        "images": [
                        "/src/",
                        "/src/",
                        "/src/"
                        ],
                        "movie": "/src/",
                        "name": "",
                        "isShowRole": true,
                        "role": "",
                        "textAlign": "left",
                        "message": ""
                    },
                    {
                        "title": "新婦プロフィール",
                        "isShowVisual": true,
                        "selectVisual": "images",
                        "images": [
                        "/src/",
                        "/src/",
                        "/src/"
                        ],
                        "movie": "/src/",
                        "name": "",
                        "isShowRole": true,
                        "role": "",
                        "textAlign": "left",
                        "message": ""
                    },
                    {
                        "title": "家族プロフィール1",
                        "isShowVisual": true,
                        "selectVisual": "images",
                        "images": [
                        "/src/",
                        "/src/",
                        "/src/"
                        ],
                        "movie": "/src/",
                        "name": "",
                        "isShowRole": true,
                        "role": "",
                        "textAlign": "left",
                        "message": ""
                    }
                    ]
                },
                {
                    "name": "写真ギャラリー",
                    "id": "gallery",
                    "visible": true,
                    "contents": [
                    "",
                    "",
                    ""
                    ]
                },
                {
                    "name": "パーティー情報",
                    "id": "information",
                    "visible": true,
                    "contents": {
                        "date": "2023/11/22",
                        "type": "ceremony_reception_separate_venue",
                        "events": [
                            {
                                "isOtherParty": false,
                                "eventName": "テストイベント",
                                "plans": [
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    },
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    },
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    }
                                ],
                                "venue": "",
                                "zip": "",
                                "address": "",
                                "isShowMaps": false,
                                "tel": "",
                                "url": "",
                                "feeOption": "gift_system",
                                "feeAmount": [""],
                                "isShowText": false,
                                "text": ""
                            },
                            {
                                "isOtherParty": false,
                                "eventName": "1.5次会",
                                "plans": [
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    },
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    },
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    }
                                ],
                                "venue": "",
                                "zip": "",
                                "address": "",
                                "isShowMaps": false,
                                "tel": "",
                                "url": "",
                                "feeOption": "gift_system",
                                "feeAmount": [""],
                                "isShowText": false,
                                "text": ""
                            },
                            {
                                "isOtherParty": true,
                                "eventName": "追加パーティ2",
                                "plans": [
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    },
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    },
                                    {
                                        "isShowPlan": false,
                                        "hour": "",
                                        "minute": ""
                                    }
                                ],
                                "venue": "",
                                "zip": "",
                                "address": "",
                                "isShowMaps": false,
                                "tel": "",
                                "url": "",
                                "feeOption": "gift_system",
                                "feeAmount": [""],
                                "isShowText": false,
                                "text": ""
                            }
                        ]
                    }
                },
                {
                    "name": "会費・ご祝儀の事前支払い",
                    "id": "gift",
                    "visible": true,
                    "contents": {
                    "isUseGift": true,
                    "isUseInVenue": true,
                    "isUseInAfterParty": true,
                    "message": "",
                    "textAlign": "",
                    "usageFee": "guest",
                    "scheduleDate": ""
                    }
                },
                {
                    "name": "フリー項目",
                    "id": "freeField",
                    "visible": true,
                    "contents": [
                    {
                        "title": "フリー項目1",
                        "heading": "見出し",
                        "textAlign": "left",
                        "message": "",
                        "isShowVisual": true,
                        "selectVisual": "images",
                        "images": [
                        "/src/",
                        "/src/",
                        "/src/"
                        ],
                        "movie": "/src/"
                    },
                    {
                        "title": "フリー項目2",
                        "heading": "見出し",
                        "textAlign": "left",
                        "message": "",
                        "isShowVisual": true,
                        "selectVisual": "images",
                        "images": [
                        "/src/",
                        "/src/",
                        "/src/"
                        ],
                        "movie": "/src/"
                    }
                    ]
                },
                {
                    "name": "出欠フォーム",
                    "id": "guestAnswer",
                    "visible": true,
                    "contents": {
                    "selectList": [
                        {
                        "title": "新郎新婦ゲスト選択",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": false
                        },
                        {
                        "title": "お名前",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "お名前（ふりがな）",
                        "id": "",
                        "disabled": false,
                        "required": false,
                        "visible": true
                        },
                        {
                        "title": "お名前（ローマ字）",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "間柄",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "関係性",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "プロフィール写真",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "性別",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "生年月日",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "住所",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "電話番号",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "メールアドレス",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "アレルギー項目の入力",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "連絡入力",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "お祝いメッセージ",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        },
                        {
                        "title": "お祝い画像",
                        "id": "",
                        "disabled": false,
                        "required": true,
                        "visible": true
                        }
                    ],
                    "questionnaire": [
                        {
                        "title": "アンケート項目1",
                        "heading": "見出し",
                        "textAlign": "left",
                        "message": "",
                        "method": "",
                        "answer": [
                            "",
                            "",
                            ""
                        ]
                        },
                        {
                        "title": "アンケート項目2",
                        "heading": "見出し",
                        "textAlign": "left",
                        "message": "",
                        "method": "",
                        "answer": [
                            "",
                            "",
                            ""
                        ]
                        }
                    ],
                    "limit": {
                        "setting": 1,
                        "textAlign": "left",
                        "message": ""
                    },
                    "attendance": {
                        "isHideAttendance": true,
                        "isHideSkip": true,
                        "isAddFields": true,
                        "fields": [
                        ""
                        ]
                    }
                    }
                }
                ]
            }';
    }
}
