{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-imagick": "^3.8", "bensampo/laravel-enum": "^6.6", "doctrine/dbal": "^3.7", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^3.11", "laravel/framework": "^9.19", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.11", "laravel/tinker": "^2.7", "league/flysystem-aws-s3-v3": "~3.0", "maatwebsite/excel": "^3.1", "mll-lab/laravel-graphql-playground": "^2.6", "nuwave/lighthouse": "^6.12", "pbmedia/laravel-ffmpeg": "*", "socialiteproviders/line": "^4.1"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "nunomaduro/larastan": "^2.0", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0", "timeweb/phpstan-enum": "^3.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "stable", "prefer-stable": true}