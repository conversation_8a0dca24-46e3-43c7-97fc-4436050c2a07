APP_NAME=stg8695.favori.wedding
APP_ENV=production
APP_KEY=base64:6NyVWMe4EmpcZQNpzpd+qJRBkWyTxqTRHu3SsBqK5eE=
APP_DEBUG=false
APP_URL=https://stg8695.favori.wedding

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
# LOG_LEVEL=debug # メモ: この設定にすると画像が表示されなくなる。imagesクエリの結果の presigned_url が nullになる
LOG_LEVEL=error # 2024/6/5

DB_CONNECTION=mysql
DB_HOST=favori.cty0m8akkspg.ap-northeast-1.rds.amazonaws.com
DB_PORT=3306
DB_DATABASE=favori_stg
DB_USERNAME=favori_admin
DB_PASSWORD=pLd1qBiK7L7Fsbtr

# DBコピー先
DB_COPY_TARGET_CONNECTION=mysql
DB_COPY_TARGET_HOST=favori.cty0m8akkspg.ap-northeast-1.rds.amazonaws.com
DB_COPY_TARGET_PORT=3306
DB_COPY_TARGET_DATABASE=favori
DB_COPY_TARGET_USERNAME=favori_admin
DB_COPY_TARGET_PASSWORD=pLd1qBiK7L7Fsbtr

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
SESSION_DRIVER=file
SESSION_LIFETIME=120
MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# QueueWorker
QUEUE_CONNECTION=database

# AWS
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=qyjrVOtDO4aga8dqHuZ16W9t502sW9B8uP//zFPE
AWS_DEFAULT_REGION=ap-northeast-1

# メール設定
MAIL_MAILER=ses
MAIL_FROM_NAME="stg8695.favori.wedding"
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_CONTACT_ADDRESS="<EMAIL>" # お問い合わせメール管理者 2024/7/12
MAIL_BCC_ADDRESS="<EMAIL>" # BCCメールアドレス 2024/8/13
MAIL_FRONT_URL_RESET="https://stg8695.favori.wedding/login/password"
MAIL_FRONT_URL_TMP_MEMBER="https://stg8695.favori.wedding/register/check"
MAIL_FRONT_URL_WEB_INVITATION="https://stg8695.favori.wedding/wi/"
FROM_MAIL_DEVELOPER_ADDRESS=<EMAIL> # 開発者メールアドレス 2025/05/08
TO_MAIL_DEVELOPER_ADDRESS=<EMAIL>,<EMAIL> #開発者のメールアドレス 2025/05/08
MAIL_EXTERNAL_BROWSER_PARAM="?openExternalBrowser=1"

# エラーメール送信
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="rlhb ghdl nsxr dkah"
MAIL_ENCRYPTION=tls

# AWS S3
AWS_BUCKET=favori-private-stg #バケット名
AWS_USE_PATH_STYLE_ENDPOINT=false #minio true S3 false
AWS_URL_MEMBER_EXPIRATION=10800 #会員ディレクト以下の有効期限 (2024/5/28 3時間に変更)
AWS_URL_ADMIN_EXPIRATION=1440 #管理人ディレクトリ以下の有効期限
AWS_DIR_ADMIN="admin/"
AWS_DIR_MEMBER="member/"
AWS_DIR_GUEST="guest/"
AWS_DIR_WI="wi/"
AWS_URL=https://stg8695.favori.wedding
AWS_BUCKET_WI=favori-public-stg
AWS_WI_URL=https://stg8695.favori.wedding
AWS_URL_MAX_AGE=86400

# AWS画像コピー先
AWS_COPY_TARGET_BUCKET=favori-private #バケット名
AWS_COPY_TARGET_BUCKET_WI=favori-public #バケット名
AWS_COPY_TARGET_URL=https://favori.wedding #URL

# SNSログイン
SNS_LOGIN_URL=https://stg8695.favori.wedding/login
SNS_REGISTER_URL=https://stg8695.favori.wedding/register

# LINEログイン(本番)
LINE_CLIENT_ID=2005874657
LINE_CLIENT_SECRET=799b7c396664f1a7e0b3cb93009dab52
LINE_CALLBACK_URL=https://stg8695.favori.wedding/api/auth/line/callback
LINE_APP_STATE=favori
LINE_BOT_PROMPT=aggressive

# Googleログイン(本番)
GOOGLE_CLIENT_ID=196444450019-t9p7idk37k2knhbumjhi6bkd0c6ubiqk.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=https://stg8695.favori.wedding/api/auth/google/callback

# 楽天決済
RAKUTEN_PAYMENT_API_ENDPOINT=https://payment-stg.global.rakuten.com/gp/Payment/
RAKUTEN_PAYMENT_SERVICE_ID=stg-all-webportal
RAKUTEN_PAYMENT_SECRET_KEY=455FD3D78FD5E295C6D3DCD7AFC8D81484CC30BE0DDFA9A1D997C739039024DD

# GraphQL Playground
GRAPHQL_PLAYGROUND_ENABLED=false

# GMO Payment
GMO_PG_API_ENDPOINT=https://pt01.mul-pay.jp/
GMO_PG_SHOP_ID=tshop00024104
GMO_PG_SHOP_PASS=35yzuzh6

# 動画関連
FFMPEG_BINARIES=/usr/local/bin/ffmpeg
FFPROBE_BINARIES=/usr/local/bin/ffprobe

# APIキー
API_KEY=b2Hy8mF4qL9p

# cloundFrontの環境変数
AWS_CLOUDFRONT_KEY_PAIR_ID=KACBLCCVPKTK5
AWS_CLOUDFRONT_PRIVATE_KEY_PATH=app/secrets/cloudfront/private_key.pem
AWS_CLOUDFRONT_RESOURCE_URL=https://stg8695.favori.wedding/member/
AWS_CLOUDFRONT_DOWNLOAD_URL=https://stg8695.favori.wedding
AWS_CLOUDFRONT_COOKIE_DOMAIN=.favori.wedding
AWS_CLOUDFRONT_COOKIE_SECURE=true
CLOUDFRONT_COOKIE_EXPIRY=86400

# MediaConvert アクセスロール
AWS_MEDIACONVERT_DECORATOR_ROLE='arn:aws:iam::613841076301:role/service-role/MediaConvert_Default_Role'

# MediaConvert ファイル名の名前装飾子
AWS_MEDIACONVERT_DECORATOR='_hls'
