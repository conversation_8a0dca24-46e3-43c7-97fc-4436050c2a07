# Getting Start

[こちらを見てください](./docs/GETTINGSTART.md)

# 資料

- [テーブル定義](./docs/schema)
  - `yarn run schema:doc`で生成できます。

## ローカルの各URL

- playground(http://localhost/graphql-playground)
- mailpit(http://localhost:8025)
- phpmyadmin(http://localhost:4040)
- minio(http://localhost:9001)

# 開発方針

## ブランチ戦略（例）

（例）

- 開発ブランチ(develop)
  - 機能開発ブランチ(feature/xxxx)
- メインブランチ(main)
- 本番環境ブランチ(production)

## 開発の流れ（例）

1. develop ブランチから featue/<xxx>の名前でブランチを作成して、実装を行ってください。
   1. <xxx>の部分は自由でいいですが、対応する backlog タスクがある場合は、タスク ID を入れるとわかりやすいです。
2. 実装が終わったら、ブランチを push して、PullRequest を作成して下さい。
   1. 作業中のプルリクエストを上げている場合は、WIP などのラベルを付けてください。
3. lint と test が通っていることを確認して、レビュー依頼を出してください。
   1. プルリクエストの reviewer にレビュー担当者をいれて、ラベルでレビュー依頼をつけてください。
   2. TODO: 誰に？
4. レビューワーは、問題がなければ、Approve してマージしてください。
5. マージしたことを実装者に伝えて、実装者は、開発環境で動作確認を行ってください。
