{"name": "favori-backend", "version": "1.0.0", "repository": "**************:nscreates/favori-backend.git", "scripts": {"start": "docker-compose up", "stop": "docker-compose down", "clearall": "docker-compose down -v", "lint": "npm-run-all -s lint:format lint:analyze", "lint:analyze": "docker-compose run api vendor/bin/phpstan analyse --memory-limit=2G", "lint:format": "prettier ./src/app -c", "lintfix": "npm-run-all -s lintfix:format lintfix:analyze", "lintfix:analyze": "docker-compose run api vendor/bin/phpstan analyse --memory-limit=2G", "lintfix:format": "prettier ./src/app --write", "schema:doc": "docker-compose run schemaspy /entrypoint.sh", "test": "docker-compose run api php artisan test", "bash": "docker-compose run api bash", "artisan": "docker-compose run api php artisan", "composer": "docker-compose run api php composer", "prettier": "prettier"}, "devDependencies": {"@prettier/plugin-php": "^0.19.6", "npm-run-all": "^4.1.5", "prettier": "^2.0.0"}}