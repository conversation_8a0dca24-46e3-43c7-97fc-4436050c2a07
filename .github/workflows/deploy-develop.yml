name: deploy favori-backend

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  EC2_SECURITY_GROUP_ID: sg-0396f923aba83ad06
  EC2_HOST_USER: *************
  EC2_HOST_ADMIN: dev8695-admin.favori.wedding

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Generate ssh key
        run: echo "$SSH_SECRET_KEY" > ${{ runner.temp }}/key && chmod 600 ${{ runner.temp }}/key
        env:
          SSH_SECRET_KEY: ${{ secrets.SSH_SECRET_KEY }}
          
      # IPアドレスを取得
      - name: Check Public IP
        id: ip
        uses: haythem/public-ip@v1.3

      # AWS CLIをインストールする
      - name: Install AWS CLI
        run: |
          curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
          unzip awscliv2.zip
          sudo ./aws/install --update
          aws --version

      # AWS CLIにキーを設定をする
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-northeast-1

      # SSHのセキュリティグループを開放する
      - name: Open security group
        run: aws ec2 authorize-security-group-ingress --group-id ${{ env.EC2_SECURITY_GROUP_ID }} --protocol tcp --port 22 --cidr ${{ steps.ip.outputs.ipv4 }}/32

      # backend のデプロイ (user)
      - name: Deploy backend (user)
        run: ssh -i ${{ runner.temp }}/key -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no ec2-user@${{ env.EC2_HOST_USER }} "/bin/bash /var/www/backend/deploy.sh"

      # backend のデプロイ (admin)
      - name: Deploy backend (admin)
        run: ssh -i ${{ runner.temp }}/key -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no ec2-user@${{ env.EC2_HOST_ADMIN }} "/bin/bash /var/www/backend/deploy.sh"

      # SSHのセキュリティグループを閉じる
      - name: Close security group
        run: aws ec2 revoke-security-group-ingress --group-id ${{ env.EC2_SECURITY_GROUP_ID }} --protocol tcp --port 22 --cidr ${{ steps.ip.outputs.ipv4 }}/32