name: CodeCheck
on:
  push:
    branches:
      - develop
  pull_request:
    types: [opened, synchronize]
    # paths:
    #   - "src/**"
jobs:
  lint__api:
    name: Api Php Lint And Unit Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: ["16.20.0"]
    # defaults:
    #   run:
    #     working-directory: src
    services:
      mysql:
        image: mysql:5.7
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - uses: actions/checkout@v2

      - name: Set up Node version
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}

      - name: Yarn Install
        run: yarn install --frozen-lockfile

      - name: Format
        run: yarn lint:format

      - name: Setup PHP with PECL extension
        uses: shivammathur/setup-php@v2
        with:
          php-version: "8.2"
          extensions: mbstring, simplexml, curl, fileinfo, tokenizer, dom

      - name: cache vendor
        id: cache
        uses: actions/cache@v1
        with:
          ref: main
          path: ./vendor
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-

      # for migration
      - name: composer install --no-scripts
        if: steps.cache.outputs.cache-hit != 'true'
        run: composer install
        working-directory: src

      - name: Lint
        run: vendor/bin/phpstan analyse --memory-limit=2G
        working-directory: src

      # - name: Prepare Laravel Keygen
      # run: |
      #   cp .env.ci .env
      #   php artisan key:generate

      # - name: Prepare Laravel Migrate
      #   run: |
      #     php artisan migrate --force --env=testing

      # - name: Unit Test
      #   run: yarn run test:unit
