#!/bin/bash
cd /var/www/backend/src
git pull
if [ $? -ne 0 ]; then
    curl -X POST --data '{"text":"<!channel> develop自動デプロイ git pull失敗"}' *******************************************************************************
fi

composer install
php artisan migrate --force

# php artisan cache:clear でパーミッションエラーが発生する問題 仮対応
sudo chown -R ec2-user:nginx ./storage/framework/cache/data
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
# Lighthouse キャッシュクリア
php artisan lighthouse:clear-cache

# QueueWorkerの再起動
if systemctl list-units --type=service --all | grep -q "LaravelQueueWorker.service"; then
  sudo systemctl restart LaravelQueueWorker
  if [ $? -eq 0 ]; then
    sudo systemctl status LaravelQueueWorker
  else
    echo "Failed to restart LaravelQueueWorker"
  fi
fi